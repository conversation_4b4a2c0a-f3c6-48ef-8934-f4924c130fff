@echo off
echo 🚀 启动IndustryAI服务器...

REM 检查虚拟环境
if exist ".venv\Scripts\activate.bat" (
    echo ✅ 激活虚拟环境 (.venv)
    call .venv\Scripts\activate.bat
) else if exist "venv\Scripts\activate.bat" (
    echo ✅ 激活虚拟环境 (venv)
    call venv\Scripts\activate.bat
) else (
    echo ⚠️  未找到虚拟环境,使用系统Python
)

REM 设置 PYTHONPATH
set PYTHONPATH=%PYTHONPATH%;%CD%
echo 项目路径: %PYTHONPATH%

REM 设置工厂环境变量(如果未设置)
if not defined PLANT set PLANT=阳泉
echo PLANT: %PLANT%

REM 设置后台任务
set BACKGROUND_TASK=True
echo BACKGROUND_TASK: %BACKGROUND_TASK%

REM 启动服务器
echo 🌐 启动Web服务器...
python src\backend\main.py --host 0.0.0.0 --port 8999