import os
import sys
import time
from industrytslib.core import get_realtime_predictor
from industrytslib.utils.readconfig import read_config_toml

seq_pred_list =[
    "生料A磨孪生样本_生料A磨孪生样本_WaveForM",
    "生料B磨孪生样本_生料B磨孪生样本_WaveForM",
    "窑孪生样本_窑孪生样本_WaveForM",
    "煤磨孪生样本_煤磨孪生样本_WaveForM",
    "水泥A磨孪生样本_水泥A磨孪生样本_WaveForM",
    "水泥B磨孪生样本_水泥B磨孪生样本_WaveForM",
]

def main_sync():
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    config = read_config_toml("config/database_config.example.toml")

    predictor_list = []
    for project_name in seq_pred_list:
        predictor = get_realtime_predictor(project_name, config, "sequence")
        predictor_list.append(predictor)

    while True:
        for predictor in predictor_list:
            try:
                predictor.main()
            except Exception as e:
                print(e)
        time.sleep(60)


if __name__ == "__main__":
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    main_sync()
