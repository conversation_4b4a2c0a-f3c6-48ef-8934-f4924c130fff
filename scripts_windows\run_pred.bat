@echo off
echo 🔮 启动预测模式 (Windows)...

REM 检查虚拟环境
if exist ".venv\Scripts\activate.bat" (
    echo ✅ 激活虚拟环境 (.venv)
    call .venv\Scripts\activate.bat
) else if exist "venv\Scripts\activate.bat" (
    echo ✅ 激活虚拟环境 (venv)
    call venv\Scripts\activate.bat
) else (
    echo ⚠️  未找到虚拟环境,使用系统Python
)

REM 设置 PYTHONPATH
set PYTHONPATH=%PYTHONPATH%;%CD%
echo 项目路径: %PYTHONPATH%

REM 项目配置数组(Windows批处理中使用循环处理)
echo 开始预测任务...

REM 预测项目1
echo 请求实时预测项目: Bubble缺陷样本_Bubble缺陷样本_CNN
echo   - 间隔时间: 60秒
echo   - 预测器类型: classic_mo
python src\backend\requestes\requests_predictor.py --project-name "Bubble缺陷样本_Bubble缺陷样本_CNN" --interval "60" --predictor-type "classic_mo"
echo 请求实时预测项目: Bubble缺陷样本_Bubble缺陷样本_CNN 完成
echo ---

REM 预测项目2
echo 请求实时预测项目: Knot缺陷样本_Knot缺陷样本_CNN
echo   - 间隔时间: 60秒
echo   - 预测器类型: classic_mo
python src\backend\requestes\requests_predictor.py --project-name "Knot缺陷样本_Knot缺陷样本_CNN" --interval "60" --predictor-type "classic_mo"
echo 请求实时预测项目: Knot缺陷样本_Knot缺陷样本_CNN 完成
echo ---

REM 预测项目3
echo 请求实时预测项目: Stone缺陷样本_Stone缺陷样本_CNN
echo   - 间隔时间: 60秒
echo   - 预测器类型: classic_mo
python src\backend\requestes\requests_predictor.py --project-name "Stone缺陷样本_Stone缺陷样本_CNN" --interval "60" --predictor-type "classic_mo"
echo 请求实时预测项目: Stone缺陷样本_Stone缺陷样本_CNN 完成
echo ---

REM 预测项目4
echo 请求实时预测项目: PointDefect缺陷样本_PointDefect缺陷样本_CNN
echo   - 间隔时间: 60秒
echo   - 预测器类型: classic_mo
python src\backend\requestes\requests_predictor.py --project-name "PointDefect缺陷样本_PointDefect缺陷样本_CNN" --interval "60" --predictor-type "classic_mo"
echo 请求实时预测项目: PointDefect缺陷样本_PointDefect缺陷样本_CNN 完成
echo ---

echo ✅ 所有预测任务完成