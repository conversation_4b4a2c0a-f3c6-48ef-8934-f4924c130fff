
# 1. 项目架构概览

本部分提供 `src/backend` 的高层架构概览。

## 1.1. 技术栈

- **框架**: FastAPI
- **语言**: Python
- **异步任务调度**: APScheduler
- **命令行接口 (CLI)**: Typer
- **Web 服务器**: Uvicorn
- **核心依赖**: `industrytslib` (可能是一个内部库,用于处理时间序列、数据、模型训练等)

## 1.2. 架构风格

项目采用**模块化的单体 (Modular Monolith)** 架构。

- **FastAPI 应用 (`app.py`)** 作为核心入口点,负责应用的生命周期管理、中间件配置和路由注册。
- **功能按模块划分**,体现在 `routers` 目录中,每个文件(`train.py`, `realtime_predict.py`, `decision_making.py`)代表一个核心业务领域。
- **后台任务**通过 `APScheduler` 进行管理,与主应用解耦,实现了定时任务(如训练、预测)和周期性系统维护(如心跳检测)。
- **配置中心化**,通过 `config/` 目录下的 TOML 文件进行管理。

## 1.3. 核心组件职责

| 组件 | 主要职责 |
| :--- | :--- |
| **`app.py`** | - 初始化 FastAPI 应用实例。<br>- 管理应用生命周期(启动/关闭事件)。<br>- 初始化并管理 `APScheduler` 调度器。<br>- 注册核心路由 (`/train`, `/rt`, `/opt`)。<br>- 配置全局中间件和静态文件。 |
| **`main.py`** | - 提供基于 `Typer` 的命令行接口 (CLI)。<br>- 作为应用的入口点,使用 `uvicorn` 启动 FastAPI 服务器。 |
| **`routers/`** | - 定义 API 端点 (Endpoints)。<br>- 处理 HTTP 请求,调用相应的业务逻辑。<br>- 将业务逻辑与 Web 框架解耦。 |
| **`api/`** | - 定义 API 的请求/响应模型 (Pydantic Models)。<br>- 确保接口数据的类型安全和校验。 |
| **`utils/task_scheduler.py`** | - 创建和配置多个 `APScheduler` 实例,用于不同类型的任务(训练、预测、决策)。<br>- 为不同任务类型提供独立的线程池,避免资源竞争。 |
| **`background_tasks/`** | - 实现具体的后台任务逻辑,例如数据处理、系统监控等。 |
| **`industrytslib`** | - (推断) 提供核心业务逻辑的实现,如模型训练器、预测器、决策代理等。 |

## 1.4. Mermaid 架构图

```mermaid
graph TD
    subgraph "用户/客户端"
        Client[外部调用者/UI]
    end

    subgraph "Web 层 (FastAPI)"
        A[uvicorn] --> B(main.py - Typer CLI)
        B --> C{app.py - FastAPI App}
        C --> R1(routers/train.py)
        C --> R2(routers/realtime_predict.py)
        C --> R3(routers/decision_making.py)
        C --> Favicon(favicon.ico)
    end

    subgraph "业务逻辑层"
        R1 --> T(tasks/trainer.py)
        R2 --> P(tasks/predictor.py)
        R3 --> D(tasks/decision_agent.py)
    end

    subgraph "后台任务调度 (APScheduler)"
        C --> S(utils/task_scheduler.py)
        S --> ST(Train Scheduler)
        S --> SP(Predict Scheduler)
        S --> SD(Decision Scheduler)
        S --> SB(Background Scheduler)
        
        ST -- schedules --> T
        SP -- schedules --> P
        SD -- schedules --> D
        SB -- schedules --> BT(background_tasks)
    end

    subgraph "数据与配置"
        T --> DB[(Database)]
        P --> DB
        D --> DB
        BT --> DB
        C --> Config(config/*.toml)
    end

    Client -- HTTP/S --> A
```
