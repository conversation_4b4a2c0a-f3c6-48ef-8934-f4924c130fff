<template>
  <div class="predict">
    <div class="page-title">实时预测</div>
    
    <!-- 预测表单 -->
    <div class="card">
      <h3>启动预测任务</h3>
      <el-form 
        ref="predictFormRef" 
        :model="predictForm" 
        :rules="predictRules" 
        label-width="120px" 
        class="form-container"
      >
        <el-form-item label="项目名称" prop="project_name">
          <el-input 
            v-model="predictForm.project_name" 
            placeholder="请输入项目名称"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="预测器类型" prop="predictor_type">
          <el-select v-model="predictForm.predictor_type" placeholder="请选择预测器类型" style="width: 100%">
            <el-option label="基础预测器" value="basic" />
            <el-option label="经典预测器" value="classic" />
            <el-option label="经典多输出" value="classic_mo" />
            <el-option label="序列预测器" value="sequence" />
            <el-option label="质量预测器" value="quality" />
            <el-option label="序列软测量" value="sequence_soft" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="预测间隔" prop="interval">
          <el-input-number 
            v-model="predictForm.interval" 
            :min="1" 
            :max="3600" 
            placeholder="秒"
            style="width: 100%"
          />
          <div class="form-help">预测任务执行间隔时间(秒)</div>
        </el-form-item>
        
        <div class="button-group">
          <el-button type="primary" @click="startPredict" :loading="loading.predict">
            <el-icon><VideoPlay /></el-icon>
            启动预测
          </el-button>
          <el-button @click="resetForm">
            <el-icon><RefreshRight /></el-icon>
            重置表单
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 特殊预测任务 -->
    <div class="card">
      <h3>特殊预测任务</h3>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>序列实时预测</span>
            </template>
            <el-form label-width="100px">
              <el-form-item label="项目名称">
                <el-input v-model="seqPredictForm.project_name" placeholder="项目名称" />
              </el-form-item>
              <el-form-item label="预测间隔">
                <el-input-number v-model="seqPredictForm.interval" :min="1" style="width: 100%" />
              </el-form-item>
              <el-button type="success" @click="startSeqPredict" :loading="loading.seqPredict" style="width: 100%">
                启动序列预测
              </el-button>
            </el-form>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>序列软测量</span>
            </template>
            <el-form label-width="100px">
              <el-form-item label="项目名称">
                <el-input v-model="seqSoftForm.project_name" placeholder="项目名称" />
              </el-form-item>
              <el-form-item label="预测间隔">
                <el-input-number v-model="seqSoftForm.interval" :min="1" style="width: 100%" />
              </el-form-item>
              <el-button type="warning" @click="startSeqSoft" :loading="loading.seqSoft" style="width: 100%">
                启动软测量
              </el-button>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 预测控制 -->
    <div class="card">
      <h3>预测控制</h3>
      <el-form label-width="120px" class="form-container">
        <el-form-item label="停止指定预测">
          <div style="display: flex; gap: 10px; align-items: center;">
            <el-input 
              v-model="stopProjectName" 
              placeholder="请输入要停止的项目名称"
              style="flex: 1;"
            />
            <el-button type="warning" @click="stopPredict" :loading="loading.stopPredict">
              <el-icon><VideoPause /></el-icon>
              停止预测
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="停止所有预测">
          <el-button type="danger" @click="stopAllPredict" :loading="loading.stopAllPredict">
            <el-icon><VideoStop /></el-icon>
            停止所有预测
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 预测结果展示 -->
    <div class="card">
      <h3>预测结果监控</h3>
      <div class="toolbar">
        <el-button @click="loadPredictResults" :loading="loading.results">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-select v-model="selectedProject" placeholder="选择项目" style="width: 200px; margin-left: 10px;">
          <el-option label="全部项目" value="" />
          <el-option 
            v-for="project in projectList" 
            :key="project" 
            :label="project" 
            :value="project" 
          />
        </el-select>
      </div>
      
      <!-- 预测结果图表 -->
      <div class="chart-container" v-if="chartData.length > 0">
        <v-chart :option="chartOption" autoresize />
      </div>
      
      <div v-else class="no-data">
        <el-empty description="暂无预测数据" />
      </div>
    </div>

    <!-- 预测记录 -->
    <div class="card">
      <h3>预测记录</h3>
      <div class="toolbar">
        <el-button @click="loadPredictRecords" :loading="loading.records">
          <el-icon><Refresh /></el-icon>
          刷新记录
        </el-button>
        <el-button @click="clearPredictRecords" type="danger" plain>
          <el-icon><Delete /></el-icon>
          清空记录
        </el-button>
      </div>
      
      <el-table :data="predictRecords" style="width: 100%" v-loading="loading.records">
        <el-table-column prop="timestamp" label="时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="项目名称" width="150" />
        <el-table-column prop="predictor_type" label="预测器类型" width="150" />
        <el-table-column prop="interval" label="间隔(秒)" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
              {{ scope.row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="描述" />
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { predictAPI } from '../utils/api'
import db from '../utils/database'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const predictFormRef = ref()

const loading = reactive({
  predict: false,
  seqPredict: false,
  seqSoft: false,
  stopPredict: false,
  stopAllPredict: false,
  records: false,
  results: false
})

const predictForm = reactive({
  project_name: '',
  predictor_type: '',
  interval: 30
})

const seqPredictForm = reactive({
  project_name: '',
  interval: 30
})

const seqSoftForm = reactive({
  project_name: '',
  interval: 30
})

const stopProjectName = ref('')
const predictRecords = ref([])
const chartData = ref([])
const selectedProject = ref('')
const projectList = ref([])

// 表单验证规则
const predictRules = {
  project_name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' }
  ],
  predictor_type: [
    { required: true, message: '请选择预测器类型', trigger: 'change' }
  ],
  interval: [
    { required: true, message: '请输入预测间隔', trigger: 'blur' },
    { type: 'number', min: 1, max: 3600, message: '间隔时间应在1-3600秒之间', trigger: 'blur' }
  ]
}

// 图表配置
const chartOption = computed(() => {
  const filteredData = selectedProject.value 
    ? chartData.value.filter(item => item.project === selectedProject.value)
    : chartData.value
    
  return {
    title: {
      text: '预测结果趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      boundaryGap: false
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '预测值',
        type: 'line',
        data: filteredData.map(item => [item.timestamp, item.value]),
        smooth: true,
        lineStyle: {
          color: '#409EFF'
        }
      }
    ]
  }
})

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 启动预测
const startPredict = async () => {
  try {
    await predictFormRef.value.validate()
    loading.predict = true
    
    const response = await predictAPI.startPredict(predictForm)
    
    // 记录到本地数据库
    await db.addPredictRecord({
      projectName: predictForm.project_name,
      predictor_type: predictForm.predictor_type,
      interval: predictForm.interval,
      status: 'success',
      message: '预测任务启动成功'
    })
    
    ElMessage.success('预测任务启动成功')
    await loadPredictRecords()
    
  } catch (error) {
    console.error('启动预测失败:', error)
    
    // 记录失败信息
    await db.addPredictRecord({
      projectName: predictForm.project_name,
      predictor_type: predictForm.predictor_type,
      interval: predictForm.interval,
      status: 'failed',
      message: `预测任务启动失败: ${error.message}`
    })
    
  } finally {
    loading.predict = false
  }
}

// 启动序列预测
const startSeqPredict = async () => {
  if (!seqPredictForm.project_name.trim()) {
    ElMessage.warning('请输入项目名称')
    return
  }
  
  try {
    loading.seqPredict = true
    
    const response = await predictAPI.startSeqPredict({
      project_name: seqPredictForm.project_name,
      interval: seqPredictForm.interval
    })
    
    await db.addPredictRecord({
      projectName: seqPredictForm.project_name,
      predictor_type: 'sequence_realtime',
      interval: seqPredictForm.interval,
      status: 'success',
      message: '序列预测任务启动成功'
    })
    
    ElMessage.success('序列预测任务启动成功')
    await loadPredictRecords()
    
  } catch (error) {
    console.error('启动序列预测失败:', error)
  } finally {
    loading.seqPredict = false
  }
}

// 启动序列软测量
const startSeqSoft = async () => {
  if (!seqSoftForm.project_name.trim()) {
    ElMessage.warning('请输入项目名称')
    return
  }
  
  try {
    loading.seqSoft = true
    
    const response = await predictAPI.startSeqSoft({
      project_name: seqSoftForm.project_name,
      interval: seqSoftForm.interval
    })
    
    await db.addPredictRecord({
      projectName: seqSoftForm.project_name,
      predictor_type: 'sequence_soft',
      interval: seqSoftForm.interval,
      status: 'success',
      message: '序列软测量任务启动成功'
    })
    
    ElMessage.success('序列软测量任务启动成功')
    await loadPredictRecords()
    
  } catch (error) {
    console.error('启动序列软测量失败:', error)
  } finally {
    loading.seqSoft = false
  }
}

// 停止指定预测
const stopPredict = async () => {
  if (!stopProjectName.value.trim()) {
    ElMessage.warning('请输入要停止的项目名称')
    return
  }
  
  try {
    loading.stopPredict = true
    
    const response = await predictAPI.stopPredict({
      project_name: stopProjectName.value.trim()
    })
    
    await db.addPredictRecord({
      projectName: stopProjectName.value.trim(),
      status: 'success',
      message: '预测任务停止成功'
    })
    
    ElMessage.success('预测任务停止成功')
    stopProjectName.value = ''
    await loadPredictRecords()
    
  } catch (error) {
    console.error('停止预测失败:', error)
  } finally {
    loading.stopPredict = false
  }
}

// 停止所有预测
const stopAllPredict = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要停止所有预测任务吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.stopAllPredict = true
    
    const response = await predictAPI.stopAllPredict()
    
    await db.addPredictRecord({
      projectName: 'ALL',
      status: 'success',
      message: '所有预测任务停止成功'
    })
    
    ElMessage.success('所有预测任务停止成功')
    await loadPredictRecords()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止所有预测失败:', error)
    }
  } finally {
    loading.stopAllPredict = false
  }
}

// 重置表单
const resetForm = () => {
  predictFormRef.value.resetFields()
}

// 加载预测记录
const loadPredictRecords = async () => {
  try {
    loading.records = true
    predictRecords.value = await db.getPredictRecords(50)
    
    // 更新项目列表
    const projects = [...new Set(predictRecords.value.map(record => record.projectName))]
    projectList.value = projects.filter(project => project && project !== 'ALL')
    
  } catch (error) {
    console.error('加载预测记录失败:', error)
  } finally {
    loading.records = false
  }
}

// 加载预测结果(模拟数据)
const loadPredictResults = async () => {
  try {
    loading.results = true
    
    // 模拟预测结果数据
    const now = Date.now()
    const mockData = []
    
    for (let i = 0; i < 50; i++) {
      mockData.push({
        timestamp: now - (50 - i) * 60000, // 每分钟一个数据点
        value: Math.random() * 100 + 50 + Math.sin(i * 0.1) * 20,
        project: projectList.value[Math.floor(Math.random() * projectList.value.length)] || 'default'
      })
    }
    
    chartData.value = mockData
    
  } catch (error) {
    console.error('加载预测结果失败:', error)
  } finally {
    loading.results = false
  }
}

// 清空预测记录
const clearPredictRecords = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有预测记录吗？此操作不可恢复。',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await db.clearStore('predictRecords')
    await loadPredictRecords()
    ElMessage.success('预测记录已清空')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空预测记录失败:', error)
    }
  }
}

// 监听项目选择变化
watch(selectedProject, () => {
  // 图表会自动更新
})

onMounted(async () => {
  await loadPredictRecords()
  await loadPredictResults()
})
</script>

<style scoped>
.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #909399;
}

.chart-container {
  height: 400px;
  margin-top: 20px;
}
</style>