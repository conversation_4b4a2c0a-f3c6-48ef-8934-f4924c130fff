<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEditing ? '编辑算法配置' : '创建算法配置'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="配置名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入配置名称" />
      </el-form-item>
      
      <el-form-item label="算法类型" prop="algorithm_type">
        <el-select v-model="form.algorithm_type" placeholder="请选择算法类型" style="width: 100%">
          <el-option label="经典算法" value="classic" />
          <el-option label="经典算法(替代)" value="classic_alter" />
          <el-option label="序列算法" value="sequence" />
          <el-option label="时间序列经典" value="time_series_classic" />
          <el-option label="时间序列序列" value="time_series_sequence" />
          <el-option label="LSTM-KAN" value="lstm_kan" />
          <el-option label="Transformer" value="transformer" />
          <el-option label="Informer" value="informer" />
          <el-option label="TimesNet" value="timesnet" />
          <el-option label="PatchTST" value="patchtst" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入配置描述"
        />
      </el-form-item>
      
      <el-form-item label="参数配置">
        <div class="parameters-section">
          <div class="parameter-item" v-for="(value, key) in form.parameters" :key="key">
            <el-input
              v-model="parameterKeys[key]"
              placeholder="参数名"
              style="width: 40%"
              @blur="updateParameterKey(key, parameterKeys[key])"
            />
            <el-input
              v-model="form.parameters[key]"
              placeholder="参数值"
              style="width: 40%; margin-left: 10px"
            />
            <el-button
              type="danger"
              size="small"
              @click="removeParameter(key)"
              style="margin-left: 10px"
            >
              删除
            </el-button>
          </div>
          <el-button type="primary" size="small" @click="addParameter">
            添加参数
          </el-button>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEditing ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  config: {
    type: Object,
    default: () => ({})
  },
  isEditing: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'save'])

const formRef = ref()
const saving = ref(false)

const form = reactive({
  id: null,
  name: '',
  algorithm_type: '',
  description: '',
  parameters: {}
})

const parameterKeys = ref({})

const rules = {
  name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' }
  ],
  algorithm_type: [
    { required: true, message: '请选择算法类型', trigger: 'change' }
  ]
}

// 监听配置变化
watch(() => props.config, (newConfig) => {
  if (newConfig && Object.keys(newConfig).length > 0) {
    form.id = newConfig.id
    form.name = newConfig.name || ''
    form.algorithm_type = newConfig.algorithm_type || ''
    form.description = newConfig.description || ''
    form.parameters = { ...newConfig.parameters } || {}
    
    // 初始化参数键映射
    parameterKeys.value = {}
    Object.keys(form.parameters).forEach(key => {
      parameterKeys.value[key] = key
    })
  }
}, { immediate: true, deep: true })

// 监听对话框显示状态
watch(() => props.visible, (visible) => {
  if (visible && !props.isEditing) {
    // 新建时重置表单
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  form.id = null
  form.name = ''
  form.algorithm_type = ''
  form.description = ''
  form.parameters = {}
  parameterKeys.value = {}
  formRef.value?.clearValidate()
}

// 添加参数
const addParameter = () => {
  const key = `param_${Date.now()}`
  form.parameters[key] = ''
  parameterKeys.value[key] = ''
}

// 删除参数
const removeParameter = (key) => {
  delete form.parameters[key]
  delete parameterKeys.value[key]
}

// 更新参数键
const updateParameterKey = (oldKey, newKey) => {
  if (newKey && newKey !== oldKey) {
    const value = form.parameters[oldKey]
    delete form.parameters[oldKey]
    delete parameterKeys.value[oldKey]
    form.parameters[newKey] = value
    parameterKeys.value[newKey] = newKey
  }
}

// 处理保存
const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saving.value = true
    
    const configData = {
      id: form.id,
      name: form.name,
      algorithm_type: form.algorithm_type,
      description: form.description,
      parameters: { ...form.parameters }
    }
    
    emit('save', configData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.parameters-section {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.parameter-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.parameter-item:last-of-type {
  margin-bottom: 15px;
}

.dialog-footer {
  text-align: right;
}
</style>