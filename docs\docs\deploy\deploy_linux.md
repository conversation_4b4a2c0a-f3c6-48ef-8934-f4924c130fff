<!-- Ubuntu/CentOS Deploy -->
# 1.打包app

在最新的项目下面运行下面的命令:
```shell
bash scripts/zip.sh
```
运行完成后会在根目录生成一个industryai.tar.gz文件,然后将文件上传到现场服务器。

# 2.解压与安装

创建文件夹app,然后将项目文件解压到app文件夹:
```shell
mkdir app
tar -zxvf industryai.tar.gz -C app/
```

## 安装需要的python依赖
首先安装Anaconda并创建虚拟环境`exp`(`xtyh`,环境名称根据项目需求设置),然后进入虚拟环境:
```shell
conda create -n exp python=3.11
conda activate exp
```
然后安装项目依赖(需要网络):
```shell
pip install -r requirements.txt
```
离线安装核心库`industrytslib`:
```shell
pip install industrytslib-*-py3-none-any.whl --no-deps --force
```

# 3.环境配置
## 数据库配置
根据实际需求修改`config/database_config.toml`文件。

## 数据处理配置
根据实际需求修改`config/data_preprocess_config.toml`文件。

# 4.启动服务(优先使用screen,防止断联)
进入app文件夹,然后运行下面的命令:
```shell
bash scripts/run.sh
```
