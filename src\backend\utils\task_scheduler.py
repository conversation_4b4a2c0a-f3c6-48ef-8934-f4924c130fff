from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.executors.pool import ThreadPoolExecutor
from apscheduler.jobstores.memory import MemoryJobStore

            
# 配置作业默认值
job_defaults = {
    'coalesce': True,  # 堆积的任务只运行一次
    'max_instances': 10,  # 同一个任务同时可以有20个实例运行
    'misfire_grace_time': 60 # 任务错过执行时间的容忍度(秒)
}

# 创建background task 调度器
background_jobstore = MemoryJobStore()
background_executor = ThreadPoolExecutor(max_workers=10)
background_scheduler = BackgroundScheduler(
    jobstores={'default': background_jobstore}, 
    executors={'default': background_executor},
    job_defaults=job_defaults
)

# 创建训练调度器, 同时只允许2个训练任务
train_jobstore = MemoryJobStore()
train_executor = ThreadPoolExecutor(max_workers=2)
train_scheduler = BackgroundScheduler(
    jobstores={'default': train_jobstore}, 
    executors={'default': train_executor},
    job_defaults=job_defaults
)

# 创建预测调度器, 同时只允许运行10个预测任务
predict_jobstore = MemoryJobStore()
predict_executor = ThreadPoolExecutor(max_workers=10)
predict_scheduler = BackgroundScheduler(
    jobstores={'default': predict_jobstore}, 
    executors={'default': predict_executor},
    job_defaults=job_defaults
)

# 创建决策调度器, 同时只允许3个决策任务
decision_jobstore = MemoryJobStore()
decision_executor = ThreadPoolExecutor(max_workers=3)
decision_scheduler = BackgroundScheduler(
    jobstores={'default': decision_jobstore}, 
    executors={'default': decision_executor},
    job_defaults=job_defaults
)

# 启动所有调度器
background_scheduler.start()
train_scheduler.start()
predict_scheduler.start()
decision_scheduler.start()

