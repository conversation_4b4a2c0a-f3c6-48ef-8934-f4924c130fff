# IndustryAI Just 命令使用指南

## 📖 概述

本项目使用 [Just](https://github.com/casey/just) 作为统一的命令管理工具,替代了原有的多个shell脚本。Just是一个现代化的命令运行器,提供了更好的可读性、参数化支持和错误处理。

## 🚀 快速开始

### 安装 Just

```bash
# Ubuntu/Debian
sudo apt install just

# 或使用 cargo 安装
cargo install just

# 或使用 snap 安装
sudo snap install --edge just
```

### 查看所有可用命令

```bash
# 显示帮助信息
just help

# 或直接运行(默认显示帮助)
just
```

## 📋 命令分类

### 🚀 服务管理

#### `just server [plant]`
启动IndustryAI主服务器

```bash
# 使用默认工厂(阳泉)
just server

# 指定工厂名称
just server "北京"
just server "上海"
```

**功能说明:**
- 自动检查Python环境和依赖
- 设置必要的环境变量(PLANT、BACKGROUND_TASK、PYTHONPATH)
- 启动FastAPI服务器(监听0.0.0.0:8999)
- 替代原有的 `scripts/run.sh`

### 🎯 训练相关

#### `just train`
运行所有预配置的训练任务

```bash
just train
```

**预配置项目:**
- 阳泉窑电耗样本_阳泉窑电耗样本_CNN(间隔300秒,classic算法)
- 阳泉窑实物煤耗样本_阳泉窑实物煤耗样本_CNN(间隔600秒,classic_alter算法)
- 阳泉fcao样本_阳泉fcao样本_CNN(间隔450秒,classic算法)

#### `just train-single <project_name> [interval] [algorithm]`
运行单个训练任务

```bash
# 使用默认参数(间隔300秒,classic算法)
just train-single "项目名称"

# 自定义间隔时间
just train-single "项目名称" 600

# 自定义算法类型
just train-single "项目名称" 300 "classic_alter"
```

**参数说明:**
- `project_name`:项目名称(必需)
- `interval`:间隔时间(秒,默认300)
- `algorithm`:算法类型(默认classic)
  - `classic`:经典算法
  - `classic_alter`:经典替代算法

### 🔮 预测相关

#### `just predict`
运行所有预配置的预测任务

```bash
just predict
```

**预配置项目:**
- Bubble缺陷样本_Bubble缺陷样本_CNN(间隔60秒,classic_mo预测器)
- Knot缺陷样本_Knot缺陷样本_CNN(间隔60秒,classic_mo预测器)
- Stone缺陷样本_Stone缺陷样本_CNN(间隔60秒,classic_mo预测器)
- PointDefect缺陷样本_PointDefect缺陷样本_CNN(间隔60秒,classic_mo预测器)

#### `just predict-single <project_name> [interval] [predictor_type]`
运行单个预测任务

```bash
# 使用默认参数(间隔60秒,classic_mo预测器)
just predict-single "Bubble缺陷样本_Bubble缺陷样本_CNN"

# 自定义参数
just predict-single "项目名称" 120 "classic_mo"
```

**参数说明:**
- `project_name`:项目名称(必需)
- `interval`:间隔时间(秒,默认60)
- `predictor_type`:预测器类型(默认classic_mo)

### ⚡ 优化相关

#### `just optimize`
运行所有预配置的优化任务

```bash
just optimize
```

**预配置项目:**
- 回转窑(间隔600秒)

#### `just optimize-single <project_name> [interval]`
运行单个优化任务

```bash
# 使用默认间隔(600秒)
just optimize-single "回转窑"

# 自定义间隔时间
just optimize-single "项目名称" 300
```

### 💓 心跳检测

#### `just build-heartbeat`
构建Rust心跳检测程序

```bash
just build-heartbeat
```

**功能说明:**
- 检查Rust环境(cargo命令)
- 进入 `src/heartbeat_checker` 目录
- 执行 `cargo build --release`
- 验证可执行文件生成
- 替代原有的 `scripts/build_heartbeat.sh`

#### `just start-heartbeat`
启动心跳监控程序

```bash
just start-heartbeat
```

**环境变量配置:**
```bash
# 可选的环境变量配置
export HEARTBEAT_FILE="heartbeat.json"          # 心跳文件路径
export HEARTBEAT_TIMEOUT=30                     # 超时时间(秒)
export HEARTBEAT_CHECK_INTERVAL=5               # 检查间隔(秒)
export HEARTBEAT_MAX_FAILURES=3                 # 最大失败次数
export HEARTBEAT_LOG_FILE="/tmp/heartbeat_monitor.log"  # 日志文件
export HEARTBEAT_VERBOSE=false                  # 详细输出
export RUST_LOG=info                           # Rust日志级别

# 然后启动
just start-heartbeat
```

### 📦 项目管理

#### `just package`
打包项目为tar.gz文件

```bash
just package
```

**功能说明:**
- 创建 `industryai.tar.gz` 压缩包
- 自动排除不必要的文件和目录
- 替代原有的 `scripts/zip.sh`

**排除的文件/目录:**
- `*.pyc`、`__pycache__`
- `.git`、`.env`、`.venv`、`.idea`、`.trae`
- `heartbeat_checker`、`logs`、`experiments`、`docs`
- 已存在的 `industryai.tar.gz`

#### `just install-deps`
安装industrytslib依赖库

```bash
just install-deps
```

**功能说明:**
- 在 `libs` 目录中查找 `industrytslib-*.whl` 文件
- 安装指定的依赖组合:`async,data_storage,data_vis,ml`
- 使用 `--force --no-deps` 参数强制安装
- 替代原有的 `libs/dependency_install.sh`

#### `just clean-deps`
卸载industrytslib依赖库

```bash
just clean-deps
```

#### `just list-wheels`
列出可用的wheel文件

```bash
just list-wheels
```

### 🧪 开发工具

#### `just exp <script_name>`
运行实验脚本

```bash
# 运行训练实验
just exp exp_train

# 运行预测实验
just exp exp_pred_multi_output

# 运行优化实验
just exp exp_opt
```

**可用的实验脚本:**
- `exp_train`:训练实验
- `exp_train_joint`:联合训练实验
- `exp_train_seq`:序列训练实验
- `exp_train_seq_mt`:多任务序列训练实验
- `exp_pred_multi_output`:多输出预测实验
- `exp_rt`:实时实验
- `exp_rt_mo`:多输出实时实验
- `exp_seq_rt`:序列实时实验
- `exp_seq_soft`:序列软测量实验
- `exp_opt`:优化实验
- `seq_pred`:序列预测
- `torch_val`:PyTorch验证

#### `just status`
检查项目状态

```bash
just status
```

**检查内容:**
- Python环境信息
- industrytslib依赖状态
- 心跳检测程序构建状态
- 配置文件存在性检查
- 项目目录结构验证

## 🔧 高级用法

### 环境变量配置

项目支持通过环境变量进行配置:

```bash
# 数据库配置
export DATABASE_URL="postgresql://user:pass@localhost/db"

# 工厂配置
export PLANT="阳泉"

# 后台任务配置
export BACKGROUND_TASK=True

# 心跳监控配置
export HEARTBEAT_FILE="custom_heartbeat.json"
export HEARTBEAT_TIMEOUT=60
```

### 批量操作示例

```bash
# 完整的部署流程
just install-deps
just build-heartbeat
just status
just server

# 开发调试流程
just status
just exp exp_train
just train-single "测试项目" 60 classic
```

### 并行执行

```bash
# 在不同终端中并行运行
# 终端1:启动服务器
just server

# 终端2:启动心跳监控
just start-heartbeat

# 终端3:运行训练任务
just train
```

## 🐛 故障排除

### 常见问题

#### 1. Just命令未找到
```bash
# 检查just是否安装
which just

# 如果未安装,使用cargo安装
cargo install just
```

#### 2. Python环境问题
```bash
# 检查当前环境
just status

# 激活正确的conda环境
conda activate your_env_name
```

#### 3. 依赖库安装失败
```bash
# 检查wheel文件是否存在
just list-wheels

# 手动安装
cd libs
pip install industrytslib-*.whl[async,data_storage,data_vis,ml] --force --no-deps
```

#### 4. 心跳检测构建失败
```bash
# 检查Rust环境
cargo --version

# 如果未安装Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env
```

#### 5. 服务器启动失败
```bash
# 检查端口占用
ss -tlnp | grep 8999

# 检查配置文件
ls -la config/
```

### 日志查看

```bash
# 查看服务器日志
tail -f logs/app.log

# 查看心跳监控日志
tail -f /tmp/heartbeat_monitor.log

# 查看系统日志
journalctl -f -u your_service_name
```

## 📚 参考资料

- [Just官方文档](https://github.com/casey/just)
- [IndustryTSLib文档](https://github.com/your-org/industrytslib)
- [FastAPI文档](https://fastapi.tiangolo.com/)
- [PyTorch文档](https://pytorch.org/docs/)

## 🤝 贡献指南

如需添加新的命令或修改现有功能:

1. 编辑根目录的 `justfile`
2. 遵循现有的命名规范和错误处理模式
3. 更新本文档
4. 提交PR进行代码审查

## 📄 许可证

本项目遵循MIT许可证。详见LICENSE文件。