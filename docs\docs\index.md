# IndustryAI 文档

欢迎使用IndustryAI文档！本文档提供了关于IndustryAI工业智能平台的完整指南。

## 项目概述

IndustryAI是一个基于FastAPI和PyTorch的工业智能平台,专注于工业时间序列建模、实时预测和智能决策。项目使用industrytslib作为核心算法库,提供完整的工业AI解决方案。

### 核心特性

- 🏭 **工业时间序列建模**: 支持多种深度学习算法(TimesNet、Informer、PatchTST、Mamba等)
- 🔄 **实时预测系统**: 高性能异步预测管道,支持多模型并行
- 🧠 **智能优化决策**: 基于强化学习和多目标优化的决策系统
- 📊 **数据处理引擎**: 基于Polars的高性能数据处理
- 🔒 **企业级安全**: FastAPI Guard安全中间件,IP控制和攻击检测
- 💾 **多数据库支持**: PostgreSQL、MSSQL、InfluxDB、Redis
- 🖥️ **现代化前端**: Vue 3 + Element Plus + ECharts
- 🐳 **容器化部署**: Docker支持,跨平台部署

## 技术架构

```mermaid
graph TD
    subgraph TopLevel [服务层]
        direction LR
        A["<b>前端界面</b><br/>Vue 3 + EP"]
        B["<b>FastAPI后端</b><br/>异步API服务"]
        C["<b>industrytslib</b><br/>核心算法库"]
    end
    
    D["<b>任务调度器</b><br/>APScheduler"]
    E["<b>数据存储层</b><br/>PostgreSQL, Redis, InfluxDB"]

    A <--> B
    B <--> C
    A --> D
    B --> D
    C --> D
    D --> E
```

## 文档结构

### 📚 用户文档
- [用户指南](user_guide.md) - 安装、配置和使用说明
- [Just命令指南](just_guide.md) - 统一命令管理工具使用
- [配置文档](configuration.md) - 系统配置和安全设置

### 🔧 部署文档
- [部署概述](deploy.md) - 部署方式选择
- [Linux部署](deploy/deploy_linux.md) - Linux环境部署指南
- [Windows部署](deploy/deploy_windows.md) - Windows环境部署指南

### 📡 API文档
- [API概述](api_doc/index.md) - 接口总览和认证
- [训练接口](api_doc/train.md) - 模型训练相关接口
- [实时预测接口](api_doc/realtime_predict.md) - 预测任务接口
- [优化决策接口](api_doc/decision_making.md) - 智能决策接口

## 快速开始

### 1. 环境准备
```bash
# 检查Python版本 (需要3.11+)
python --version

# 安装Just命令工具
sudo apt install just  # Ubuntu/Debian
# 或 cargo install just
```

### 2. 项目部署
```bash
# 查看所有可用命令
just help

# 启动服务 (默认阳泉工厂)
just server

# 或指定工厂
just server "北京"
```

### 3. 验证部署
```bash
# 检查服务状态
curl http://localhost:8081/train/

# 查看API文档
open http://localhost:8081/docs
```

## 版本信息

- **当前版本**: 2025.06.26
- **Python要求**: >=3.11
- **核心依赖**: FastAPI, PyTorch 2.5+, industrytslib 3.1.7
- **前端版本**: Vue 3.4+, Element Plus 2.4+

## 技术支持

- 📖 **文档问题**: 查看对应章节的详细说明
- 🐛 **Bug报告**: 提交issue到项目仓库
- 💬 **技术讨论**: 联系开发团队
- 🔧 **故障排除**: 参考[用户指南](user_guide.md)中的故障排除章节

## 开发团队

- **作者**: Yonghang Li (<EMAIL>)
- **项目**: 工业智能平台
- **许可**: 企业级商业许可
