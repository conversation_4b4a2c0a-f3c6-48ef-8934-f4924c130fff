import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '../utils/api'

export const useSystemStore = defineStore('system', () => {
  const isLoading = ref(false)
  const systemInfo = ref({
    status: 'unknown',
    version: '1.0.0',
    uptime: 0,
    lastUpdate: null
  })
  
  const heartbeatStatus = ref({
    enabled: false,
    healthy: false,
    lastUpdate: null,
    checkResults: {},
    errorMessage: null
  })

  // 检查系统健康状态
  const checkHealth = async () => {
    try {
      isLoading.value = true
      const response = await api.get('/health')
      systemInfo.value = {
        ...systemInfo.value,
        status: response.data.status,
        lastUpdate: response.data.timestamp
      }
      return response.data.status === 'ok'
    } catch (error) {
      console.error('健康检查失败:', error)
      systemInfo.value.status = 'error'
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 获取心跳状态
  const getHeartbeatStatus = async () => {
    try {
      const response = await api.get('/heartbeat/status')
      heartbeatStatus.value = {
        enabled: response.data.enabled,
        healthy: response.data.healthy,
        lastUpdate: response.data.last_update,
        checkResults: response.data.check_results || {},
        errorMessage: response.data.error_message
      }
      return response.data
    } catch (error) {
      console.error('获取心跳状态失败:', error)
      heartbeatStatus.value.errorMessage = '无法获取心跳状态'
      return null
    }
  }

  // 重置状态
  const resetStatus = () => {
    systemInfo.value = {
      status: 'unknown',
      version: '1.0.0',
      uptime: 0,
      lastUpdate: null
    }
    heartbeatStatus.value = {
      enabled: false,
      healthy: false,
      lastUpdate: null,
      checkResults: {},
      errorMessage: null
    }
  }

  return {
    isLoading,
    systemInfo,
    heartbeatStatus,
    checkHealth,
    getHeartbeatStatus,
    resetStatus
  }
})