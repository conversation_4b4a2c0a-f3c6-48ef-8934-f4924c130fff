from pydantic_settings import BaseSettings
from typing import Dict, Any
from functools import lru_cache

class Settings(BaseSettings):
    """Application settings.
    
    Attributes:
        app_name: Name of the application
        version: Application version
        background_tasks: Flag to enable background tasks
        job_defaults: Default job scheduler settings
    """
    app_name: str = "Industry AI Server"
    version: str = "2024.11.preview"
    background_tasks: bool = True
    job_defaults: Dict[str, Any] = {
        "coalesce": True,
        "max_instances": 1,
        "misfire_grace_time": 80
    }
    
    class Config:
        env_file = ".env"

@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()
