import os
import sys
from loguru import logger

from industrytslib.core import get_trainer
from industrytslib.utils.logutils import get_logger
# from industrytslib.utils.readconfig import read_config_toml


# Add both current directory and parent directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.extend([current_dir, parent_dir])


if __name__ == "__main__":
    project_name = "阳泉回转窑联合训练孪生样本_阳泉回转窑联合训练孪生样本_MTWaveMambaMOE"

    # config = read_config_toml("config/dbconfig.toml")
    logger = get_logger(project_name, "train")
    logger.info(f"project_name: {project_name}")
    trainer = get_trainer(
        project_name=project_name, 
        model_type="multi_task_seq_pretrainer"
    )
    trainer.main()
    # trainer.test(test=1)
