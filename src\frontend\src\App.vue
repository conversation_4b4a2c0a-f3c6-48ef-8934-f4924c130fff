<template>
  <div id="app">
    <el-container class="layout-container">
      <!-- 顶部导航栏 -->
      <el-header class="header">
        <div class="header-content">
          <div class="logo">
            <el-icon><Setting /></el-icon>
            <span>工业AI时间序列分析系统</span>
          </div>
          <div class="nav-menu">
            <el-menu
              :default-active="$route.path"
              mode="horizontal"
              router
              background-color="transparent"
              text-color="#fff"
              active-text-color="#409EFF"
            >
              <el-menu-item index="/">首页</el-menu-item>
              <el-menu-item index="/train">模型训练</el-menu-item>
              <el-menu-item index="/predict">实时预测</el-menu-item>
              <el-menu-item index="/decision">决策优化</el-menu-item>
              <el-menu-item index="/monitor">系统监控</el-menu-item>
            </el-menu>
          </div>
        </div>
      </el-header>

      <!-- 主内容区域 -->
      <el-main class="main-content">
        <router-view />
      </el-main>

      <!-- 底部状态栏 -->
      <el-footer class="footer">
        <div class="footer-content">
          <span>© 2024 工业AI时间序列分析系统 - 版本 1.0.0</span>
          <div class="status-indicators">
            <el-tag :type="systemStatus.type" size="small">
              {{ systemStatus.text }}
            </el-tag>
          </div>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useSystemStore } from './stores/system'

const systemStore = useSystemStore()

const systemStatus = ref({
  type: 'success',
  text: '系统正常'
})

// 检查系统状态
const checkSystemStatus = async () => {
  try {
    const status = await systemStore.checkHealth()
    systemStatus.value = {
      type: status ? 'success' : 'danger',
      text: status ? '系统正常' : '系统异常'
    }
  } catch (error) {
    systemStatus.value = {
      type: 'danger',
      text: '连接失败'
    }
  }
}

onMounted(() => {
  checkSystemStatus()
  // 每30秒检查一次系统状态
  setInterval(checkSystemStatus, 30000)
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  height: 60px;
  line-height: 60px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.logo .el-icon {
  margin-right: 8px;
  font-size: 24px;
}

.nav-menu {
  flex: 1;
  display: flex;
  justify-content: center;
}

.main-content {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
}

.footer {
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
  height: 60px;
  line-height: 60px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  color: #606266;
}

.status-indicators {
  display: flex;
  gap: 10px;
}
</style>