import os
import sys
from loguru import logger

from industrytslib.core import get_trainer
from industrytslib.utils.logutils import get_logger
from industrytslib.utils.readconfig import read_config_toml


# Add both current directory and parent directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.extend([current_dir, parent_dir])


if __name__ == "__main__":
    # project_name = "阳泉回转窑联合训练孪生样本_阳泉回转窑联合训练孪生样本_WaveForM"
    # project_name = "阳泉回转窑联合训练孪生样本_阳泉回转窑联合训练孪生样本_WaveMamba"
    # project_name = "阳泉回转窑联合训练孪生样本_阳泉回转窑联合训练孪生样本_Informer"
    # project_name = "阳泉回转窑联合训练孪生样本_阳泉回转窑联合训练孪生样本_iTransformer"
    # project_name = "阳泉回转窑联合训练孪生样本_阳泉回转窑联合训练孪生样本_DLinear"
    # project_name = "阳泉回转窑联合训练孪生样本_阳泉回转窑联合训练孪生样本_RWKV"
    
    project_name_list = [
        "三友窑孪生样本_三友窑孪生样本_WaveForM",
        "原料A磨孪生样本_原料A磨孪生样本_WaveForM",
        "原料B磨孪生样本_原料B磨孪生样本_WaveForM",
        "水泥A磨孪生样本_水泥A磨孪生样本_WaveForM",
        "水泥磨二线孪生样本_水泥磨二线孪生样本_WaveForM",
        "煤磨孪生样本_煤磨孪生样本_WaveForM",
    ]

    config = read_config_toml("config/database_config.toml")
    model_type="time_series_sequence"
    
    for project_name in project_name_list:
        logger = get_logger(project_name, "train")
        logger.info(f"project_name: {project_name}")
        trainer = get_trainer(
            project_name=project_name, 
            dbconfig=config,
            model_type=model_type
        )
        trainer.main()
        
        del logger
        del trainer
