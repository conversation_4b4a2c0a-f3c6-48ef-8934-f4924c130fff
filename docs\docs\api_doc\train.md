# 训练模块 API (`/train`)

## 概述

训练模块负责管理和执行模型训练任务。它提供了启动单个或批量训练、停止训练以及查询正在运行的训练任务的接口。所有与训练相关的API都以 `/train` 为路径前缀。

## 🆕 最新API特性

经过更新,训练API现在支持:

1. **小时单位设置** - 使用 `interval_hours` 参数设置训练间隔
2. **3天错过任务容忍时间** - 自动设置为3天(259200秒)的misfire_grace_time
3. **向后兼容** - 仍支持原有的 `interval` 参数(秒单位)
4. **并发控制** - 最多同时执行3个训练任务
5. **时间单位枚举** - 支持秒、分钟、小时三种时间单位
6. **🔥 强制停止机制** - 支持强制终止正在运行的训练进程,无需等待任务自然结束

## 接口列表

### 1. 训练模块首页

-   **接口地址**: `GET /train/`
-   **功能描述**: 返回训练模块的欢迎信息,可用于测试服务连通性。
-   **请求参数**: 无
-   **响应示例**:
    ```json
    {
      "message": "train information page"
    }
    ```

---

### 2. 启动单个模型训练 🆕

-   **接口地址**: `POST /train/train`
-   **功能描述**: 启动一个定期执行的模型训练任务,支持小时单位间隔设置。
-   **注意**:
    -   如果已存在同名项目(`project_name`)的训练任务,旧任务将被移除,新任务将替换它。
    -   自动设置3天的错过任务容忍时间(259200秒)
    -   最多同时执行3个训练任务

#### 请求体(新版本 - 推荐)

```json
{
  "project_name": "string",
  "interval_hours": "integer",
  "algorithm_type": "string"
}
```

#### 请求体(向后兼容)

```json
{
  "project_name": "string",
  "interval": "integer",
  "time_unit": "hours|minutes|seconds",
  "algorithm_type": "string"
}
```

| 参数名           | 类型    | 必填 | 描述                                                         |
| ---------------- | ------- | ---- | ------------------------------------------------------------ |
| `project_name`   | string  | 是   | 项目的唯一名称,也用作后台任务的 `job_id`。                    |
| `interval_hours` | integer | 否*  | 训练间隔(小时)。**推荐使用此参数**。                        |
| `interval`       | integer | 否*  | 训练间隔(数值单位由time_unit指定)。**向后兼容参数**。       |
| `time_unit`      | string  | 否   | 时间单位:`"hours"`(默认)、`"minutes"`、`"seconds"`。         |
| `algorithm_type` | string  | 是   | 指定使用的训练算法。                                         |

> \* `interval_hours` 或 `interval` 必须提供其中一个,优先使用 `interval_hours`

#### `algorithm_type` 可选值

```
"sequence", "sequence_online", "classic", "classic_online", "gan", "rl", 
"time_series_sequence", "time_series_classic", "multi_input_time_series_classic", 
"joint_loss", "joint_gan", "quality_sequence", "joint_trainer_sequence", 
"joint_quality_monitor", "joint_trainer_final", "multi_task_seq_pretrainer", "ml"
```

#### 响应示例

```json
{
  "code": 200,
  "message": "训练模型<project_name>请求成功!!!"
}
```

---

### 3. 批量模型训练 🆕

-   **接口地址**: `POST /train/train_list`
-   **功能描述**: 批量启动多个模型训练任务,支持小时单位间隔设置。
-   **注意**: 此接口会立即执行所有训练任务的 `main` 方法,不会添加到调度器。

#### 请求体(新版本 - 推荐)

```json
{
  "project_name_list": ["string"],
  "interval_hours_list": ["integer"],
  "algorithm_type_list": ["string"]
}
```

#### 请求体(向后兼容)

```json
{
  "project_name_list": ["string"],
  "interval_list": ["integer"],
  "time_unit": "hours|minutes|seconds",
  "algorithm_type_list": ["string"]
}
```

| 参数名                | 类型         | 必填 | 描述                                |
| --------------------- | ------------ | ---- | ----------------------------------- |
| `project_name_list`   | `list[str]`  | 是   | 要训练的项目名称列表。              |
| `interval_hours_list` | `list[int]`  | 否*  | 对应的训练间隔列表(小时)。**推荐使用**。 |
| `interval_list`       | `list[int]`  | 否*  | 对应的训练间隔列表(数值单位由time_unit指定)。**向后兼容**。 |
| `time_unit`           | string       | 否   | 时间单位:`"hours"`(默认)、`"minutes"`、`"seconds"`。 |
| `algorithm_type_list` | `list[str]`  | 是   | 对应的算法类型列表。                |

> \* `interval_hours_list` 或 `interval_list` 必须提供其中一个,优先使用 `interval_hours_list`

#### 响应示例

```json
{
  "code": 200,
  "message": "训练列表['project1', 'project2']请求成功!!!"
}
```

---

### 4. 停止指定模型训练 🔥

-   **接口地址**: `POST /train/stop_train`
-   **功能描述**: 强制停止指定项目的训练任务,包括调度器中的定时任务和正在运行的训练进程。
-   **停止机制**: 
    -   首先从调度器中移除定时任务
    -   然后强制终止对应的训练子进程（如果存在）
    -   支持优雅终止（SIGTERM）和强制杀死（SIGKILL）
-   **注意**: 此操作会立即中断正在进行的训练,可能导致训练状态丢失

#### 请求体

```json
{
  "project_name": "string"
}
```

| 参数名         | 类型   | 必填 | 描述             |
| -------------- | ------ | ---- | ---------------- |
| `project_name` | string | 是   | 要停止的项目名称。 |

#### 响应示例

```json
{
  "code": 200,
  "message": "停止训练<project_name>请求成功!!!"
}
```

---

### 5. 停止所有训练任务 🔥

-   **接口地址**: `POST /train/stop_train_all`
-   **功能描述**: 强制停止所有训练任务,包括调度器中的所有训练相关任务和正在运行的训练进程。
-   **停止机制**:
    -   清空调度器中所有训练相关的定时任务
    -   强制终止所有正在运行的训练子进程
    -   支持优雅终止（SIGTERM）和强制杀死（SIGKILL）
-   **请求参数**: 无
-   **注意**: 此操作会立即中断所有正在进行的训练,可能导致训练状态丢失
-   **响应示例**:
    ```json
    {
      "code": 200,
      "message": "停止所有训练任务!!!"
    }
    ```

---

### 6. 查询训练任务状态 🆕

-   **接口地址**: `GET /train/query_train_tasks`
-   **功能描述**: 查询所有训练任务的状态,包括调度器中的定时任务和正在运行的训练进程。
-   **请求参数**: 无
-   **返回信息**:
    -   `scheduled_tasks`: 调度器中的定时训练任务（包含下次执行时间、上次执行时间等）
    -   `running_processes`: 当前正在执行的训练子进程（包含进程ID、进程名、运行状态等）
    -   `summary`: 任务数量统计
    -   `total_count`: 总任务数
-   **响应字段说明**:
    | 字段名                | 类型           | 说明                                   |
    |----------------------|---------------|----------------------------------------|
    | code                 | int           | 状态码，200表示成功                    |
    | message              | string        | 返回信息                               |
    | data.total_count     | int           | 总任务数（调度任务+运行进程）          |
    | data.scheduled_tasks | list[object]  | 调度器中的定时训练任务列表             |
    | data.running_processes| list[object] | 当前正在运行的训练进程列表             |
    | data.summary         | object        | 任务数量统计（调度任务数、运行进程数） |

    - `scheduled_tasks` 每项字段：
        | 字段名         | 类型     | 说明                 |
        |----------------|----------|----------------------|
        | id             | string   | 任务ID（即项目名）    |
        | project_name   | string   | 项目名称             |
        | next_run_time  | string   | 下次执行时间（ISO格式）|
        | last_run_time  | string或null | 上次执行时间（可选）|
        | type           | string   | 固定为'scheduled'    |
    - `running_processes` 每项字段：
        | 字段名         | 类型     | 说明                 |
        |----------------|----------|----------------------|
        | project_name   | string   | 项目名称             |
        | pid            | int      | 进程ID               |
        | process_name   | string   | 进程名               |
        | is_alive       | bool     | 是否存活             |
        | type           | string   | 固定为'running_process'|
    - `summary` 字段：
        | 字段名           | 类型   | 说明                 |
        |----------------|--------|----------------------|
        | scheduled_count | int    | 调度任务数量         |
        | running_count   | int    | 运行进程数量         |

-   **响应示例**:
    ```json
    {
      "code": 200,
      "message": "查询成功",
      "data": {
        "total_count": 2,
        "scheduled_tasks": [
          {
            "id": "project_name_example",
            "project_name": "project_name_example",
            "next_run_time": "2024-07-30T12:00:00.123456+08:00",
            "last_run_time": "2024-07-30T10:00:00.123456+08:00",
            "type": "scheduled"
          }
        ],
        "running_processes": [
          {
            "project_name": "project_name_example",
            "pid": 12345,
            "process_name": "train_project_name_example",
            "is_alive": true,
            "type": "running_process"
          }
        ],
        "summary": {
          "scheduled_count": 1,
          "running_count": 1
        }
      }
    }
    ```

-   **说明**:
    - `scheduled_tasks` 反映调度器中已注册的定时训练任务（即周期性自动训练任务）。
    - `running_processes` 反映当前正在运行的训练子进程（即实际执行中的训练任务）。
    - `summary` 字段便于前端快速统计任务数量。
    - `total_count` 为所有任务总数（调度任务+运行进程）。
    - 字段类型和内容请严格参照上述示例和说明。

---

### 7. 查询最新训练效果（返回图片）

-   **接口地址**: `POST /train/query_latest_train_effect`
-   **功能描述**: 查询指定项目最新的训练效果图片（PNG格式），用于可视化模型训练结果。
-   **请求体**:
    ```json
    {
      "project_name": "string"
    }
    ```
    | 参数名         | 类型   | 必填 | 描述             |
    | -------------- | ------ | ---- | ---------------- |
    | `project_name` | string | 是   | 要查询的项目名称 |

-   **响应类型**:
    - 成功时：返回图片文件（`image/png`），文件名为 `<project_name>.png`
    - 失败时：返回 JSON 格式的错误信息（404 或 500）

-   **图片查找逻辑**:
    1. 首先查找 `resource/results/test_results/{project_name}/test_result_ndarray/` 目录下最新的 PNG 图片（按修改时间排序）。
    2. 如果没有该目录或无图片，则查找 `resource/results/test_results/{project_name}/` 下最新的子文件夹，优先返回其中的 `true_pred_all.png`，如不存在则返回该文件夹下最新的 PNG 图片。
    3. 若均未找到，返回 404。

-   **异常情况**:
    - 若项目目录不存在，或未生成任何效果图片，返回 404，错误信息如：
      ```json
      { "detail": "未找到项目 <project_name> 的训练效果图片" }
      ```
    - 查询过程中发生内部错误，返回 500，错误信息如：
      ```json
      { "detail": "查询训练效果时发生内部错误: <错误描述>" }
      ```

-   **请求示例**:
    ```http
    POST /train/query_latest_train_effect
    Content-Type: application/json
    {
      "project_name": "阳泉窑电耗样本_阳泉窑电耗样本_CNN"
    }
    ```

-   **响应示例**:
    - 成功（图片二进制流，Content-Type: image/png，文件名如 `阳泉窑电耗样本_阳泉窑电耗样本_CNN.png`）
    - 失败（JSON）：
      ```json
      {
        "detail": "未找到项目 阳泉窑电耗样本_阳泉窑电耗样本_CNN 的训练效果图片"
      }
      ```

-   **注意事项**:
    - 若训练尚未完成或未生成效果图片，将返回404。
    - 建议前端直接以图片方式展示该接口返回内容。
    - 图片文件存储路径为 `resource/results/test_results/{project_name}/`。
    - 查找逻辑与后端实现完全一致，确保接口行为可预期。

## 使用示例

### Python客户端使用示例

#### 1. 使用小时单位(推荐)

```python
import asyncio
from src.backend.requestes.requests_train import train_model_async

async def example_train_with_hours():
    """使用小时单位设置训练间隔"""
    
    # 每2小时训练一次
    result = await train_model_async(
        base_url="http://localhost:8999",
        project_name="阳泉窑电耗样本_阳泉窑电耗样本_CNN",
        interval_hours=2,  # 2小时间隔
        algorithm_type="classic"
    )
    print("训练响应:", result)

# 运行示例
asyncio.run(example_train_with_hours())
```

#### 2. 使用秒单位(向后兼容)

```python
async def example_train_with_seconds():
    """使用秒单位设置训练间隔"""
    
    # 每7200秒(2小时)训练一次
    result = await train_model_async(
        base_url="http://localhost:8999",
        project_name="阳泉窑电耗样本_阳泉窑电耗样本_CNN",
        interval=7200,  # 7200秒 = 2小时
        algorithm_type="classic"
    )
    print("训练响应:", result)
```

#### 3. 批量训练任务

```python
async def example_batch_train():
    """批量训练多个项目"""
    
    from src.backend.requestes.requests_train import train_model_list_async
    
    project_names = [
        "阳泉窑电耗样本_阳泉窑电耗样本_CNN",
        "阳泉实物煤耗样本_阳泉实物煤耗样本_CNN",
        "阳泉fcao样本_阳泉fcao样本_CNN"
    ]
    
    # 使用小时单位
    result = await train_model_list_async(
        base_url="http://localhost:8999",
        project_name_list=project_names,
        interval_hours_list=[2, 3, 1],  # 分别为2小时、3小时、1小时
        algorithm_type_list=["classic", "classic_online", "classic"]
    )
    print("批量训练响应:", result)
```

### 命令行使用示例

#### 使用小时单位

```bash
# 每2小时训练一次
python src/backend/requestes/requests_train.py \
    --project-name "阳泉窑电耗样本_阳泉窑电耗样本_CNN" \
    --interval-hours 2 \
    --algorithm-type "classic"

# 每6小时训练一次
python src/backend/requestes/requests_train.py \
    -p "阳泉fcao样本_阳泉fcao样本_CNN" \
    -ih 6 \
    -a "classic"
```

#### 使用秒单位(向后兼容)

```bash
# 每7200秒(2小时)训练一次
python src/backend/requestes/requests_train.py \
    --project-name "阳泉窑电耗样本_阳泉窑电耗样本_CNN" \
    --interval 7200 \
    --algorithm-type "classic"
```

### HTTP请求示例

#### 使用小时单位的请求体

```json
{
  "project_name": "阳泉窑电耗样本_阳泉窑电耗样本_CNN",
  "interval_hours": 2,
  "algorithm_type": "classic"
}
```

#### 向后兼容的请求体

```json
{
  "project_name": "阳泉窑电耗样本_阳泉窑电耗样本_CNN", 
  "interval": 7200,
  "time_unit": "seconds",
  "algorithm_type": "classic"
}
```

### 完整的Python调用示例

```python
import requests
import asyncio
import time
import json
from src.backend.requestes.requests_train import train_model_async, stop_train

BASE_URL = "http://localhost:8999"
HEADERS = {"Content-Type": "application/json"}

async def start_training_with_hours(project_name: str, hours: int, algo_type: str = "classic"):
    """启动训练任务 - 使用小时单位"""
    print(f"\n--- 启动项目 '{project_name}' 的训练,每{hours}小时执行一次 ---")
    try:
        result = await train_model_async(
            base_url=BASE_URL,
            project_name=project_name,
            interval_hours=hours,
            algorithm_type=algo_type
        )
        print("响应:", result)
        return True
    except Exception as e:
        print(f"请求失败: {e}")
        return False

def start_training_legacy(project_name: str, interval_seconds: int, algo_type: str = "classic"):
    """启动训练任务 - 传统方式(秒单位)"""
    print(f"\n--- 启动项目 '{project_name}' 的训练,每{interval_seconds}秒执行一次 ---")
    payload = {
        "project_name": project_name,
        "interval": interval_seconds,
        "time_unit": "seconds",
        "algorithm_type": algo_type
    }
    try:
        response = requests.post(f"{BASE_URL}/train/train", json=payload, headers=HEADERS)
        response.raise_for_status()
        print("响应:", response.json())
        return True
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return False

def query_tasks():
    """查询所有运行中的训练任务"""
    print("\n--- 查询所有训练任务 ---")
    try:
        response = requests.get(f"{BASE_URL}/train/query_train_tasks", headers=HEADERS)
        response.raise_for_status()
        tasks = response.json()
        print(json.dumps(tasks, indent=2, ensure_ascii=False))
        return tasks.get('data', [])
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return []

def stop_training(project_name: str):
    """强制停止指定的训练任务（包括调度器任务和运行中的进程）"""
    print(f"\n--- 强制停止项目 '{project_name}' 的训练 ---")
    payload = {"project_name": project_name}
    try:
        response = requests.post(f"{BASE_URL}/train/stop_train", json=payload, headers=HEADERS)
        response.raise_for_status()
        print("响应:", response.json())
        print("注意: 此操作会立即中断正在进行的训练,可能导致训练状态丢失")
        return True
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return False

def stop_all_trainings():
    """强制停止所有训练任务（包括调度器任务和运行中的进程）"""
    print("\n--- 强制停止所有训练任务 ---")
    try:
        response = requests.post(f"{BASE_URL}/train/stop_train_all", headers=HEADERS)
        response.raise_for_status()
        print("响应:", response.json())
        print("注意: 此操作会立即中断所有正在进行的训练,可能导致训练状态丢失")
    except requests.RequestException as e:
        print(f"请求失败: {e}")

def force_stop_training_demo():
    """演示强制停止功能的完整流程"""
    PROJECT_NAME = "demo_force_stop_project"
    
    print("\n=== 强制停止功能演示 ===")
    
    # 1. 启动一个训练任务
    print("\n1. 启动训练任务...")
    start_training_legacy(PROJECT_NAME, 3600)  # 每小时执行一次
    
    # 2. 查询任务状态
    print("\n2. 查询任务状态...")
    time.sleep(2)
    query_tasks()
    
    # 3. 强制停止训练
    print("\n3. 强制停止训练...")
    stop_training(PROJECT_NAME)
    
    # 4. 再次查询确认停止
    print("\n4. 确认停止结果...")
    time.sleep(1)
    query_tasks()
    
    print("\n=== 演示完成 ===")

async def main():
    """主函数演示各种用法"""
    PROJECT_NAME = "demo_project_for_training"
    
    # 1. 使用新的小时单位API启动训练
    await start_training_with_hours(PROJECT_NAME, 2)  # 每2小时执行一次
    
    # 2. 等待片刻
    print("\n等待5秒...")
    time.sleep(5)
    
    # 3. 查询任务状态
    running_tasks = query_tasks()
    
    # 4. 演示强制停止特定任务
    if running_tasks:
        stop_training(PROJECT_NAME)
    
    # 5. 演示传统API(向后兼容)
    start_training_legacy("legacy_project", 7200)  # 每2小时(7200秒)
    
    # 6. 演示强制停止功能的完整流程
    force_stop_training_demo()
    
    # 7. 强制停止所有任务
    time.sleep(2)
    stop_all_trainings()

if __name__ == "__main__":
    asyncio.run(main())
```

## 🔥 强制停止机制说明

### 技术实现

训练系统采用**子进程隔离**的架构设计,每个训练任务都在独立的子进程中执行,这样可以实现真正的强制停止：

1. **进程管理**: 系统维护一个全局的训练进程字典 `training_processes`,记录每个项目对应的子进程信息
2. **双重停止**: 停止训练时会同时处理调度器任务和运行中的子进程
3. **优雅终止**: 首先尝试发送 `SIGTERM` 信号,给训练任务5秒时间进行清理
4. **强制杀死**: 如果优雅终止失败,则发送 `SIGKILL` 信号强制终止进程
5. **进程清理**: 自动清理已结束的进程,避免僵尸进程

### 停止流程

```
用户请求停止训练
        ↓
从调度器移除定时任务
        ↓
查找对应的训练子进程
        ↓
发送SIGTERM信号（优雅终止）
        ↓
等待5秒
        ↓
进程仍存在？
   ↙        ↘
  是         否
   ↓         ↓
发送SIGKILL   清理完成
（强制杀死）
   ↓
清理进程记录
```

### 注意事项

- **数据安全**: 强制停止可能导致正在写入的模型文件损坏,建议在训练间隙停止
- **状态丢失**: 训练进度、中间结果等可能丢失,无法恢复
- **资源释放**: 系统会自动释放进程占用的内存和GPU资源
- **并发影响**: 强制停止不会影响其他正在运行的训练任务

### 最佳实践

1. **监控训练状态**: 使用 `/train/query_train_tasks` 接口监控训练进程
2. **合理停止时机**: 尽量在训练epoch间隙或验证阶段停止
3. **备份策略**: 重要训练任务建议设置定期保存检查点
4. **日志查看**: 检查训练日志确认停止是否成功

## 错过任务处理说明

- **自动设置**: 系统自动设置3天的错过任务容忍时间
- **计算公式**: 3天 = 3 × 24 × 3600 = 259200秒
- **行为**: 如果因系统故障等原因错过了训练任务,只要不超过3天,任务仍会执行
- **并发限制**: 系统最多同时执行3个训练任务

## 迁移指南

如果您正在使用旧的API,建议迁移到新的小时单位:

```python
# 旧方式(仍然支持)
payload = {
    "project_name": "test_project",
    "interval": 7200,  # 秒
    "algorithm_type": "classic"
}

# 新方式(推荐)
payload = {
    "project_name": "test_project", 
    "interval_hours": 2,  # 小时
    "algorithm_type": "classic"
}
```

## 时间单位枚举

API支持以下时间单位:

```python
from api import TimeUnit

# 可用的时间单位
TimeUnit.SECONDS  # "seconds"
TimeUnit.MINUTES  # "minutes" 
TimeUnit.HOURS    # "hours" (默认)
```