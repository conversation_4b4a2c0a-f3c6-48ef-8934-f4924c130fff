<template>
  <div class="home">
    <div class="page-title">系统概览</div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stats-card">
        <div class="stats-value">{{ stats.totalProjects }}</div>
        <div class="stats-label">总项目数</div>
      </div>
      <div class="stats-card">
        <div class="stats-value">{{ stats.runningTasks }}</div>
        <div class="stats-label">运行中任务</div>
      </div>
      <div class="stats-card">
        <div class="stats-value">{{ stats.todayPredictions }}</div>
        <div class="stats-label">今日预测次数</div>
      </div>
      <div class="stats-card">
        <div class="stats-value">{{ systemStore.systemInfo.status === 'ok' ? '正常' : '异常' }}</div>
        <div class="stats-label">系统状态</div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="card">
      <h3>快速操作</h3>
      <div class="quick-actions">
        <el-button type="primary" @click="$router.push('/train')">
          <el-icon><Setting /></el-icon>
          模型训练
        </el-button>
        <el-button type="success" @click="$router.push('/predict')">
          <el-icon><TrendCharts /></el-icon>
          实时预测
        </el-button>
        <el-button type="warning" @click="$router.push('/decision')">
          <el-icon><Operation /></el-icon>
          决策优化
        </el-button>
        <el-button type="info" @click="$router.push('/monitor')">
          <el-icon><Monitor /></el-icon>
          系统监控
        </el-button>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="card">
      <h3>最近活动</h3>
      <el-table :data="recentActivities" style="width: 100%" v-loading="loading">
        <el-table-column prop="timestamp" label="时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="120">
          <template #default="scope">
            <el-tag :type="getTypeColor(scope.row.type)">{{ getTypeText(scope.row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="项目" width="150" />
        <el-table-column prop="message" label="描述" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
              {{ scope.row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 系统信息 -->
    <div class="card">
      <h3>系统信息</h3>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="系统版本">{{ systemStore.systemInfo.version }}</el-descriptions-item>
        <el-descriptions-item label="运行状态">
          <el-tag :type="systemStore.systemInfo.status === 'ok' ? 'success' : 'danger'">
            {{ systemStore.systemInfo.status === 'ok' ? '正常运行' : '异常' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="最后更新">{{ formatTime(systemStore.systemInfo.lastUpdate) }}</el-descriptions-item>
        <el-descriptions-item label="心跳状态">
          <el-tag :type="systemStore.heartbeatStatus.healthy ? 'success' : 'warning'">
            {{ systemStore.heartbeatStatus.enabled ? (systemStore.heartbeatStatus.healthy ? '健康' : '异常') : '未启用' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useSystemStore } from '../stores/system'
import db from '../utils/database'

const systemStore = useSystemStore()
const loading = ref(false)

const stats = ref({
  totalProjects: 0,
  runningTasks: 0,
  todayPredictions: 0
})

const recentActivities = ref([])

let refreshTimer = null

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 获取类型颜色
const getTypeColor = (type) => {
  const colors = {
    train: 'primary',
    predict: 'success',
    decision: 'warning'
  }
  return colors[type] || 'info'
}

// 获取类型文本
const getTypeText = (type) => {
  const texts = {
    train: '训练',
    predict: '预测',
    decision: '决策'
  }
  return texts[type] || type
}

// 加载最近活动
const loadRecentActivities = async () => {
  try {
    loading.value = true
    
    // 从本地数据库获取最近的活动记录
    const [trainRecords, predictRecords, decisionRecords] = await Promise.all([
      db.getTrainRecords(10),
      db.getPredictRecords(10),
      db.getDecisionRecords(10)
    ])
    
    // 合并并排序所有记录
    const allRecords = [...trainRecords, ...predictRecords, ...decisionRecords]
    allRecords.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
    
    recentActivities.value = allRecords.slice(0, 20).map(record => ({
      ...record,
      message: record.message || `${getTypeText(record.type)}任务: ${record.projectName}`,
      status: record.status || 'success'
    }))
    
    // 更新统计数据
    const today = new Date().toDateString()
    const todayRecords = allRecords.filter(record => 
      new Date(record.timestamp).toDateString() === today
    )
    
    stats.value = {
      totalProjects: new Set(allRecords.map(r => r.projectName)).size,
      runningTasks: allRecords.filter(r => r.status === 'running').length,
      todayPredictions: todayRecords.filter(r => r.type === 'predict').length
    }
    
  } catch (error) {
    console.error('加载最近活动失败:', error)
  } finally {
    loading.value = false
  }
}

// 刷新系统状态
const refreshSystemStatus = async () => {
  await systemStore.checkHealth()
  await systemStore.getHeartbeatStatus()
}

onMounted(async () => {
  // 初始化数据库
  try {
    await db.init()
    await loadRecentActivities()
  } catch (error) {
    console.error('数据库初始化失败:', error)
  }
  
  // 刷新系统状态
  await refreshSystemStatus()
  
  // 设置定时刷新
  refreshTimer = setInterval(() => {
    refreshSystemStatus()
    loadRecentActivities()
  }, 30000) // 每30秒刷新一次
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.quick-actions {
  display: flex;
  gap: 15px;
  margin-top: 15px;
  flex-wrap: wrap;
}

.quick-actions .el-button {
  flex: 1;
  min-width: 120px;
}

@media (max-width: 768px) {
  .quick-actions {
    flex-direction: column;
  }
  
  .quick-actions .el-button {
    flex: none;
  }
}
</style>