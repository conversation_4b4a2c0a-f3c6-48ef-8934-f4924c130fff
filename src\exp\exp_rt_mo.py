"""
多输出实时预测 - 使用APScheduler优化
"""
import os
import sys
import time

from datetime import datetime

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.executors.pool import ThreadPoolExecutor
from industrytslib.core import get_realtime_predictor
from industrytslib.utils.readconfig import read_config_toml
from industrytslib.utils.logutils import get_logger

# 配置日志
logger = get_logger(
    logger_name="rt_mo",
    logger_type="exp",
    level="INFO",
    console_level="INFO",
    rotation="00:00",
    retention="14 days"
)

# Add both current directory and parent directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.extend([current_dir, parent_dir])
os.chdir(parent_dir)  # 切换到项目根目录

# 预测模型列表
pred_list = [
    "回转窑强度样本_回转窑强度样本_CNN",
    "水泥A磨强度样本_水泥A磨强度样本_CNN",
    "水泥磨二线强度样本_水泥磨二线强度样本_CNN",
]

# 读取配置文件
config = read_config_toml("config/database_config.toml")

def predictor_task(predictor, project_name):
    """单个预测器的任务函数"""
    try:
        logger.info(f"执行预测任务: {project_name}")
        predictor.run()
        logger.info(f"预测任务完成: {project_name}")
    except Exception as e:
        logger.error(f"预测任务出错 {project_name}: {str(e)}")

def main_with_scheduler():
    """使用APScheduler进行任务调度的主函数"""
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # 创建预测器列表
    predictor_list = []
    for project_name in pred_list:
        try:
            predictor = get_realtime_predictor(project_name, config, "classic_mo")
            logger.info(f"任务 {project_name} 创建成功!")
            predictor_list.append((predictor, project_name))
        except Exception as e:
            logger.error(f"创建预测器 {project_name} 失败: {str(e)}")
    
    # 配置调度器
    executors = {
        'default': ThreadPoolExecutor(max_workers=len(predictor_list) + 2)
    }
    
    scheduler = BackgroundScheduler(executors=executors)
    
    # 将每个预测器的任务添加到调度器
    for predictor, project_name in predictor_list:
        scheduler.add_job(
            predictor_task, 
            'interval',
            args=[predictor, project_name],
            seconds=60,
            id=f"predictor_{project_name}",
            max_instances=1,
            next_run_time=datetime.now(),  # 设置立即执行
            coalesce=True,  # 合并执行错过的任务
            misfire_grace_time=30  # 任务错过执行时间的容忍度
        )
    
    try:
        logger.info("启动调度器...")
        scheduler.start()
        
        # 保持程序运行
        while True:
            time.sleep(60)
            
    except (KeyboardInterrupt, SystemExit):
        logger.info("正在关闭调度器...")
        scheduler.shutdown()
        logger.info("调度器已关闭")

if __name__ == "__main__":
    main_with_scheduler()
    