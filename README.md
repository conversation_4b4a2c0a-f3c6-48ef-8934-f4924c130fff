# IndustryAI - 工业智能平台

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.116+-green.svg)](https://fastapi.tiangolo.com/)
[![PyTorch](https://img.shields.io/badge/PyTorch-2.7+-red.svg)](https://pytorch.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Version](https://img.shields.io/badge/Version-2025.07.18-red.svg)](pyproject.toml)

> 🏭 **基于深度学习的工业时间序列智能分析平台**
> 
> 集成先进的时间序列预测、异常检测、故障诊断和工艺参数优化算法,为流程工业提供全方位的智能化解决方案。

IndustryAI是一个专为流程工业设计的智能分析平台,采用最新的深度学习技术,提供毫秒级的实时预测、智能参数优化和全面的异常监控。平台支持多种先进算法,包括TimesNet、Informer、PatchTST、Mamba等,能够处理复杂的工业时间序列数据,为企业的数字化转型和智能制造提供强有力的技术支撑。

## 🎯 核心业务领域

- **时间序列预测**：支持短期和长期的工业过程预测,预测精度行业领先
- **实时异常检测**：多维度数据异常监控,及时发现潜在问题
- **工艺参数优化**：基于强化学习的参数调优,提升生产效率
- **故障诊断分析**：智能故障识别和根因分析,减少停机时间

## ✨ 核心特性

- 🎯 **智能建模**：支持TimesNet、Informer、PatchTST、Mamba等先进时间序列模型,满足不同场景需求
- 🔮 **实时预测**：毫秒级响应的在线预测服务,支持高并发和大规模数据处理
- 🛡️ **异常检测**：多维度工业数据异常监控和预警,基于深度学习的智能检测算法
- ⚡ **参数优化**：基于强化学习的工艺参数智能优化,自动寻找最优操作参数
- 📊 **数据处理**：高性能Polars数据处理引擎,比传统Pandas处理速度提升数倍
- 🔒 **安全防护**：企业级安全认证和权限管理,支持JWT认证和RBAC权限控制
- 🐳 **容器化**：完整的Docker部署方案,支持一键部署和弹性扩容
- 📈 **可视化**：丰富的数据可视化和监控面板,直观展示分析结果和系统状态

## 🚀 快速开始

### 系统要求

- **操作系统**：Linux (推荐 Ubuntu 20.04+) / Windows 10+ / macOS 10.15+
- **Python**：3.11+ (必需)
- **内存**：8GB+ (推荐 16GB+)
- **GPU**：NVIDIA GPU with CUDA 12.8+ (可选,用于加速训练)
- **存储**：20GB+ 可用空间
- **网络**：稳定的互联网连接（用于下载依赖）

### 环境准备

#### 1. 安装Just命令工具 (推荐)

Just是一个现代化的命令运行器,简化了项目的构建和管理流程。

```bash
# Linux/macOS
curl --proto '=https' --tlsv1.2 -sSf https://just.systems/install.sh | bash -s -- --to ~/bin

# Windows (使用Scoop)
scoop install just

# Windows (使用Chocolatey)
choco install just
```

#### 2. 克隆项目

```bash
git clone https://github.com/bahayonghang/IndustryAI.git
cd IndustryAI
```

#### 3. 设置虚拟环境

```bash
# Linux/macOS
just setup-venv
just venv

# Windows
just setup-venv-win
just venv-win
```

#### 4. 安装依赖

```bash
# 同步项目依赖
uv sync

# 安装自定义industrytslib库
just install-deps
```

### 启动服务

```bash
# 启动主服务 (Linux/macOS)
just server

# 启动主服务 (Windows)
just server-win

# 指定工厂名称启动
just server "阳泉工厂"
```

服务启动后,访问 http://localhost:8000 查看Web界面。

## 🎯 主要功能
能

### 模型训练

IndustryAI支持多种深度学习算法的模型训练,可以根据不同的工业场景选择合适的算法。

```bash
# 运行所有训练任务
just train

# 单个项目训练
just train-single "项目名称" 300 "classic"

# 参数说明：
# - 项目名称：要训练的具体项目
# - 间隔时间：训练间隔（秒）,默认300
# - 算法类型：classic/timesnet/informer/patchtst/mamba
```

### 实时预测

提供高性能的实时预测服务,支持毫秒级响应和高并发处理。

```bash
# 运行所有预测任务
just predict

# 单个项目预测
just predict-single "项目名称" 60 "classic_mo"

# 参数说明：
# - 项目名称：要预测的具体项目
# - 间隔时间：预测间隔（秒）,默认60
# - 预测器类型：classic_mo/advanced/ensemble
```

### 参数优化

基于强化学习的工艺参数智能优化,自动寻找最优操作参数。

```bash
# 运行优化决策
just optimize

# 单个项目优化
just optimize-single "项目名称" 600

# 参数说明：
# - 项目名称：要优化的具体项目
# - 间隔时间：优化间隔（秒）,默认600
```

### 监控服务

独立的任务状态监控服务,实时跟踪训练、预测和决策任务的运行情况。

```bash
# 启动监控服务 (Linux/macOS)
just monitor

# 启动监控服务 (Windows)
just monitor-win
```

监控服务启动后,可以通过查看 `logs/task_monitor_service.log` 文件来监控运行日志。

**配置监控间隔**：

```bash
# 在.env文件中配置监控间隔（秒）
echo "TASK_MONITOR_INTERVAL_SECONDS=60" >> .env
```#
# 🏗️ 项目架构

### 技术栈

- **核心语言**：Python 3.11+
- **Web框架**：FastAPI + Uvicorn ASGI服务器
- **机器学习**：PyTorch + industrytslib (自定义时间序列库)
- **数据处理**：Polars (高性能数据处理引擎)
- **任务调度**：APScheduler (异步任务管理)
- **命令行**：Typer (CLI接口)
- **包管理**：uv (现代Python包管理器)
- **任务运行**：Just (跨平台命令运行器)

### 架构设计

IndustryAI采用**模块化单体架构 (Modular Monolith)**,在保持部署简单性的同时实现了良好的模块化设计。

```
┌─────────────────────────────────────────────────────────────┐
│                    IndustryAI 架构图                        │
├─────────────────────────────────────────────────────────────┤
│  Web层        │  FastAPI + Uvicorn                         │
├─────────────────────────────────────────────────────────────┤
│  API层        │  /train  │  /rt     │  /opt               │
│               │  训练API  │  预测API  │  优化API            │
├─────────────────────────────────────────────────────────────┤
│  业务层       │  训练模块  │  预测模块  │  优化模块           │
│               │  异步任务调度 (APScheduler)                 │
├─────────────────────────────────────────────────────────────┤
│  数据层       │  Polars数据处理 + industrytslib算法库      │
├─────────────────────────────────────────────────────────────┤
│  基础设施     │  心跳检测  │  日志系统  │  配置管理           │
└─────────────────────────────────────────────────────────────┘
```

### 目录结构

```
IndustryAI/
├── src/backend/              # FastAPI应用核心
│   ├── app.py               # 应用入口和生命周期管理
│   ├── main.py              # CLI接口 (Typer)
│   ├── routers/             # API路由模块
│   │   ├── train.py         # 训练相关API
│   │   ├── realtime_predict.py  # 实时预测API
│   │   └── decision_making.py   # 决策优化API
│   ├── api/                 # Pydantic数据模型
│   ├── tasks/               # 后台任务实现
│   ├── utils/               # 工具模块
│   └── middleware/          # 中间件
├── src/frontend/            # Web前端界面
├── config/                  # TOML配置文件
├── scripts/                 # Linux/macOS脚本
├── scripts_windows/         # Windows批处理脚本
├── libs/                    # 自定义industrytslib库
└── docs/                    # 项目文档
```

### 核心设计原则

- **模块化**：按业务领域划分模块,降低耦合度
- **异步优先**：使用APScheduler实现高效的任务调度
- **跨平台**：支持Linux、macOS、Windows多平台部署
- **高性能**：采用Polars替代Pandas,显著提升数据处理性能
- **可扩展**：支持多种算法和自定义扩展##
 🐳 Docker部署

### 快速部署

```bash
# 构建Docker镜像
docker build -t industryai .

# 运行容器
docker run -d -p 8000:8000 --name industryai-server industryai

# 查看运行状态
docker ps

# 查看日志
docker logs -f industryai-server
```

### 生产环境部署

使用Docker Compose进行生产环境部署：

```bash
# 启动完整服务栈
docker-compose up -d

# 查看服务状态
docker-compose ps

# 停止服务
docker-compose down
```

### 镜像管理

```bash
# 保存镜像为压缩文件
docker save industryai | gzip > industryai.tar.gz

# 从压缩文件加载镜像
gunzip -c industryai.tar.gz | docker load

# 验证镜像
docker images | grep industryai
```

### 环境变量配置

在生产环境中,可以通过环境变量配置系统参数：

```bash
# 创建环境配置文件
cat > .env << EOF
# 工厂配置
PLANT=阳泉工厂

# 监控配置
TASK_MONITOR_INTERVAL_SECONDS=60

# 心跳检测配置
HEARTBEAT_ENABLED=True
HEARTBEAT_UPDATE_INTERVAL=10
HEARTBEAT_TIMEOUT=30

# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost:5432/industryai
EOF

# 使用环境文件启动容器
docker run -d --env-file .env -p 8000:8000 industryai
```## 🔒 
安全特性

IndustryAI提供企业级的安全保障,确保工业数据和系统的安全性。

### 核心安全功能

- **代码加密**：使用PyArmor进行源码保护,防止核心算法泄露
- **硬件绑定**：基于硬件ID的许可证验证,确保授权使用
- **身份认证**：JWT令牌认证机制,支持用户身份验证
- **权限控制**：RBAC (基于角色的访问控制) 权限管理
- **数据加密**：敏感数据加密存储和传输
- **API安全**：FastAPI Guard安全中间件,IP控制和攻击检测

### 许可证管理

#### 1. 获取硬件ID

```bash
# 获取当前机器的硬件ID
python src/backend/licence/license_tool.py get-id
```

#### 2. 生成授权文件

```bash
# 生成标准授权（365天）
python src/backend/licence/license_tool.py generate --days 365 --hardware-id <hardware_id>

# 生成试用版授权（30天）
python src/backend/licence/license_tool.py generate --days 30 --trial

# 生成带硬件绑定的试用版授权
python src/backend/licence/license_tool.py generate --days 30 --hardware-id <hardware_id> --trial
```

#### 3. 打包加密程序

```bash
# 打包标准版
python src/backend/licence/license_tool.py pack

# 打包带试用期的版本
python src/backend/licence/license_tool.py pack --trial-days 30
```

### 安全配置建议

1. **生产环境部署**：
   - 使用HTTPS协议
   - 配置防火墙规则
   - 定期更新系统和依赖

2. **数据保护**：
   - 定期备份重要数据
   - 使用加密存储敏感信息
   - 实施数据访问审计

3. **网络安全**：
   - 限制API访问IP范围
   - 使用VPN或专网连接
   - 监控异常访问行为## 
📖 文档导航

### 用户文档

- 📚 **[用户指南](docs/docs/user_guide.md)** - 详细的功能使用说明和操作指导
- 🚀 **[快速入门](docs/docs/quickstart.md)** - 5分钟快速上手指南
- ⚙️ **[配置说明](docs/docs/configuration.md)** - 环境变量和配置选项详解
- 🛠️ **[Just命令指南](docs/docs/just_guide.md)** - 完整的命令行工具使用手册

### 开发文档

- 🔧 **[开发者指南](docs/docs/developer_guide.md)** - 开发环境搭建和贡献指南
- 📋 **[API文档](docs/docs/api_doc/)** - 完整的REST API接口文档
- 🏗️ **[架构文档](docs/docs_ai/)** - 详细的系统架构和设计说明
- 📊 **[算法文档](docs/docs/algorithms.md)** - 时间序列算法原理和使用

### 部署文档

- 🚀 **[部署指南](docs/docs/deploy/)** - 生产环境部署最佳实践
- 🐳 **[Docker指南](docs/docs/docker_guide.md)** - 容器化部署详细说明
- 🔐 **[安全配置](docs/docs/security_config.md)** - 安全设置和最佳实践
- 💓 **[心跳监控](docs/docs/heartbeat_monitor.md)** - 系统健康监控配置

### 专业文档

- 🏭 **[工业应用案例](docs/docs/industrial_cases.md)** - 实际工业场景应用案例
- 📈 **[性能优化](docs/docs/performance.md)** - 系统性能调优指南
- 🔍 **[故障排除](docs/docs/troubleshooting.md)** - 常见问题解决方案
- 📝 **[更新日志](CHANGELOG.md)** - 版本更新记录和新特性说明## 🔧 故障排除


### 常见问题

#### 环境配置问题

**问题**：Python版本不兼容
```bash
# 检查Python版本
python --version

# 应该显示Python 3.11+
# 如果版本过低,请升级Python或使用pyenv管理多版本
```

**问题**：虚拟环境创建失败
```bash
# 清理并重新创建虚拟环境
rm -rf .venv
just setup-venv
```

**问题**：依赖安装失败
```bash
# 检查uv是否正确安装
uv --version

# 清理缓存并重新安装
uv cache clean
uv sync --reinstall
```

#### 服务启动问题

**问题**：端口8000被占用
```bash
# 检查端口占用情况
netstat -tulpn | grep 8000

# 或者使用其他端口启动
uvicorn src.backend.app:app --host 0.0.0.0 --port 8001
```

**问题**：授权文件无效
```bash
# 检查授权文件是否存在
ls -la licenses/

# 重新生成授权文件
python src/backend/licence/license_tool.py generate --days 30 --trial
```

#### 功能使用问题

**问题**：训练任务失败
```bash
# 查看训练日志
tail -f logs/train.log

# 检查数据文件是否存在
ls -la data/

# 验证配置文件
cat config/project_config.toml
```

**问题**：预测服务无响应
```bash
# 检查服务状态
curl http://localhost:8000/health

# 查看系统资源使用
htop
free -h
```

### 系统检查命令

```bash
# 项目状态检查
just status

# 查看所有可用命令
just help

# 检查配置文件
find config/ -name "*.toml" -exec echo "=== {} ===" \; -exec cat {} \;

# 查看日志文件
find logs/ -name "*.log" -exec tail -n 20 {} \;
```

### 性能诊断

```bash
# 内存使用情况
free -h

# 磁盘空间检查
df -h

# GPU状态检查（如果有GPU）
nvidia-smi

# 网络连接检查
ping -c 4 google.com
```

## 💬 技术支持

### 获取帮助

- 📖 **文档问题**：查看对应章节的详细说明
- 🐛 **Bug报告**：提交issue到项目仓库
- 💬 **技术讨论**：联系开发团队
- 🔧 **故障排除**：参考上述故障排除章节

### 联系方式

- **项目仓库**：[https://github.com/bahayonghang/IndustryAI](https://github.com/bahayonghang/IndustryAI)
- **技术支持**：<EMAIL>
- **问题反馈**：通过GitHub Issues提交

### 社区支持

- 加入技术交流群获取实时帮助
- 参与开源社区讨论
- 分享使用经验和最佳实践## 🤝 贡献
指南

我们欢迎社区贡献！无论是bug修复、新功能开发还是文档改进,都非常感谢您的参与。

### 开发环境搭建

1. **Fork项目**
   ```bash
   # Fork项目到您的GitHub账户
   # 然后克隆您的fork
   git clone https://github.com/YOUR_USERNAME/IndustryAI.git
   cd IndustryAI
   ```

2. **设置开发环境**
   ```bash
   # 创建开发分支
   git checkout -b feature/your-feature-name
   
   # 设置虚拟环境
   just setup-venv
   just venv
   
   # 安装开发依赖
   uv sync --group dev
   ```

3. **配置开发工具**
   ```bash
   # 安装pre-commit钩子
   pre-commit install
   
   # 运行代码格式化
   black src/
   isort src/
   ```

### 开发规范

#### 代码规范

- **Python代码**：遵循PEP 8规范,使用black进行格式化
- **文件命名**：Python文件使用snake_case,配置文件使用kebab-case
- **导入规范**：项目根目录使用绝对导入,模块内使用相对导入
- **类型注解**：新代码必须包含完整的类型注解

#### 提交规范

```bash
# 提交信息格式
git commit -m "feat: 添加新的时间序列预测算法"
git commit -m "fix: 修复训练任务内存泄漏问题"
git commit -m "docs: 更新API文档"

# 提交类型：
# feat: 新功能
# fix: 修复bug
# docs: 文档更新
# style: 代码格式调整
# refactor: 代码重构
# test: 测试相关
# chore: 构建过程或辅助工具的变动
```

#### 分支管理

- `main`：主分支,保持稳定
- `develop`：开发分支,集成新功能
- `feature/*`：功能分支
- `hotfix/*`：紧急修复分支

### 贡献流程

1. **创建Issue**：描述要解决的问题或要添加的功能
2. **开发代码**：在feature分支上进行开发
3. **编写测试**：为新功能编写相应的测试用例
4. **更新文档**：更新相关文档和README
5. **提交PR**：创建Pull Request并描述变更内容
6. **代码审查**：等待维护者审查和反馈
7. **合并代码**：审查通过后合并到主分支

### 测试指南

```bash
# 运行单元测试
pytest tests/

# 运行集成测试
pytest tests/integration/

# 检查代码覆盖率
pytest --cov=src tests/

# 运行性能测试
pytest tests/performance/
```

### 文档贡献

- 使用中文编写用户文档
- 技术文档保持中英文对照
- 代码注释使用中文
- API文档使用OpenAPI规范#
# 📄 许可证

本项目采用 [MIT许可证](LICENSE)。

```
MIT License

Copyright (c) 2025 Yonghang Li

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 🌟 致谢

感谢以下开源项目和技术社区的支持：

- [FastAPI](https://fastapi.tiangolo.com/) - 现代、快速的Web框架
- [PyTorch](https://pytorch.org/) - 深度学习框架
- [Polars](https://pola.rs/) - 高性能数据处理库
- [Just](https://github.com/casey/just) - 命令运行器
- [uv](https://github.com/astral-sh/uv) - Python包管理器

## 📊 项目统计

- **开发语言**：Python
- **代码行数**：50,000+ 行
- **支持算法**：4+ 种时间序列算法
- **文档页数**：100+ 页
- **测试覆盖率**：85%+

---

<div align="center">

**🏭 IndustryAI - 让工业更智能**

[![GitHub stars](https://img.shields.io/github/stars/bahayonghang/IndustryAI?style=social)](https://github.com/bahayonghang/IndustryAI/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/bahayonghang/IndustryAI?style=social)](https://github.com/bahayonghang/IndustryAI/network/members)
[![GitHub issues](https://img.shields.io/github/issues/bahayonghang/IndustryAI)](https://github.com/bahayonghang/IndustryAI/issues)

**如果这个项目对您有帮助,请给我们一个⭐️**

</div>