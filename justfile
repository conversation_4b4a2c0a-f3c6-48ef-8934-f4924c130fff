# IndustryAI Project Justfile
# 工业AI项目统一命令管理

# 默认命令 - 显示帮助
default: help

# 激活虚拟环境
activate-venv:
    #!/usr/bin/env bash
    set -euo pipefail
    
    echo "🐍 激活Python虚拟环境..."
    
    # 检查.venv目录是否存在
    if [ ! -d ".venv" ]; then
        echo "❌ .venv目录不存在,请先创建虚拟环境"
        echo "💡 提示: python -m venv .venv"
        exit 1
    fi
    
    # 检查激活脚本是否存在
    if [ ! -f ".venv/bin/activate" ]; then
        echo "❌ 虚拟环境激活脚本不存在"
        exit 1
    fi
    
    echo "✅ 虚拟环境已找到"
    echo "📝 要激活虚拟环境,请运行以下命令:"
    echo "   source .venv/bin/activate"
    echo ""
    echo "💡 或者使用以下命令直接激活并进入shell:"
    echo "   bash --rcfile <(echo '. ~/.bashrc; source .venv/bin/activate')"
    
    # 显示虚拟环境信息
    if [ -f ".venv/pyvenv.cfg" ]; then
        echo ""
        echo "📊 虚拟环境信息:"
        cat .venv/pyvenv.cfg
    fi

# 创建并激活虚拟环境
setup-venv:
    #!/usr/bin/env bash
    set -euo pipefail
    
    echo "🐍 设置Python虚拟环境..."
    
    # 检查Python是否可用
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3未找到,请先安装Python3"
        exit 1
    fi
    
    # 如果.venv目录不存在,创建它
    if [ ! -d ".venv" ]; then
        echo "📦 创建虚拟环境..."
        python3 -m venv .venv
        echo "✅ 虚拟环境创建成功"
    else
        echo "✅ 虚拟环境已存在"
    fi
    
    # 激活虚拟环境并安装基础依赖
    echo "🔧 激活虚拟环境并更新pip..."
    source .venv/bin/activate && pip install --upgrade pip
    
    echo "✅ 虚拟环境设置完成"
    echo "📝 要激活虚拟环境,请运行: just activate-venv"

# 进入激活了虚拟环境的shell
venv:
    #!/usr/bin/env bash
    set -euo pipefail
    
    echo "🐍 进入虚拟环境shell..."
    
    # 检查.venv目录是否存在
    if [ ! -d ".venv" ]; then
        echo "❌ .venv目录不存在,请先运行: just setup-venv"
        exit 1
    fi
    
    # 检查激活脚本是否存在
    if [ ! -f ".venv/bin/activate" ]; then
        echo "❌ 虚拟环境激活脚本不存在"
        exit 1
    fi
    
    echo "✅ 启动虚拟环境shell (输入 'exit' 退出)"
    bash --rcfile <(echo '. ~/.bashrc; source .venv/bin/activate; echo "🐍 虚拟环境已激活 (.venv)"')

# Windows版本 - 激活虚拟环境
activate-venv-win:
    @powershell -Command "Write-Host '🐍 激活Python虚拟环境 (Windows)...'"
    @powershell -Command "if (-not (Test-Path '.venv')) { Write-Host '❌ .venv目录不存在,请先创建虚拟环境'; Write-Host '💡 提示: python -m venv .venv'; exit 1 }"
    @powershell -Command "if (-not (Test-Path '.venv\Scripts\activate.ps1')) { Write-Host '❌ 虚拟环境激活脚本不存在'; exit 1 }"
    @powershell -Command "Write-Host '✅ 虚拟环境已找到'"
    @powershell -Command "Write-Host '📝 要激活虚拟环境,请运行以下命令:'"
    @powershell -Command "Write-Host '   .venv\Scripts\activate.ps1'"
    @powershell -Command "Write-Host ''"
    @powershell -Command "Write-Host '💡 或者使用以下命令直接激活并进入PowerShell:'"
    @powershell -Command "Write-Host '   powershell -ExecutionPolicy Bypass -File .venv\Scripts\activate.ps1'"
    @powershell -Command "if (Test-Path '.venv\pyvenv.cfg') { Write-Host ''; Write-Host '📊 虚拟环境信息:'; Get-Content '.venv\pyvenv.cfg' }"

# Windows版本 - 创建并激活虚拟环境
setup-venv-win:
    @powershell -Command "Write-Host '🐍 设置Python虚拟环境 (Windows)...'"
    @powershell -Command "try { python --version | Out-Null } catch { Write-Host '❌ Python未找到,请先安装Python'; exit 1 }"
    @powershell -Command "if (-not (Test-Path '.venv')) { Write-Host '📦 创建虚拟环境...'; python -m venv .venv; Write-Host '✅ 虚拟环境创建成功' } else { Write-Host '✅ 虚拟环境已存在' }"
    @powershell -Command "Write-Host '🔧 激活虚拟环境并更新pip...'"
    @powershell -Command ".venv\Scripts\activate.bat; pip install --upgrade pip"
    @powershell -Command "Write-Host '✅ 虚拟环境设置完成'"
    @powershell -Command "Write-Host '📝 要激活虚拟环境,请运行: just activate-venv-win'"

# Windows版本 - 进入激活了虚拟环境的shell
venv-win:
    @powershell -Command "Write-Host '🐍 进入虚拟环境shell (Windows)...'"
    @powershell -Command "if (-not (Test-Path '.venv')) { Write-Host '❌ .venv目录不存在,请先运行: just setup-venv-win'; exit 1 }"
    @powershell -Command "if (-not (Test-Path '.venv\Scripts\activate.ps1')) { Write-Host '❌ 虚拟环境激活脚本不存在'; exit 1 }"
    @powershell -Command "Write-Host '✅ 启动虚拟环境shell (输入 exit 退出)'"
    @powershell -ExecutionPolicy Bypass -Command "& '.venv\Scripts\activate.ps1'; Write-Host '🐍 虚拟环境已激活 (.venv)'; powershell"

# 跨平台依赖同步
sync:
    #!/usr/bin/env bash
    set -euo pipefail
    
    echo "📦 同步项目依赖..."
    
    # 检查uv是否安装
    if ! command -v uv &> /dev/null; then
        echo "❌ uv未找到,请先安装uv"
        echo "💡 安装提示: curl -LsSf https://astral.sh/uv/install.sh | sh"
        exit 1
    fi
    
    # 检测操作系统
    OS_TYPE=$(uname -s 2>/dev/null || echo "Unknown")
    
    if [[ "$OS_TYPE" == "Linux" ]]; then
        echo "🐧 检测到Linux系统,使用 uv sync --all-extras"
        uv sync --all-extras
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]] || [[ "$OS_TYPE" == "MINGW"* ]]; then
        echo "🪟 检测到Windows系统,使用 uv sync"
        uv sync
    else
        echo "🍎 检测到Mac或其他系统,使用 uv sync"
        uv sync
    fi
    
    if [ $? -eq 0 ]; then
        echo "✅ 依赖同步完成!"
    else
        echo "❌ 依赖同步失败,请检查错误信息"
        exit 1
    fi

# 启动主服务器
server plant="阳泉":
    #!/usr/bin/env bash
    set -euo pipefail
    
    echo "🚀 启动IndustryAI服务器..."
    
    # 设置工厂环境变量
    export PLANT="{{plant}}"
    
    # 调用scripts目录中的启动脚本
    bash scripts/run.sh

# Windows版本 - 启动主服务器
server-win plant="阳泉":
    @powershell -Command "Write-Host '🚀 启动IndustryAI服务器 (Windows)...'"
    @powershell -Command "Write-Host '设置工厂: {{plant}}'"
    @powershell -Command "$env:PLANT='{{plant}}'; Start-Process -FilePath '.\scripts_windows\run_server.bat' -Wait"

# 训练模式 - 运行所有训练任务
train:
    #!/usr/bin/env bash
    set -euo pipefail
    
    echo "🎯 启动训练模式..."
    
    # 调用scripts目录中的训练脚本
    bash scripts/run_train.sh

# Windows版本 - 训练模式
train-win:
    @powershell -Command "Write-Host '🎯 启动训练模式 (Windows)...'"
    @powershell -Command "& '.\scripts_windows\run_train.bat'"

# 单个训练任务
train-single project_name interval="300" algorithm="classic":
    #!/usr/bin/env bash
    set -euo pipefail
    
    echo "🎯 启动单个训练任务..."
    
    # 设置环境变量
    export PYTHONPATH=${PYTHONPATH:+$PYTHONPATH:}$(pwd)
    
    echo "请求训练项目: {{project_name}}"
    echo "  - 间隔时间: {{interval}}秒"
    echo "  - 算法类型: {{algorithm}}"
    
    python src/backend/requestes/requests_train.py \
        --project-name "{{project_name}}" \
        --interval "{{interval}}" \
        --algorithm-type "{{algorithm}}"

# 预测模式 - 运行所有预测任务
predict:
    #!/usr/bin/env bash
    set -euo pipefail
    
    echo "🔮 启动预测模式..."
    
    # 调用scripts目录中的预测脚本
    bash scripts/run_pred.sh

# Windows版本 - 预测模式
predict-win:
    @powershell -Command "Write-Host '🔮 启动预测模式 (Windows)...'"
    @powershell -Command "& '.\scripts_windows\run_pred.bat'"

# 单个预测任务
predict-single project_name interval="60" predictor_type="classic_mo":
    #!/usr/bin/env bash
    set -euo pipefail
    
    echo "🔮 启动单个预测任务..."
    
    # 设置环境变量
    export PYTHONPATH=${PYTHONPATH:+$PYTHONPATH:}$(pwd)
    
    echo "请求实时预测项目: {{project_name}}"
    echo "  - 间隔时间: {{interval}}秒"
    echo "  - 预测器类型: {{predictor_type}}"
    
    python src/backend/requestes/requests_predictor.py \
        --project-name "{{project_name}}" \
        --interval "{{interval}}" \
        --predictor-type "{{predictor_type}}"

# 优化决策模式
optimize:
    #!/usr/bin/env bash
    set -euo pipefail
    
    echo "⚡ 启动优化决策模式..."
    
    # 调用scripts目录中的优化脚本
    bash scripts/run_opt.sh

# Windows版本 - 优化决策模式
optimize-win:
    @powershell -Command "Write-Host '⚡ 启动优化决策模式 (Windows)...'"
    @powershell -Command "& '.\scripts_windows\run_opt.bat'"

# 单个优化任务
optimize-single project_name interval="600":
    #!/usr/bin/env bash
    set -euo pipefail
    
    echo "⚡ 启动单个优化任务..."
    
    # 设置环境变量
    export PYTHONPATH=${PYTHONPATH:+$PYTHONPATH:}$(pwd)
    
    echo "请求决策优化项目: {{project_name}}"
    echo "  - 间隔时间: {{interval}}秒"
    
    python src/backend/requestes/requests_opt.py \
        --project-name "{{project_name}}" \
        --interval "{{interval}}"

# 启动任务状态监控
monitor:
    #!/usr/bin/env bash
    set -euo pipefail

    echo "📊 启动任务状态监控服务..."

    # 调用scripts目录中的监控脚本
    bash scripts/run_monitor.sh

# Windows版本 - 启动任务状态监控
monitor-win:
    @powershell -Command "Write-Host '📊 启动任务状态监控服务 (Windows)...'"
    @powershell -Command "& '.\scripts_windows\run_monitor.bat'"

# 心跳检测相关命令
# 构建心跳检测程序
build-heartbeat:
    #!/usr/bin/env bash
    set -e
    
    echo "🔨 构建心跳检测程序..."
    
    # 调用scripts目录中的构建脚本
    bash scripts/build_heartbeat.sh

# 启动心跳监控
start-heartbeat:
    #!/usr/bin/env bash
    set -e
    
    echo "💓 启动心跳监控程序..."
    
    # 调用scripts目录中的心跳监控脚本
    bash scripts/start_heartbeat_monitor.sh

# 项目打包
package:
    #!/usr/bin/env bash
    set -euo pipefail
    
    echo "📦 打包项目..."
    
    # 调用scripts目录中的打包脚本
    bash scripts/zip.sh
    
    echo "✅ 打包完成: industryai.tar.gz"
    ls -lh industryai.tar.gz

# 安装依赖库
install-deps:
    #!/usr/bin/env bash
    set -euo pipefail
    
    echo "📚 安装industrytslib依赖..."
    
    # 切换到libs目录
    cd libs
    
    # 查找industrytslib的wheel文件
    WHL_FILE=$(find . -name "industrytslib-*.whl" -type f | head -1)
    
    if [ -z "$WHL_FILE" ]; then
        echo "错误: 在libs目录中未找到industrytslib的wheel文件"
        echo "请确保libs文件夹中包含industrytslib-*.whl文件"
        exit 1
    fi
    
    # 获取文件名(不含路径)
    WHL_FILENAME=$(basename "$WHL_FILE")
    echo "找到wheel文件: $WHL_FILENAME"
    
    # 安装wheel文件及其依赖
    echo "正在安装 $WHL_FILENAME 及其依赖组合..."
    echo "依赖组合: async, data_storage, data_vis, ml"
    
    pip install "${WHL_FILENAME}[async,data_storage,data_vis,ml]" --force --no-deps
    
    if [ $? -eq 0 ]; then
        echo "✅ 安装成功！"
        echo "已安装的包信息:"
        pip show industrytslib
    else
        echo "❌ 安装失败,请检查错误信息"
        exit 1
    fi

# 清理安装的依赖
clean-deps:
    @echo "🧹 卸载industrytslib..."
    pip uninstall industrytslib -y || echo "industrytslib未安装或卸载失败"

# 列出可用的wheel文件
list-wheels:
    @echo "📋 可用的wheel文件:"
    @find libs -name "industrytslib-*.whl" -type f || echo "未找到wheel文件"

# 开发相关命令
# 运行实验脚本
exp script_name:
    #!/usr/bin/env bash
    set -euo pipefail
    
    echo "🧪 运行实验脚本: {{script_name}}"
    
    # 设置环境变量
    export PYTHONPATH=${PYTHONPATH:+$PYTHONPATH:}$(pwd)
    
    python src/exp/{{script_name}}.py

# 检查项目状态
status:
    #!/usr/bin/env bash
    set -euo pipefail
    
    echo "📊 项目状态检查"
    echo "=================="
    
    # Python环境
    echo "Python环境:"
    # 检查当前conda环境
    current_env=$(conda info --envs | grep '*' | awk '{print $1}' 2>/dev/null || echo "未知")
    echo "当前使用的python环境为: $current_env"
    
    # 检查python路径
    python_path=$(which python)
    echo "python路径: $python_path"
    
    # 设置项目路径
    export PYTHONPATH=${PYTHONPATH:+$PYTHONPATH:}$(pwd)
    echo "项目路径: $PYTHONPATH"
    echo ""
    
    # 依赖检查
    echo "依赖库状态:"
    pip show industrytslib 2>/dev/null || echo "industrytslib未安装"
    echo ""
    
    # 心跳程序状态
    echo "心跳程序状态:"
    if [ -f "src/heartbeat_checker/target/release/heartbeat_checker" ]; then
        echo "✅ 心跳检测程序已构建"
    else
        echo "❌ 心跳检测程序未构建"
    fi
    echo ""
    
    # 配置文件检查
    echo "配置文件状态:"
    [ -f "config/database_config.toml" ] && echo "✅ 数据库配置存在" || echo "❌ 数据库配置缺失"
    [ -f ".env" ] && echo "✅ 环境配置存在" || echo "❌ 环境配置缺失"
    echo ""
    
    # 项目文件
    echo "项目结构:"
    echo "  - 源码目录: $([ -d 'src' ] && echo '✅' || echo '❌')"
    echo "  - 配置目录: $([ -d 'config' ] && echo '✅' || echo '❌')"
    echo "  - 脚本目录: $([ -d 'scripts' ] && echo '✅' || echo '❌')"
    echo "  - 依赖目录: $([ -d 'libs' ] && echo '✅' || echo '❌')"

# 显示帮助信息
help:
    @echo "🏭 IndustryAI 项目命令帮助"
    @echo "============================="
    @echo ""
    @echo "🐍 环境管理:"
    @echo "  just setup-venv               - 创建并设置.venv虚拟环境 (Linux/macOS)"
    @echo "  just setup-venv-win           - 创建并设置.venv虚拟环境 (Windows)"
    @echo "  just activate-venv            - 显示激活.venv虚拟环境的方法 (Linux/macOS)"
    @echo "  just activate-venv-win        - 显示激活.venv虚拟环境的方法 (Windows)"
    @echo "  just venv                     - 进入激活了虚拟环境的shell (Linux/macOS)"
    @echo "  just venv-win                 - 进入激活了虚拟环境的shell (Windows)"
    @echo "  just sync                     - 跨平台依赖同步 (Linux用--all-extras)"
    @echo ""
    @echo "🚀 服务管理:"
    @echo "  just server [plant]          - 启动主服务器 (默认plant=阳泉)"
    @echo "  just server-win [plant]      - 启动主服务器 (Windows版本)"
    @echo ""
    @echo "🎯 训练相关:"
    @echo "  just train                    - 运行所有训练任务"
    @echo "  just train-win                - 运行所有训练任务 (Windows版本)"
    @echo "  just train-single <name> [interval] [algorithm] - 运行单个训练任务"
    @echo ""
    @echo "🔮 预测相关:"
    @echo "  just predict                  - 运行所有预测任务"
    @echo "  just predict-win              - 运行所有预测任务 (Windows版本)"
    @echo "  just predict-single <name> [interval] [type] - 运行单个预测任务"
    @echo ""
    @echo "⚡ 优化相关:"
    @echo "  just optimize                 - 运行所有优化任务"
    @echo "  just optimize-win             - 运行所有优化任务 (Windows版本)"
    @echo "  just optimize-single <name> [interval] - 运行单个优化任务"
    @echo ""
    @echo "📊 监控服务:"
    @echo "  just monitor                  - 启动任务状态监控服务"
    @echo "  just monitor-win              - 启动任务状态监控服务 (Windows版本)"
    @echo ""
    @echo "📦 项目管理:"
    @echo "  just package                  - 打包项目"
    @echo "  just install-deps             - 安装依赖库"
    @echo "  just clean-deps               - 清理依赖库"
    @echo "  just list-wheels              - 列出可用wheel文件"
    @echo ""
    @echo "🧪 开发工具:"
    @echo "  just exp <script_name>        - 运行实验脚本"
    @echo "  just status                   - 检查项目状态"
    @echo "  just help                     - 显示此帮助信息"
    @echo ""
    @echo "📝 使用示例:"
    @echo "  just setup-venv               # 首次设置虚拟环境 (Linux/macOS)"
    @echo "  just setup-venv-win           # 首次设置虚拟环境 (Windows)"
    @echo "  just sync                     # 同步项目依赖 (跨平台)"
    @echo "  just venv-shell               # 进入虚拟环境shell (Linux/macOS)"
    @echo "  just venv-shell-win           # 进入虚拟环境shell (Windows)"
    @echo "  just server                   # 启动服务器 (Linux/macOS)"
    @echo "  just server-win               # 启动服务器 (Windows)"
    @echo "  just train                    # 运行所有训练 (Linux/macOS)"
    @echo "  just train-win                # 运行所有训练 (Windows)"
    @echo "  just predict-single \"项目名\" 60 classic_mo"
    @echo "  just exp exp_train            # 运行训练实验"