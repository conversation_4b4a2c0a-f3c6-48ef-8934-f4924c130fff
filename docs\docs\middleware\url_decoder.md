# URL解码中间件

## 概述

URL解码中间件是为了解决Java客户端与FastAPI后端之间的中文参数传输问题而设计的。当Java客户端使用`URLEncoder.encode()`对JSON请求体中的中文参数进行URL编码时,此中间件会自动检测并解码这些参数,确保FastAPI能正确处理中文内容。

## 功能特性

### 核心功能
- **自动检测**: 智能识别JSON请求体中的URL编码字符串
- **递归解码**: 支持嵌套JSON结构的深度解码
- **类型安全**: 只处理字符串类型,保持其他数据类型不变
- **错误处理**: 完善的异常处理和日志记录
- **性能优化**: 只处理必要的请求,避免不必要的性能开销

### 处理范围
- **请求方法**: POST、PUT、PATCH
- **内容类型**: `application/json`
- **编码格式**: UTF-8 URL编码

## 使用场景

### 典型问题
Java客户端发送包含中文的JSON数据时：

```java
// Java客户端代码
JSONObject json = new JSONObject();
json.put("project_name", URLEncoder.encode("中文项目", "UTF-8"));
// 结果: {"project_name": "%E4%B8%AD%E6%96%87%E9%A1%B9%E7%9B%AE"}
```

### 解决方案
中间件自动将URL编码的参数解码：

```json
// 中间件处理前
{"project_name": "%E4%B8%AD%E6%96%87%E9%A1%B9%E7%9B%AE"}

// 中间件处理后
{"project_name": "中文项目"}
```

## 安装配置

### 1. 导入中间件

在`app.py`中导入URL解码中间件：

```python
from middleware.url_decoder import URLDecoderMiddleware
```

### 2. 注册中间件

在创建FastAPI应用后,路由注册前添加中间件：

```python
app = FastAPI(
    title='Industry AI Server',
    description='工业AI服务器',
    version='2025.07.preview',
    lifespan=lifespan,
)

# 添加URL解码中间件
app.add_middleware(URLDecoderMiddleware)

# 然后注册路由
app.include_router(train_router, prefix='/train', tags=['训练'])
app.include_router(realtime_predict_router, prefix='/realtime_predict', tags=['实时预测'])
```

### 3. 中间件顺序

⚠️ **重要**: 中间件的添加顺序很重要,URL解码中间件应该在其他业务中间件之前添加,以确保后续中间件接收到的是解码后的数据。

## 使用示例

### Java客户端示例

```java
public class IndustryAIClient {
    public static void sendRequest() throws Exception {
        String url = "http://localhost:8081/realtime_predict/realtime_predict";
        
        // 创建包含中文的JSON数据
        JSONObject json = new JSONObject();
        json.put("project_name", URLEncoder.encode("中文项目", "UTF-8"));
        json.put("interval", 60);
        json.put("predictor_type", "classic");
        
        // 发送HTTP请求
        HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
        conn.setDoOutput(true);
        
        try (OutputStream os = conn.getOutputStream()) {
            byte[] input = json.toString().getBytes("utf-8");
            os.write(input, 0, input.length);
        }
        
        // 读取响应
        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(conn.getInputStream(), "utf-8"))) {
            StringBuilder response = new StringBuilder();
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
            System.out.println(response.toString());
        }
    }
}
```

### Python测试示例

```python
import requests
import urllib.parse

# 模拟Java客户端的URL编码行为
project_name_encoded = urllib.parse.quote("中文项目", encoding='utf-8')

data = {
    "project_name": project_name_encoded,
    "interval": 60,
    "predictor_type": "classic"
}

response = requests.post(
    "http://localhost:8081/realtime_predict/realtime_predict",
    json=data,
    headers={"Content-Type": "application/json; charset=UTF-8"}
)

print(response.json())
```

## 日志监控

### 日志级别
- **DEBUG**: 详细的解码过程信息
- **INFO**: 成功解码的数据记录
- **WARNING**: 解码失败或格式错误
- **ERROR**: 中间件处理异常

### 日志示例

```
[INFO] URL解码中间件已初始化
[DEBUG] 原始JSON数据: {"project_name": "%E4%B8%AD%E6%96%87%E9%A1%B9%E7%9B%AE"}
[DEBUG] URL解码: '%E4%B8%AD%E6%96%87%E9%A1%B9%E7%9B%AE' -> '中文项目'
[INFO] 检测到URL编码数据并已解码: {"project_name": "中文项目"}
```

## 性能考虑

### 优化策略
1. **选择性处理**: 只处理JSON格式的POST/PUT/PATCH请求
2. **智能检测**: 只对包含`%`字符的字符串进行解码尝试
3. **缓存机制**: 避免重复解码相同的字符串
4. **异步处理**: 不阻塞请求处理流程

### 性能影响
- **CPU开销**: 极低,只在检测到URL编码时才进行处理
- **内存开销**: 最小,只在需要时创建新的请求体
- **延迟影响**: 几乎可忽略不计（< 1ms）

## 故障排除

### 常见问题

#### 1. 中文字符仍然显示为编码格式
**原因**: 中间件未正确注册或顺序错误
**解决**: 检查中间件注册代码和顺序

#### 2. JSON解析失败
**原因**: 请求体格式不正确
**解决**: 确保Content-Type为`application/json`

#### 3. 部分字符解码失败
**原因**: 编码格式不是UTF-8
**解决**: 确保Java客户端使用UTF-8编码

### 调试步骤

1. **检查日志**: 查看中间件的DEBUG级别日志
2. **验证请求**: 确认请求头和请求体格式
3. **测试端点**: 使用测试API验证中间件功能
4. **逐步排查**: 从简单的单字符测试开始

## 最佳实践

### 推荐做法
1. **Java客户端优化**: 建议修改Java客户端直接发送UTF-8 JSON,避免URL编码
2. **统一编码**: 确保整个系统使用UTF-8编码
3. **错误处理**: 在API端点中添加适当的错误处理
4. **监控日志**: 定期检查中间件日志,发现潜在问题

### 不推荐做法
1. **过度依赖**: 不要将中间件作为处理所有编码问题的万能解决方案
2. **忽略性能**: 在高并发场景下注意监控性能影响
3. **缺少测试**: 部署前务必进行充分测试

## 技术细节

### 实现原理
1. **请求拦截**: 继承`BaseHTTPMiddleware`拦截HTTP请求
2. **内容检测**: 检查Content-Type和请求方法
3. **JSON解析**: 解析请求体为JSON对象
4. **递归处理**: 遍历JSON结构,解码字符串值
5. **请求重构**: 创建新的请求体并传递给下游

### 核心算法
```python
def _decode_json_recursively(self, data):
    if isinstance(data, dict):
        return {key: self._decode_json_recursively(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [self._decode_json_recursively(item) for item in data]
    elif isinstance(data, str):
        return self._decode_url_string(data)
    else:
        return data
```

## 版本兼容性

- **FastAPI**: >= 0.68.0
- **Python**: >= 3.8
- **Starlette**: >= 0.14.0
- **industrytslib**: 当前版本

## 相关文档

- [FastAPI中间件文档](https://fastapi.tiangolo.com/tutorial/middleware/)
- [Python urllib.parse文档](https://docs.python.org/3/library/urllib.parse.html)
- [IndustryAI API文档](../api_doc/index.md)
- [开发者指南](../developer_guide.md)

## 更新日志

### v1.0.0 (2025-01-XX)
- 初始版本发布
- 支持基本的URL解码功能
- 递归处理嵌套JSON结构
- 完善的错误处理和日志记录

---

如需技术支持或发现问题,请参考[开发者指南](../developer_guide.md)或提交issue到项目仓库。