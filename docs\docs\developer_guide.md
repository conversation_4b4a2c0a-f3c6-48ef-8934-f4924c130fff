# 开发者指南

## 概述

欢迎参与IndustryAI项目的开发！本指南将帮助您了解项目结构、开发流程和贡献方式。

## 项目架构

### 目录结构

```
industryai/
├── src/                          # 源代码目录
│   ├── industryai/              # 主包
│   │   ├── core/                # 核心模块
│   │   │   ├── config.py        # 配置管理
│   │   │   ├── database.py      # 数据库连接
│   │   │   ├── security.py      # 安全认证
│   │   │   └── logging.py       # 日志配置
│   │   ├── models/              # AI模型模块
│   │   │   ├── timeseries/      # 时间序列模型
│   │   │   ├── optimization/    # 优化算法
│   │   │   └── base/            # 基础模型类
│   │   ├── api/                 # API接口
│   │   │   ├── v1/              # API v1版本
│   │   │   ├── middleware/      # 中间件
│   │   │   └── dependencies/    # 依赖注入
│   │   ├── services/            # 业务服务
│   │   │   ├── training/        # 训练服务
│   │   │   ├── prediction/      # 预测服务
│   │   │   └── monitoring/      # 监控服务
│   │   ├── utils/               # 工具模块
│   │   │   ├── data_processing/ # 数据处理
│   │   │   ├── visualization/   # 可视化
│   │   │   └── helpers/         # 辅助函数
│   │   └── schemas/             # 数据模式
│   └── tests/                   # 测试代码
├── docs/                        # 文档
├── scripts/                     # 脚本文件
├── config/                      # 配置文件
├── docker/                      # Docker配置
├── justfile                     # Just命令定义
├── pyproject.toml              # 项目配置
└── README.md                   # 项目说明
```

### 技术栈

- **Web框架**: FastAPI 0.104+
- **异步框架**: asyncio, uvloop
- **数据处理**: Polars 1.30+, NumPy, Pandas
- **机器学习**: PyTorch 2.6+, scikit-learn
- **数据库**: PostgreSQL, Redis, InfluxDB
- **ORM**: SQLAlchemy 2.0+, asyncpg
- **数据验证**: Pydantic 2.0+
- **日志**: loguru
- **测试**: pytest, pytest-asyncio
- **代码质量**: ruff, black, mypy
- **文档**: MkDocs, mkdocstrings

## 开发环境搭建

### 1. 环境要求

- Python 3.11+
- uv (Python包管理器)
- Just (命令运行器)
- Git
- Docker (可选)
- PostgreSQL 14+
- Redis 6+

### 2. 克隆项目

```bash
git clone https://github.com/your-repo/industryai.git
cd industryai
```

### 3. 安装依赖

```bash
# 安装uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 创建虚拟环境并安装依赖
uv venv
source .venv/bin/activate  # Linux/macOS
# 或 .venv\Scripts\activate  # Windows

uv pip install -e ".[dev,test,docs]"
```

### 4. 安装Just

```bash
# Linux/macOS
curl --proto '=https' --tlsv1.2 -sSf https://just.systems/install.sh | bash -s -- --to ~/bin

# 或使用包管理器
# Ubuntu/Debian
sudo apt install just

# macOS
brew install just

# Windows
scoop install just
```

### 5. 配置环境

```bash
# 复制环境配置模板
cp .env.example .env.dev

# 编辑配置文件
vim .env.dev
```

### 6. 初始化数据库

```bash
# 启动数据库服务
just db-start

# 运行数据库迁移
just db-migrate

# 创建测试数据
just db-seed
```

### 7. 启动开发服务器

```bash
# 启动开发服务器
just dev

# 或手动启动
uv run uvicorn src.industryai.main:app --reload --host 0.0.0.0 --port 8000
```

## 开发工作流

### 1. 分支管理

我们使用Git Flow工作流:

```bash
# 主分支
main          # 生产环境代码
develop       # 开发分支

# 功能分支
feature/*     # 新功能开发
bugfix/*      # Bug修复
hotfix/*      # 紧急修复
release/*     # 发布准备
```

### 2. 开发流程

```bash
# 1. 从develop分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/your-feature-name

# 2. 开发功能
# 编写代码...

# 3. 运行测试
just test

# 4. 代码格式化和检查
just lint
just format

# 5. 提交代码
git add .
git commit -m "feat: add your feature description"

# 6. 推送分支
git push origin feature/your-feature-name

# 7. 创建Pull Request
# 在GitHub/GitLab上创建PR到develop分支
```

### 3. 提交信息规范

使用[Conventional Commits](https://www.conventionalcommits.org/)规范:

```bash
# 格式
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]

# 示例
feat(models): add TimesNet model implementation
bugfix(api): fix prediction endpoint validation
docs(readme): update installation instructions
test(services): add unit tests for training service
refactor(core): improve database connection handling
```

**类型说明:**
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 代码规范

### 1. Python代码规范

我们使用以下工具确保代码质量:

```bash
# 代码格式化
just format
# 等同于:
black src/ tests/
ruff format src/ tests/

# 代码检查
just lint
# 等同于:
ruff check src/ tests/
mypy src/
```

### 2. 代码风格指南

#### 命名规范

```python
# 类名:PascalCase
class TimesNetModel:
    pass

# 函数名和变量名:snake_case
def train_model(model_config: dict) -> None:
    training_data = load_data()
    
# 常量:UPPER_SNAKE_CASE
MAX_SEQUENCE_LENGTH = 96
DEFAULT_BATCH_SIZE = 32

# 私有方法:前缀下划线
def _validate_input(self, data: Any) -> bool:
    pass
```

#### 类型注解

```python
from typing import List, Dict, Optional, Union
from pydantic import BaseModel

# 函数类型注解
def process_data(
    data: List[Dict[str, float]], 
    config: Optional[Dict[str, Any]] = None
) -> Dict[str, Union[float, int]]:
    """处理时间序列数据
    
    Args:
        data: 输入数据列表
        config: 可选配置参数
        
    Returns:
        处理结果字典
        
    Raises:
        ValueError: 当数据格式不正确时
    """
    pass

# 类属性类型注解
class ModelConfig(BaseModel):
    model_name: str
    sequence_length: int = 96
    prediction_length: int = 24
    learning_rate: float = 0.001
    batch_size: Optional[int] = None
```

#### 文档字符串

使用Google风格的docstring:

```python
def train_timeseries_model(
    data: pd.DataFrame,
    model_type: str,
    config: ModelConfig
) -> TrainingResult:
    """训练时间序列模型
    
    这个函数用于训练各种类型的时间序列预测模型,支持TimesNet、
    Informer、PatchTST等多种架构。
    
    Args:
        data: 训练数据,包含时间戳和特征列
        model_type: 模型类型,支持 'timesnet', 'informer', 'patchtst'
        config: 模型配置参数
        
    Returns:
        TrainingResult: 包含训练结果的对象,包括:
            - model: 训练好的模型
            - metrics: 训练指标
            - history: 训练历史
            
    Raises:
        ValueError: 当model_type不支持时
        DataError: 当数据格式不正确时
        
    Example:
        >>> data = pd.read_csv('timeseries_data.csv')
        >>> config = ModelConfig(model_name='timesnet', epochs=100)
        >>> result = train_timeseries_model(data, 'timesnet', config)
        >>> print(f"训练完成,损失: {result.metrics['loss']}")
    """
    pass
```

### 3. 错误处理

```python
from industryai.core.exceptions import (
    ModelTrainingError,
    DataValidationError,
    ConfigurationError
)
from loguru import logger

def safe_model_training(data: pd.DataFrame) -> Optional[Model]:
    """安全的模型训练函数"""
    try:
        # 数据验证
        if data.empty:
            raise DataValidationError("训练数据不能为空")
            
        # 模型训练
        model = train_model(data)
        logger.info(f"模型训练成功,准确率: {model.accuracy}")
        return model
        
    except DataValidationError as e:
        logger.error(f"数据验证失败: {e}")
        raise
    except ModelTrainingError as e:
        logger.error(f"模型训练失败: {e}")
        return None
    except Exception as e:
        logger.exception(f"未知错误: {e}")
        raise ModelTrainingError(f"训练过程中发生未知错误: {e}") from e
```

## 测试指南

### 1. 测试结构

```
tests/
├── unit/                    # 单元测试
│   ├── test_models/        # 模型测试
│   ├── test_services/      # 服务测试
│   └── test_utils/         # 工具测试
├── integration/            # 集成测试
│   ├── test_api/          # API测试
│   └── test_database/     # 数据库测试
├── e2e/                   # 端到端测试
├── fixtures/              # 测试数据
└── conftest.py           # pytest配置
```

### 2. 编写测试

```python
# tests/unit/test_models/test_timesnet.py
import pytest
import torch
from unittest.mock import Mock, patch

from industryai.models.timeseries.timesnet import TimesNetModel
from industryai.schemas.model_config import ModelConfig

class TestTimesNetModel:
    """TimesNet模型测试类"""
    
    @pytest.fixture
    def model_config(self):
        """模型配置fixture"""
        return ModelConfig(
            model_name="timesnet",
            sequence_length=96,
            prediction_length=24,
            d_model=512,
            num_layers=3
        )
    
    @pytest.fixture
    def sample_data(self):
        """示例数据fixture"""
        return torch.randn(32, 96, 7)  # batch_size, seq_len, features
    
    def test_model_initialization(self, model_config):
        """测试模型初始化"""
        model = TimesNetModel(model_config)
        assert model.config == model_config
        assert model.d_model == 512
        assert model.num_layers == 3
    
    def test_forward_pass(self, model_config, sample_data):
        """测试前向传播"""
        model = TimesNetModel(model_config)
        output = model(sample_data)
        
        expected_shape = (32, 24, 7)  # batch_size, pred_len, features
        assert output.shape == expected_shape
    
    @patch('industryai.models.timeseries.timesnet.torch.save')
    def test_model_save(self, mock_save, model_config):
        """测试模型保存"""
        model = TimesNetModel(model_config)
        model.save("test_model.pth")
        
        mock_save.assert_called_once()
    
    def test_model_load(self, model_config):
        """测试模型加载"""
        with patch('torch.load') as mock_load:
            mock_load.return_value = {'state_dict': {}, 'config': model_config.dict()}
            
            model = TimesNetModel.load("test_model.pth")
            assert isinstance(model, TimesNetModel)
```

### 3. API测试

```python
# tests/integration/test_api/test_training.py
import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch

from industryai.main import app
from industryai.schemas.training import TrainingRequest

@pytest.fixture
def client():
    """测试客户端"""
    return TestClient(app)

@pytest.fixture
def auth_headers():
    """认证头"""
    return {"Authorization": "Bearer test_token"}

class TestTrainingAPI:
    """训练API测试类"""
    
    def test_get_training_info(self, client, auth_headers):
        """测试获取训练信息"""
        response = client.get("/api/v1/train/info", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "supported_algorithms" in data["data"]
        assert "TimesNet" in data["data"]["supported_algorithms"]
    
    @patch('industryai.services.training.TrainingService.start_training')
    def test_start_training(self, mock_start_training, client, auth_headers):
        """测试启动训练"""
        mock_start_training.return_value = AsyncMock()
        mock_start_training.return_value.task_id = "task_123"
        
        training_request = {
            "model_name": "TimesNet_test",
            "algorithm_type": "TimesNet",
            "data_source": "test_data.csv",
            "config": {
                "epochs": 10,
                "batch_size": 32
            }
        }
        
        response = client.post(
            "/api/v1/train/start",
            json=training_request,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "task_id" in data["data"]
```

### 4. 运行测试

```bash
# 运行所有测试
just test

# 运行特定测试文件
pytest tests/unit/test_models/test_timesnet.py -v

# 运行特定测试类
pytest tests/unit/test_models/test_timesnet.py::TestTimesNetModel -v

# 运行特定测试方法
pytest tests/unit/test_models/test_timesnet.py::TestTimesNetModel::test_forward_pass -v

# 生成覆盖率报告
pytest --cov=src/industryai --cov-report=html

# 运行性能测试
pytest tests/performance/ --benchmark-only
```

## 文档编写

### 1. API文档

API文档使用FastAPI自动生成,但需要完善docstring:

```python
from fastapi import APIRouter, Depends, HTTPException
from industryai.schemas.training import TrainingRequest, TrainingResponse

router = APIRouter(prefix="/train", tags=["训练"])

@router.post("/start", response_model=TrainingResponse)
async def start_training(
    request: TrainingRequest,
    current_user: User = Depends(get_current_user)
) -> TrainingResponse:
    """
    启动模型训练任务
    
    启动一个新的模型训练任务,支持多种时间序列预测算法。
    训练过程是异步的,可以通过返回的task_id查询训练状态。
    
    - **model_name**: 模型名称,用于标识和保存模型
    - **algorithm_type**: 算法类型,支持TimesNet、Informer等
    - **data_source**: 数据源路径或数据库查询
    - **config**: 训练配置参数
    
    Returns:
        TrainingResponse: 包含任务ID和状态信息
        
    Raises:
        HTTPException: 当请求参数无效或训练启动失败时
    """
    pass
```

### 2. 用户文档

用户文档使用Markdown编写,放在`docs/docs/`目录下:

```markdown
# 模型训练指南

## 概述

IndustryAI支持多种时间序列预测算法...

## 支持的算法

### TimesNet

TimesNet是一种基于时间序列分解的深度学习模型...

```python
# 使用示例
from industryai.models import TimesNetModel

model = TimesNetModel(config)
result = model.train(data)
```

### 配置参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| epochs | int | 100 | 训练轮数 |
| batch_size | int | 32 | 批次大小 |
```

### 3. 构建文档

```bash
# 安装文档依赖
uv pip install -e ".[docs]"

# 构建文档
just docs-build

# 启动文档服务器
just docs-serve

# 部署文档
just docs-deploy
```

## 性能优化

### 1. 代码性能

```python
# 使用异步编程
async def process_large_dataset(data: List[Dict]) -> List[Result]:
    """异步处理大数据集"""
    tasks = []
    for chunk in chunk_data(data, chunk_size=1000):
        task = asyncio.create_task(process_chunk(chunk))
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    return flatten(results)

# 使用缓存
from functools import lru_cache

@lru_cache(maxsize=128)
def expensive_computation(param: str) -> float:
    """昂贵的计算,使用缓存"""
    # 复杂计算...
    return result

# 使用Polars进行数据处理
import polars as pl

def process_timeseries_data(df: pl.DataFrame) -> pl.DataFrame:
    """使用Polars高效处理时间序列数据"""
    return (
        df
        .with_columns([
            pl.col("timestamp").str.to_datetime(),
            pl.col("value").cast(pl.Float64)
        ])
        .sort("timestamp")
        .with_columns([
            pl.col("value").rolling_mean(window_size=10).alias("ma_10"),
            pl.col("value").pct_change().alias("returns")
        ])
    )
```

### 2. 数据库优化

```python
# 使用连接池
from sqlalchemy.ext.asyncio import create_async_engine

engine = create_async_engine(
    database_url,
    pool_size=20,
    max_overflow=30,
    pool_timeout=30,
    pool_recycle=3600
)

# 批量操作
async def bulk_insert_predictions(
    predictions: List[PredictionResult]
) -> None:
    """批量插入预测结果"""
    async with engine.begin() as conn:
        await conn.execute(
            insert(prediction_table),
            [pred.dict() for pred in predictions]
        )

# 使用索引
# 在数据库中创建适当的索引
CREATE INDEX idx_predictions_timestamp ON predictions(timestamp);
CREATE INDEX idx_predictions_model_id ON predictions(model_id);
```

## 调试技巧

### 1. 日志调试

```python
from loguru import logger

# 配置日志
logger.add(
    "debug.log",
    level="DEBUG",
    format="{time} | {level} | {name}:{function}:{line} | {message}",
    rotation="1 day"
)

# 使用日志
def complex_function(data: Any) -> Any:
    logger.debug(f"输入数据类型: {type(data)}, 大小: {len(data)}")
    
    try:
        result = process_data(data)
        logger.info(f"处理成功,结果大小: {len(result)}")
        return result
    except Exception as e:
        logger.exception(f"处理失败: {e}")
        raise
```

### 2. 性能分析

```python
# 使用装饰器进行性能分析
import time
from functools import wraps

def profile_time(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        logger.info(f"{func.__name__} 执行时间: {end - start:.4f}秒")
        return result
    return wrapper

@profile_time
def train_model(data):
    # 训练逻辑
    pass

# 使用cProfile
import cProfile
import pstats

def profile_function():
    profiler = cProfile.Profile()
    profiler.enable()
    
    # 执行需要分析的代码
    train_model(data)
    
    profiler.disable()
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative')
    stats.print_stats(10)
```

## 部署指南

### 1. Docker部署

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY pyproject.toml .
RUN pip install -e .

# 复制源代码
COPY src/ src/
COPY config/ config/

# 设置环境变量
ENV PYTHONPATH=/app/src

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "industryai.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. 生产环境配置

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENV=production
      - DB_HOST=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: industryai
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    
  redis:
    image: redis:6-alpine
    restart: unless-stopped

volumes:
  postgres_data:
```

## 贡献指南

### 1. 报告问题

在GitHub上创建Issue时,请提供:

- 问题描述
- 复现步骤
- 期望行为
- 实际行为
- 环境信息(Python版本、操作系统等)
- 相关日志

### 2. 提交代码

1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 确保所有测试通过
5. 提交Pull Request

### 3. 代码审查

Pull Request将经过以下审查:

- 代码质量检查
- 测试覆盖率
- 文档完整性
- 性能影响
- 安全性检查

## 常见问题

### Q: 如何添加新的时间序列模型？

A: 继承`BaseTimeSeriesModel`类并实现必要的方法:

```python
from industryai.models.base import BaseTimeSeriesModel

class YourModel(BaseTimeSeriesModel):
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        # 初始化模型
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 实现前向传播
        pass
    
    def train_step(self, batch: Dict) -> Dict:
        # 实现训练步骤
        pass
```

### Q: 如何添加新的API端点？

A: 在相应的路由文件中添加端点:

```python
# src/industryai/api/v1/your_module.py
from fastapi import APIRouter

router = APIRouter(prefix="/your-module", tags=["Your Module"])

@router.get("/endpoint")
async def your_endpoint():
    return {"message": "Hello World"}

# 在main.py中注册路由
from industryai.api.v1.your_module import router as your_router
app.include_router(your_router, prefix="/api/v1")
```

### Q: 如何配置日志？

A: 在配置文件中设置日志参数:

```python
from loguru import logger
from industryai.core.config import settings

# 配置日志
logger.add(
    settings.LOG_FILE_PATH,
    level=settings.LOG_LEVEL,
    format=settings.LOG_FORMAT,
    rotation=settings.LOG_FILE_ROTATION
)
```

## 相关资源

- [项目主页](https://github.com/your-repo/industryai)
- [API文档](http://localhost:8000/docs)
- [问题跟踪](https://github.com/your-repo/industryai/issues)
- [讨论区](https://github.com/your-repo/industryai/discussions)
- [技术博客](https://blog.industryai.com)

## 联系方式

- 邮箱: <EMAIL>
- GitHub: [@your-username](https://github.com/your-username)
- 微信群: 扫描二维码加入开发者群