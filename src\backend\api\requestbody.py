from pydantic import BaseModel
from typing import Optional
from enum import Enum
from colorama import init, Fore


from industrytslib.core.training_pipeline import TRAINER_CLASS_MAP
from industrytslib.core.predictor_pipeline import PREDICTOR_CLASS_MAP
from industrytslib.core.decision_pipeline import DECISION_CLASS_MAP


init(autoreset=True)


PRINT_FLAG = True
def print_enum_source(enum_cls, color, class_name=None):
    if PRINT_FLAG:
        if class_name is None:
            class_name = enum_cls.__name__
        print(color + f"class {class_name}(str, Enum):")
        for v in enum_cls:
            print(color + f"    {v.name} = \"{v.value}\"")
        print()  # 空行分隔


class TimeUnit(str, Enum):
    """时间单位枚举"""
    SECONDS = "seconds"
    MINUTES = "minutes" 
    HOURS = "hours"


# class AlgorithmType_Train(str, Enum):
#     SEQUENCE = "sequence"
#     SEQUENCE_ONLINE = "sequence_online"
#     CLASSIC = "classic"
#     CLASSIC_ONLINE = "classic_online"
#     GAN = "gan"
#     RL = "rl"
#     TIME_SERIES_SEQUENCE = "time_series_sequence"
#     TIME_SERIES_CLASSIC = "time_series_classic"
#     MULTI_INPUT_TIME_SERIES_CLASSIC = "multi_input_time_series_classic"
#     JOINT_LOSS = "joint_loss"
#     JOINT_GAN = "joint_gan"
#     QUALITY_SEQUENCE = "quality_sequence"
#     JOINT_TRAINER_SEQUENCE = "joint_trainer_sequence"
#     JOINT_QUALITY_MONITOR = "joint_quality_monitor"
#     JOINT_TRAINER_FINAL = "joint_trainer_final"
#     MULTI_TASK_SEQ_PRETRAINER = "multi_task_seq_pretrainer"
#     ML = "ml"


AlgorithmType_Train = Enum(
    "AlgorithmType_Train",
    {key: key for key in TRAINER_CLASS_MAP.keys()},
    type=str
)
# 彩色打印类似于 Enum 源代码定义的格式
print_enum_source(AlgorithmType_Train, Fore.GREEN)


class TrainModelRequestBody(BaseModel):
    r"""
    训练模型请求体

    参数说明与优先级逻辑:

    1. project_name: str
        - 必填,项目名称。

    2. interval_hours: Optional[int]
        - 新增字段,表示训练时间间隔(单位:小时)。
        - 如果提供该参数,则优先使用 interval_hours,忽略 interval 和 time_unit。
        - 推荐优先使用此参数。

    3. interval: Optional[int]
        - 兼容老接口,表示训练时间间隔(单位由 time_unit 决定,默认为小时)。
        - 仅当 interval_hours 未提供时才会生效。
        - 如果 interval_hours 和 interval 都未提供,则会抛出异常。

    4. time_unit: TimeUnit
        - 指定 interval 的时间单位,可选值为 HOURS、MINUTES、SECONDS,默认为 HOURS。
        - 仅在 interval_hours 未提供、interval 存在时有效。
        - 如果 interval_hours 存在,则该参数无效。

    5. algorithm_type: AlgorithmType_Train
        - 必填,模型类型,必须为 classic_alter、sequence、time_series_sequence、classic 之一。

    参数优先级与互斥逻辑:
        - interval_hours 优先级最高,若提供则 interval 和 time_unit 均被忽略。
        - interval 仅在 interval_hours 未提供时有效,需配合 time_unit 使用。
        - interval_hours 和 interval 至少提供一个,否则会报错。

    示例用法与实际效果:
        1. 仅提供 interval_hours:
        {
            "project_name": "A",
            "interval_hours": 2,
            "algorithm_type": "sequence"
        }
        # 实际间隔:2小时 = 7200秒

        2. 仅提供 interval,指定 time_unit 为 SECONDS:
            {
                "project_name": "B",
                "interval": 60,
                "time_unit": "SECONDS",
                "algorithm_type": "classic"
            }
            # 实际间隔:60秒

        3. 仅提供 interval,指定 time_unit 为 MINUTES:
            {
                "project_name": "C",
                "interval": 10,
                "time_unit": "MINUTES",
                "algorithm_type": "classic_alter"
            }
            # 实际间隔:10分钟 = 600秒

        4. 仅提供 interval,未指定 time_unit(默认HOURS):
            {
                "project_name": "D",
                "interval": 2,
                "algorithm_type": "time_series_sequence"
            }
            # 实际间隔:2小时 = 7200秒

        5. 同时提供 interval_hours 和 interval:
            {
                "project_name": "E",
                "interval_hours": 3,
                "interval": 10,
                "time_unit": "MINUTES",
                "algorithm_type": "sequence"
            }
            # 实际间隔:3小时 = 10800秒(interval_hours 优先)

        6. interval_hours 和 interval 都未提供:
            {
                "project_name": "F",
                "algorithm_type": "classic"
            }
            # 错误:必须提供 interval_hours 或 interval
    """
    project_name: str
    interval: Optional[int] = None  # 向后兼容,单位为秒
    interval_hours: Optional[int] = None  # 新增字段,单位为小时
    time_unit: TimeUnit = TimeUnit.HOURS  # 默认使用小时
    algorithm_type: AlgorithmType_Train  # 使用定义的类型别名

    def get_interval_seconds(self) -> int:
        """获取以秒为单位的间隔时间"""
        if self.interval_hours is not None:
            return self.interval_hours * 3600
        elif self.interval is not None:
            if self.time_unit == TimeUnit.HOURS:
                return self.interval * 3600
            elif self.time_unit == TimeUnit.MINUTES:
                return self.interval * 60
            else:  # SECONDS
                return self.interval
        else:
            raise ValueError("必须提供 interval 或 interval_hours 参数")


class TrainModelListRequestBody(BaseModel):
    """
    训练模型列表请求体
    
    Args:
        project_name_list: 项目名称列表
        interval_list: 时间间隔列表(为了向后兼容)
        interval_hours_list: 时间间隔列表(小时) - 新增字段
        time_unit: 时间单位,默认为小时
    """
    project_name_list: list[str]
    interval_list: Optional[list[int]] = None  # 向后兼容
    interval_hours_list: Optional[list[int]] = None  # 新增字段
    time_unit: TimeUnit = TimeUnit.HOURS
    algorithm_type_list: list[AlgorithmType_Train]  # 使用定义的类型别名

    def get_interval_seconds_list(self) -> list[int]:
        """获取以秒为单位的间隔时间列表"""
        if self.interval_hours_list is not None:
            return [hours * 3600 for hours in self.interval_hours_list]
        elif self.interval_list is not None:
            if self.time_unit == TimeUnit.HOURS:
                return [interval * 3600 for interval in self.interval_list]
            elif self.time_unit == TimeUnit.MINUTES:
                return [interval * 60 for interval in self.interval_list]
            else:  # SECONDS
                return self.interval_list
        else:
            raise ValueError("必须提供 interval_list 或 interval_hours_list 参数")


class StopTrainRequestBody(BaseModel):
    """
    停止训练请求体
    
    Args:
        project_name: 项目名称
    """
    project_name: str


class QueryLatestTrainEffectRequestBody(BaseModel):
    """
    查询最新训练效果请求体
    
    Args:
        project_name: 项目名称
    """
    project_name: str


# class PredictorType_RealTime(str, Enum):
#     """实时预测器类型枚举"""
#     BASIC = "basic"
#     CLASSIC = "classic"
#     CLASSIC_MO = "classic_mo"
#     CLASSIC_STANDARD = "classic_standard"
#     SEQUENCE = "sequence"
#     QUALITY = "quality"
#     SEQUENCE_SOFT = "sequence_soft"
#     SEQUENCE_MO_SOFT = "sequence_mo_soft"
#     TIME_SERIES = "time_series"

# 动态生成 PredictorType_RealTime 枚举
PredictorType_RealTime = Enum(
    "PredictorType_RealTime",
    {key: key for key in PREDICTOR_CLASS_MAP.keys()},
    type=str
)
# 彩色打印PredictorType_RealTime
print_enum_source(PredictorType_RealTime, Fore.RED)


class RealtimePredictRequestBody(BaseModel):
    """
    实时预测请求体
    
    Args:
        project_name: 项目名称
        interval: 实时预测时间间隔
    """
    project_name: str
    interval: int
    predictor_type: PredictorType_RealTime  # 使用定义的类型别名


class StopRealtimePredictRequestBody(BaseModel):
    """
    停止实时预测请求体
    
    Args:
        project_name: 项目名称
    """
    project_name: str

# class DecisionType_DecisionMaking(str, Enum):
#     """优化决策类型枚举"""
#     BASIC = "basic"

# 动态生成 DecisionType_DecisionMaking 枚举
DecisionType_DecisionMaking = Enum(
    "DecisionType_DecisionMaking",
    {key: key for key in DECISION_CLASS_MAP.keys()},
    type=str
)
# 彩色打印类似于 Enum 源代码定义的格式
print_enum_source(DecisionType_DecisionMaking, Fore.BLUE)


class DecisionMakingRequestBody(BaseModel):
    """
    决策请求体
    
    Args:
        project_name: 项目名称
        interval: 决策时间间隔
    """
    project_name: str
    interval: int
    decision_type: DecisionType_DecisionMaking


class StopDecisionMakingRequestBody(BaseModel):
    """
    停止决策请求体
    
    Args:
        project_name: 项目名称
    """
    project_name: str

