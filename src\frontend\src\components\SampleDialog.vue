<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="添加样本数据"
    width="500px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="样本名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入样本名称" />
      </el-form-item>
      
      <el-form-item label="项目名称" prop="project_name">
        <el-input v-model="form.project_name" placeholder="请输入项目名称" />
      </el-form-item>
      
      <el-form-item label="数据路径" prop="data_path">
        <el-input v-model="form.data_path" placeholder="请输入数据文件路径" />
      </el-form-item>
      
      <el-form-item label="样本数量" prop="sample_count">
        <el-input-number
          v-model="form.sample_count"
          :min="0"
          :max="1000000"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入样本描述"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          添加
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'save'])

const formRef = ref()
const saving = ref(false)

const form = reactive({
  name: '',
  project_name: '',
  data_path: '',
  sample_count: 0,
  description: ''
})

const rules = {
  name: [
    { required: true, message: '请输入样本名称', trigger: 'blur' }
  ],
  project_name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' }
  ],
  data_path: [
    { required: true, message: '请输入数据路径', trigger: 'blur' }
  ],
  sample_count: [
    { required: true, message: '请输入样本数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '样本数量必须大于0', trigger: 'blur' }
  ]
}

// 监听对话框显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  form.name = ''
  form.project_name = ''
  form.data_path = ''
  form.sample_count = 0
  form.description = ''
  formRef.value?.clearValidate()
}

// 处理保存
const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saving.value = true
    
    const sampleData = {
      name: form.name,
      project_name: form.project_name,
      data_path: form.data_path,
      sample_count: form.sample_count,
      description: form.description
    }
    
    emit('save', sampleData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>