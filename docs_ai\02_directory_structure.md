
# 2. 主要目录结构及职责

本部分详细说明 `src/backend` 内各目录和关键文件的职责。

| 路径 | 类型 | 职责 |
| :--- | :--- | :--- |
| **`app.py`** | 文件 | **核心应用入口**。创建 FastAPI 实例,管理生命周期,注册路由,初始化调度器。 |
| **`main.py`** | 文件 | **命令行接口 (CLI)**。使用 Typer 包装 `app.py`,通过 `uvicorn` 启动服务。 |
| **`settings.py`** | 文件 | (当前为空) 通常用于存放应用级别的配置变量。 |
| **`/api/`** | 目录 | **API 数据模型**。 |
| `api/__init__.py` | 文件 | 导出 `requestbody.py` 中定义的 Pydantic 模型。 |
| `api/requestbody.py` | 文件 | 定义所有 API 端点的**请求体 (Request Body)**,使用 Pydantic 进行数据校验。 |
| **`/background_tasks/`** | 目录 | **后台任务实现**。 |
| `background_tasks/__init__.py` | 文件 | `BackgroundTask` 类,封装了需要周期性执行的任务,如数据处理。 |
| **`/licence/`** | 目录 | **代码加密与授权**。包含使用 PyArmor, Nuitka 等工具进行代码打包、加密和生成许可证的脚本。 |
| **`/requestes/`** | 目录 | **HTTP 客户端**。封装了 `httpx`,用于向自身或其他服务发送请求,主要用于 CLI 或测试。 |
| **`/routers/`** | 目录 | **API 路由**。定义 FastAPI 的 APIRouter,划分不同业务模块的 API。 |
| `routers/train.py` | 文件 | 处理所有与**模型训练**相关的 API 请求 (`/train`)。 |
| `routers/realtime_predict.py` | 文件 | 处理所有与**实时预测**相关的 API 请求 (`/rt`)。 |
| `routers/decision_making.py` | 文件 | 处理所有与**优化决策**相关的 API 请求 (`/opt`)。 |
| **`/tasks/`** | 目录 | **任务构建器**。 |
| `tasks/__init__.py` | 文件 | `build_tasks` 工厂函数,根据类型动态创建任务实例 (Trainer, Predictor, Agent)。 |
| `tasks/operation_status.py` | 文件 | (已废弃) 系统运行状态监控的旧实现。 |
| **`/utils/`** | 目录 | **通用工具**。 |
| `utils/basefunc.py` | 文件 | 提供通用函数,如 `worship()` (打印佛祖保佑图案)。 |
| `utils/heartbeat_checker.py` | 文件 | `HeartbeatMonitor` 类,用于生成心跳文件,供外部程序监控应用健康状态。 |
| `utils/task_scheduler.py` | 文件 | **任务调度器配置**。初始化和配置四个独立的 `APScheduler` 实例,分别用于训练、预测、决策和通用后台任务。 |
