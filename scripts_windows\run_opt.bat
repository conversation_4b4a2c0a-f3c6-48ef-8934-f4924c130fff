@echo off
echo 🎯 启动优化模式 (Windows)...

REM 检查虚拟环境
if exist ".venv\Scripts\activate.bat" (
    echo ✅ 激活虚拟环境 (.venv)
    call .venv\Scripts\activate.bat
) else if exist "venv\Scripts\activate.bat" (
    echo ✅ 激活虚拟环境 (venv)
    call venv\Scripts\activate.bat
) else (
    echo ⚠️  未找到虚拟环境,使用系统Python
)

REM 设置 PYTHONPATH
set PYTHONPATH=%PYTHONPATH%;%CD%
echo 项目路径: %PYTHONPATH%

REM 项目配置数组(Windows批处理中使用循环处理)
echo 开始优化任务...

REM 优化项目1
echo 请求决策优化项目: 回转窑
echo   - 间隔时间: 600秒
python src\backend\requestes\requests_opt.py --project-name "回转窑" --interval "600"
echo 请求决策优化项目: 回转窑 完成
echo ---

echo ✅ 所有优化任务完成