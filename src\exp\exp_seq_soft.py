import os
import sys
import time
from industrytslib.core import get_realtime_predictor
from industrytslib.utils.readconfig import read_config_toml

# Add both current directory and parent directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.extend([current_dir, parent_dir])

config = read_config_toml("config/database_config.toml")

project_name_list =[
    "一线窑fcao样本_一线窑fcao样本_CNN",
    "水泥A磨细度样本_水泥A磨细度样本_CNN",
    "水泥A磨比表面积样本_水泥A磨比表面积样本_CNN",
    "水泥B磨细度样本_水泥B磨细度样本_CNN",
    "水泥B磨比表面积样本_水泥B磨比表面积样本_CNN",
    "原料A磨生料细度样本_原料A磨生料细度样本_CNN",
    "原料B磨生料细度样本_原料B磨生料细度样本_CNN",
    "煤磨细度样本_煤磨细度样本_CNN"
]
predictor_list = []

for project_name in project_name_list:
    predictor = get_realtime_predictor(project_name, config, "sequence_soft")
    predictor_list.append(predictor)

def main_sync():
    while True:
        for predictor in predictor_list:
            try:
                predictor.main()
            except Exception as e:
                print(e)
        time.sleep(60)


if __name__ == "__main__":
    main_sync()
