[operation_status]
# Time Series Data Server
type = "mssql"
target = "timeseries"
server = "localhost"
# server = "*********"
port = 1433
database = "控制器数据库"
username = "sa"
password = "ysdxdckj@666"
tag_name = "协同优化运行寄存"


[web]
# Web Server
type = "mssql"
target = "web"
# server = "*********"
server = "************"
port = 1433
database = "eladmin"
username = "sa"
password = "ysdxdckj@666"

[online_trainer]
# Web Server
type = "mssql"
target = "online_trainer"
# server = "*********"
server = "************"
port = 1433
database = "eladmin"
username = "sa"
password = "ysdxdckj@666"

[realtimepredict]
# Real-time Prediction Data Server
type = "mssql"
target = "realtime_predict"
# server = "*********"
server = "************"
port = 1433
database = "RealtimePredict"
username = "sa"
password = "ysdxdckj@666"

[realtimepredictalter]
# Real-time Prediction Data Server
type = "mssql"
target = "realtime_predict_alter"
# server = "*********"
server = "************"
port = 1433
database = "AIPredict"
username = "sa"
password = "ysdxdckj@666"

[realtimepredict_sequence]
# Real-time Prediction Data Server
type = "mssql"
target = "realtime_predict_sequence"
# server = "*********"
server = "************"
port = 1433
database = "AIPredict"
username = "sa"
password = "ysdxdckj@666"

[timeseries]
# Time Series Data Server
type = "mssql"
target = "timeseries"
server = "************"
# server = "*********"
port = 1433
database = "控制器数据库"
username = "sa"
password = "ysdxdckj@666"

[quality]
# Quality Inspection Data Server
type = "mssql"
target = "quality"
server = "*********"
port = 1433
database = "样本检验结果"
username = "sa"
password = "ysdxdckj@666"

[decision_history]
# Decision History Data Server
type = "mssql"
target = "decision_history"
server = "*********"
port = 1433
database = "控制器数据库"
username = "sa"
password = "ysdxdckj@666"

[predict_transaction_source]
type = "mssql"
target = "predict_transaction_source"
server = "*********"
port = 1433
database = "RealtimePredict"
username = "sa"
password = "ysdxdckj@666"

[predict_transaction_target]
type = "mssql"
target = "predict_transaction_target"
server = "*********"
port = 1433
database = "控制器数据库"
username = "sa"
password = "ysdxdckj@666"

[mongodb]
type = "mongodb"
url = "mongodb://*********:27017"
database = "timeseries"
username = "admin"
password = "ysdxdckj@666"

[influxdb]
# InfluxDB
type = "influxdb"
url = "http://************:8086"
token = "qAAxOG6EbMOZkVQY2nSKWVASRH5k-YNVNVkJKHUbNc9w9oA54OUfEaXg4VBPLxXeNaTIr_hDp29r414wrY9q0Q=="
org = "dckj"

[postgresql]
type = "postgres"
target = "base"
host = "localhost"
port = 5432
database = "mydb"
username = "postgres"
password = "123456"

[model_info]
type = "sqllite"
target = "model_info"
database = "config/model_info.db"

[model_info_location]
# model info location, including sqlite, mssql, toml, default is sqlite
# ! 实际使用请修改为"mssql"
# location = "sqlite"
# location = "mssql"
location = "toml"
