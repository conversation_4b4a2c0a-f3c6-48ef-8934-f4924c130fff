/* IndustryAI 文档自定义样式 */

/* 主题色彩调整 */
:root {
  --md-primary-fg-color: #1976d2;
  --md-primary-fg-color--light: #42a5f5;
  --md-primary-fg-color--dark: #1565c0;
  --md-accent-fg-color: #2196f3;
}

/* 代码块样式优化 */
.highlight {
  border-radius: 8px;
  overflow: hidden;
}

.highlight pre {
  margin: 0;
  padding: 1rem;
}

/* API文档样式 */
.api-endpoint {
  background: var(--md-code-bg-color);
  border-left: 4px solid var(--md-primary-fg-color);
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 0 4px 4px 0;
}

.api-method {
  display: inline-block;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-weight: bold;
  font-size: 0.8rem;
  margin-right: 0.5rem;
}

.api-method.get {
  background: #4caf50;
  color: white;
}

.api-method.post {
  background: #2196f3;
  color: white;
}

.api-method.put {
  background: #ff9800;
  color: white;
}

.api-method.delete {
  background: #f44336;
  color: white;
}

/* 响应示例样式 */
.response-example {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 1rem;
  margin: 1rem 0;
}

[data-md-color-scheme="slate"] .response-example {
  background: #2d3748;
  border-color: #4a5568;
}

/* 配置表格样式 */
.config-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.config-table th,
.config-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--md-default-fg-color--lightest);
}

.config-table th {
  background: var(--md-code-bg-color);
  font-weight: 600;
}

/* 状态标签 */
.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.success {
  background: #d4edda;
  color: #155724;
}

.status-badge.warning {
  background: #fff3cd;
  color: #856404;
}

.status-badge.error {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.info {
  background: #d1ecf1;
  color: #0c5460;
}

[data-md-color-scheme="slate"] .status-badge.success {
  background: #1e4620;
  color: #a3d9a5;
}

[data-md-color-scheme="slate"] .status-badge.warning {
  background: #4d3800;
  color: #ffc107;
}

[data-md-color-scheme="slate"] .status-badge.error {
  background: #4a1e1e;
  color: #f8a2a2;
}

[data-md-color-scheme="slate"] .status-badge.info {
  background: #1e3a4a;
  color: #7dd3fc;
}

/* 特性卡片 */
.feature-card {
  background: var(--md-default-bg-color);
  border: 1px solid var(--md-default-fg-color--lightest);
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.feature-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.feature-card h3 {
  margin-top: 0;
  color: var(--md-primary-fg-color);
}

/* 架构图样式 */
.architecture-diagram {
  text-align: center;
  margin: 2rem 0;
}

.architecture-diagram svg {
  max-width: 100%;
  height: auto;
}

/* 代码语言标签 */
.highlight .filename {
  background: var(--md-code-bg-color);
  color: var(--md-code-fg-color);
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  border-bottom: 1px solid var(--md-default-fg-color--lightest);
}

/* 快速开始按钮 */
.quick-start-btn {
  display: inline-block;
  background: var(--md-primary-fg-color);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  margin: 0.5rem 0.5rem 0.5rem 0;
  transition: background-color 0.3s ease;
}

.quick-start-btn:hover {
  background: var(--md-primary-fg-color--dark);
  color: white;
  text-decoration: none;
}

.quick-start-btn.secondary {
  background: transparent;
  color: var(--md-primary-fg-color);
  border: 2px solid var(--md-primary-fg-color);
}

.quick-start-btn.secondary:hover {
  background: var(--md-primary-fg-color);
  color: white;
}

/* 版本信息 */
.version-info {
  background: var(--md-code-bg-color);
  border-left: 4px solid var(--md-accent-fg-color);
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 0 4px 4px 0;
}

/* 警告框样式增强 */
.admonition.warning {
  border-left-color: #ff9800;
}

.admonition.danger {
  border-left-color: #f44336;
}

.admonition.success {
  border-left-color: #4caf50;
}

.admonition.info {
  border-left-color: #2196f3;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .feature-card {
    margin: 0.5rem 0;
    padding: 1rem;
  }
  
  .api-endpoint {
    padding: 0.75rem;
  }
  
  .quick-start-btn {
    display: block;
    text-align: center;
    margin: 0.5rem 0;
  }
}

/* 打印样式 */
@media print {
  .md-header,
  .md-sidebar,
  .md-footer {
    display: none;
  }
  
  .md-content {
    margin: 0;
  }
  
  .highlight {
    border: 1px solid #ccc;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--md-default-bg-color);
}

::-webkit-scrollbar-thumb {
  background: var(--md-default-fg-color--lighter);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--md-default-fg-color--light);
}

/* 加载动画 */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid var(--md-default-fg-color--lightest);
  border-radius: 50%;
  border-top-color: var(--md-primary-fg-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}