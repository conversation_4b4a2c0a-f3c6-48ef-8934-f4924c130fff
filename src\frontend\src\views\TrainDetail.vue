<template>
  <div class="train-detail">
    <div class="page-header">
      <el-button @click="goBack" type="primary" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回训练页面
      </el-button>
      <div class="page-title">训练详情 - {{ projectName }}</div>
    </div>

    <!-- 训练信息概览 -->>
    <div class="card">
      <h3>训练信息</h3>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="项目名称">{{ trainInfo.project_name }}</el-descriptions-item>
        <el-descriptions-item label="算法配置">{{ trainInfo.config_name }}</el-descriptions-item>
        <el-descriptions-item label="训练状态">
          <el-tag :type="getStatusType(trainInfo.status)">{{ getStatusText(trainInfo.status) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ formatTime(trainInfo.start_time) }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ formatTime(trainInfo.end_time) }}</el-descriptions-item>
        <el-descriptions-item label="训练时长">{{ trainInfo.duration || '-' }}</el-descriptions-item>
        <el-descriptions-item label="当前轮次">{{ trainInfo.current_epoch || 0 }}</el-descriptions-item>
        <el-descriptions-item label="总轮次">{{ trainInfo.total_epochs || 0 }}</el-descriptions-item>
        <el-descriptions-item label="最佳损失">{{ trainInfo.best_loss || '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- Loss曲线图 -->>
    <div class="card">
      <h3>训练Loss曲线</h3>
      <div class="chart-container">
        <div ref="lossChartRef" class="chart"></div>
      </div>
    </div>

    <!-- 测试结果 -->>
    <div class="card">
      <h3>测试结果</h3>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="metric-card">
            <h4>回归指标</h4>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="均方误差 (MSE)">{{ testResults.mse || '-' }}</el-descriptions-item>
              <el-descriptions-item label="均方根误差 (RMSE)">{{ testResults.rmse || '-' }}</el-descriptions-item>
              <el-descriptions-item label="平均绝对误差 (MAE)">{{ testResults.mae || '-' }}</el-descriptions-item>
              <el-descriptions-item label="决定系数 (R²)">{{ testResults.r2_score || '-' }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="metric-card">
            <h4>预测vs实际值对比</h4>
            <div ref="scatterChartRef" class="chart"></div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 模型参数 -->>
    <div class="card">
      <h3>模型参数</h3>
      <el-table :data="modelParams" style="width: 100%">
        <el-table-column prop="name" label="参数名称" width="200" />
        <el-table-column prop="value" label="参数值" />
        <el-table-column prop="description" label="描述" />
      </el-table>
    </div>

    <!-- 训练日志 -->>
    <div class="card">
      <h3>训练日志</h3>
      <div class="log-container">
        <el-input
          v-model="trainLogs"
          type="textarea"
          :rows="10"
          readonly
          placeholder="训练日志将在这里显示..."
        />
      </div>
      <div class="log-actions">
        <el-button @click="refreshLogs" :loading="loading.logs">
          <el-icon><Refresh /></el-icon>
          刷新日志
        </el-button>
        <el-button @click="downloadLogs">
          <el-icon><Download /></el-icon>
          下载日志
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import trainAPI from '../api/train'
import db from '../utils/database'

const route = useRoute()
const router = useRouter()

const projectName = ref(route.params.projectName || '')
const configId = ref(route.params.configId || '')

const lossChartRef = ref()
const scatterChartRef = ref()
let lossChart = null
let scatterChart = null

const loading = reactive({
  logs: false,
  data: false
})

const trainInfo = reactive({
  project_name: '',
  config_name: '',
  status: 'running',
  start_time: '',
  end_time: '',
  duration: '',
  current_epoch: 0,
  total_epochs: 100,
  best_loss: null
})

const testResults = reactive({
  mse: null,
  rmse: null,
  mae: null,
  r2_score: null,
  predictions: [],
  actual: []
})

const modelParams = ref([])
const trainLogs = ref('')
const lossData = ref({
  epochs: [],
  train_loss: [],
  val_loss: []
})

// 状态类型映射
const getStatusType = (status) => {
  const typeMap = {
    'running': 'primary',
    'completed': 'success',
    'failed': 'danger',
    'stopped': 'warning'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'running': '训练中',
    'completed': '已完成',
    'failed': '失败',
    'stopped': '已停止'
  }
  return textMap[status] || '未知'
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 初始化Loss曲线图
const initLossChart = () => {
  if (!lossChartRef.value) return
  
  lossChart = echarts.init(lossChartRef.value)
  
  const option = {
    title: {
      text: '训练Loss曲线',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['训练Loss', '验证Loss'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      name: 'Epoch',
      data: lossData.value.epochs
    },
    yAxis: {
      type: 'value',
      name: 'Loss'
    },
    series: [
      {
        name: '训练Loss',
        type: 'line',
        data: lossData.value.train_loss,
        smooth: true,
        lineStyle: {
          color: '#409EFF'
        }
      },
      {
        name: '验证Loss',
        type: 'line',
        data: lossData.value.val_loss,
        smooth: true,
        lineStyle: {
          color: '#67C23A'
        }
      }
    ]
  }
  
  lossChart.setOption(option)
}

// 初始化散点图
const initScatterChart = () => {
  if (!scatterChartRef.value) return
  
  scatterChart = echarts.init(scatterChartRef.value)
  
  const scatterData = testResults.predictions.map((pred, index) => [
    testResults.actual[index],
    pred
  ])
  
  const option = {
    title: {
      text: '预测值 vs 实际值',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        return `实际值: ${params.data[0]}<br/>预测值: ${params.data[1]}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '实际值'
    },
    yAxis: {
      type: 'value',
      name: '预测值'
    },
    series: [
      {
        type: 'scatter',
        data: scatterData,
        symbolSize: 6,
        itemStyle: {
          color: '#409EFF',
          opacity: 0.7
        }
      },
      {
        type: 'line',
        data: [[0, 0], [100, 100]], // 理想线 y=x
        lineStyle: {
          color: '#E6A23C',
          type: 'dashed'
        },
        symbol: 'none',
        name: '理想线'
      }
    ]
  }
  
  scatterChart.setOption(option)
}

// 加载训练详情数据
const loadTrainDetail = async () => {
  try {
    loading.data = true
    
    // 模拟从后端获取训练详情数据
    const response = await trainAPI.getTrainDetail({
      project_name: projectName.value,
      config_id: configId.value
    })
    
    // 更新训练信息
    Object.assign(trainInfo, response.train_info || {})
    
    // 更新测试结果
    Object.assign(testResults, response.test_results || {})
    
    // 更新Loss数据
    if (response.loss_data) {
      lossData.value = response.loss_data
    }
    
    // 更新模型参数
    modelParams.value = response.model_params || []
    
    // 更新训练日志
    trainLogs.value = response.logs || ''
    
  } catch (error) {
    console.error('加载训练详情失败:', error)
    
    // 使用模拟数据
    loadMockData()
  } finally {
    loading.data = false
  }
}

// 加载模拟数据
const loadMockData = () => {
  // 模拟训练信息
  Object.assign(trainInfo, {
    project_name: projectName.value,
    config_name: 'LSTM-KAN配置1',
    status: 'running',
    start_time: new Date(Date.now() - 3600000).toISOString(),
    end_time: null,
    duration: '1小时23分钟',
    current_epoch: 45,
    total_epochs: 100,
    best_loss: 0.0234
  })
  
  // 模拟Loss数据
  const epochs = Array.from({length: 45}, (_, i) => i + 1)
  const trainLoss = epochs.map(e => 0.5 * Math.exp(-e/20) + 0.01 + Math.random() * 0.01)
  const valLoss = epochs.map(e => 0.6 * Math.exp(-e/18) + 0.02 + Math.random() * 0.015)
  
  lossData.value = {
    epochs,
    train_loss: trainLoss,
    val_loss: valLoss
  }
  
  // 模拟测试结果
  Object.assign(testResults, {
    mse: 0.0234,
    rmse: 0.1529,
    mae: 0.1123,
    r2_score: 0.9456,
    predictions: Array.from({length: 100}, () => Math.random() * 100),
    actual: Array.from({length: 100}, () => Math.random() * 100)
  })
  
  // 模拟模型参数
  modelParams.value = [
    { name: 'learning_rate', value: '0.001', description: '学习率' },
    { name: 'batch_size', value: '32', description: '批次大小' },
    { name: 'hidden_size', value: '128', description: '隐藏层大小' },
    { name: 'num_layers', value: '3', description: '网络层数' },
    { name: 'dropout', value: '0.2', description: 'Dropout率' }
  ]
  
  // 模拟训练日志
  trainLogs.value = `[2024-01-15 10:30:15] 开始训练模型...\n[2024-01-15 10:30:16] 加载数据集完成,训练样本: 8000, 验证样本: 2000\n[2024-01-15 10:30:20] Epoch 1/100 - train_loss: 0.4523, val_loss: 0.4891\n[2024-01-15 10:30:25] Epoch 2/100 - train_loss: 0.3876, val_loss: 0.4234\n[2024-01-15 10:30:30] Epoch 3/100 - train_loss: 0.3456, val_loss: 0.3987\n...\n[2024-01-15 11:45:32] Epoch 45/100 - train_loss: 0.0234, val_loss: 0.0267\n[2024-01-15 11:45:32] 当前最佳模型已保存`
}

// 刷新日志
const refreshLogs = async () => {
  try {
    loading.logs = true
    
    const response = await trainAPI.getTrainLogs({
      project_name: projectName.value,
      config_id: configId.value
    })
    
    trainLogs.value = response.logs || ''
    
  } catch (error) {
    console.error('刷新日志失败:', error)
  } finally {
    loading.logs = false
  }
}

// 下载日志
const downloadLogs = () => {
  const blob = new Blob([trainLogs.value], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `train_logs_${projectName.value}_${new Date().toISOString().slice(0, 10)}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// 返回训练页面
const goBack = () => {
  router.push('/train')
}

// 定时更新数据
let updateInterval = null

const startAutoUpdate = () => {
  updateInterval = setInterval(() => {
    if (trainInfo.status === 'running') {
      loadTrainDetail()
    }
  }, 5000) // 每5秒更新一次
}

const stopAutoUpdate = () => {
  if (updateInterval) {
    clearInterval(updateInterval)
    updateInterval = null
  }
}

onMounted(async () => {
  await loadTrainDetail()
  
  // 初始化图表
  setTimeout(() => {
    initLossChart()
    initScatterChart()
  }, 100)
  
  // 开始自动更新
  startAutoUpdate()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (lossChart) lossChart.resize()
    if (scatterChart) scatterChart.resize()
  })
})

onUnmounted(() => {
  stopAutoUpdate()
  
  if (lossChart) {
    lossChart.dispose()
    lossChart = null
  }
  
  if (scatterChart) {
    scatterChart.dispose()
    scatterChart = null
  }
  
  window.removeEventListener('resize', () => {
    if (lossChart) lossChart.resize()
    if (scatterChart) scatterChart.resize()
  })
})
</script>

<style scoped>
.train-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.chart-container {
  width: 100%;
  height: 400px;
}

.chart {
  width: 100%;
  height: 100%;
}

.metric-card {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  height: 100%;
}

.metric-card h4 {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 600;
}

.log-container {
  margin-bottom: 15px;
}

.log-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-textarea__inner) {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}
</style>