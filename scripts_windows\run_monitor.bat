@echo off
REM 运行前请确认:
REM 1. 你已经通过cmd或powershell进入了项目根目录 (industryai/)
REM 2. 你已经激活了正确的conda环境
REM
REM 该脚本将启动独立的任务状态监控服务,其功能与 run_monitor.sh 对齐。

REM 使用 PowerShell 加载 .env 文件,使其功能与Linux下的sed对齐
if exist .env (
    echo "正在加载 .env 文件..."
    for /f "delims=" %%i in ('powershell -NoProfile -Command "Get-Content .env | ForEach-Object { $line = $_.Trim(); if ($line -and !$line.StartsWith('#')) { $parts = $line.Split('=', 2); if ($parts.Length -eq 2) { $key = $parts[0].Trim(); $value = $parts[1].Trim(); Write-Output ('set ' + $key + '="' + $value + '"') } } }"') do (
        %%i
    )
)

REM 设置PYTHONPATH,追加当前目录
if defined PYTHONPATH (
    set "PYTHONPATH=%PYTHONPATH%;%CD%"
) else (
    set "PYTHONPATH=%CD%"
)
echo PYTHONPATH set to: %PYTHONPATH%
echo Using Python from:
where python

REM 运行任务监控服务
python src/operation_status/task_monitor.py 