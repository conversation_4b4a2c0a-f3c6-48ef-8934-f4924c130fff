@echo off
echo 🎯 启动训练模式 (Windows)...

REM 检查虚拟环境
if exist ".venv\Scripts\activate.bat" (
    echo ✅ 激活虚拟环境 (.venv)
    call .venv\Scripts\activate.bat
) else if exist "venv\Scripts\activate.bat" (
    echo ✅ 激活虚拟环境 (venv)
    call venv\Scripts\activate.bat
) else (
    echo ⚠️  未找到虚拟环境,使用系统Python
)

REM 设置 PYTHONPATH
set PYTHONPATH=%PYTHONPATH%;%CD%
echo 项目路径: %PYTHONPATH%

REM 项目配置数组(Windows批处理中使用循环处理)
echo 开始训练任务...

REM 训练项目1
echo 请求训练项目: 阳泉窑电耗样本_阳泉窑电耗样本_CNN
echo   - 间隔时间: 300秒
echo   - 算法类型: classic
python src\backend\requestes\requests_train.py --project-name "阳泉窑电耗样本_阳泉窑电耗样本_CNN" --interval "300" --algorithm-type "classic"
echo 请求训练项目: 阳泉窑电耗样本_阳泉窑电耗样本_CNN 完成
echo ---

REM 训练项目2
echo 请求训练项目: 阳泉窑实物煤耗样本_阳泉窑实物煤耗样本_CNN
echo   - 间隔时间: 600秒
echo   - 算法类型: classic_alter
python src\backend\requestes\requests_train.py --project-name "阳泉窑实物煤耗样本_阳泉窑实物煤耗样本_CNN" --interval "600" --algorithm-type "classic_alter"
echo 请求训练项目: 阳泉窑实物煤耗样本_阳泉窑实物煤耗样本_CNN 完成
echo ---

REM 训练项目3
echo 请求训练项目: 阳泉fcao样本_阳泉fcao样本_CNN
echo   - 间隔时间: 450秒
echo   - 算法类型: classic
python src\backend\requestes\requests_train.py --project-name "阳泉fcao样本_阳泉fcao样本_CNN" --interval "450" --algorithm-type "classic"
echo 请求训练项目: 阳泉fcao样本_阳泉fcao样本_CNN 完成
echo ---

echo ✅ 所有训练任务完成