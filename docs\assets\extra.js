// IndustryAI 文档增强脚本

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有功能
    initCodeCopyButtons();
    initAPITester();
    initVersionSwitcher();
    initSearchEnhancement();
    initThemeToggle();
    initScrollToTop();
    initTableOfContents();
});

// 代码复制功能增强
function initCodeCopyButtons() {
    // 为所有代码块添加复制按钮
    const codeBlocks = document.querySelectorAll('pre code');
    
    codeBlocks.forEach(function(codeBlock) {
        const pre = codeBlock.parentElement;
        if (pre.querySelector('.copy-button')) return; // 避免重复添加
        
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-button';
        copyButton.innerHTML = '📋';
        copyButton.title = '复制代码';
        
        copyButton.addEventListener('click', function() {
            const text = codeBlock.textContent;
            navigator.clipboard.writeText(text).then(function() {
                copyButton.innerHTML = '✅';
                copyButton.title = '已复制';
                setTimeout(function() {
                    copyButton.innerHTML = '📋';
                    copyButton.title = '复制代码';
                }, 2000);
            }).catch(function() {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                copyButton.innerHTML = '✅';
                setTimeout(function() {
                    copyButton.innerHTML = '📋';
                }, 2000);
            });
        });
        
        pre.style.position = 'relative';
        pre.appendChild(copyButton);
    });
}

// API测试器
function initAPITester() {
    const apiEndpoints = document.querySelectorAll('.api-endpoint');
    
    apiEndpoints.forEach(function(endpoint) {
        const method = endpoint.querySelector('.api-method');
        const url = endpoint.querySelector('code');
        
        if (method && url) {
            const testButton = document.createElement('button');
            testButton.className = 'test-api-btn';
            testButton.textContent = '测试API';
            testButton.style.marginLeft = '10px';
            
            testButton.addEventListener('click', function() {
                openAPITester(method.textContent, url.textContent);
            });
            
            endpoint.appendChild(testButton);
        }
    });
}

function openAPITester(method, url) {
    const modal = document.createElement('div');
    modal.className = 'api-tester-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>API测试器</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="api-info">
                    <span class="api-method ${method.toLowerCase()}">${method}</span>
                    <input type="text" id="api-url" value="${url}" />
                </div>
                <div class="headers-section">
                    <h4>请求头</h4>
                    <textarea id="headers" placeholder='{
  "Content-Type": "application/json",
  "Authorization": "Bearer your_token"
}'></textarea>
                </div>
                <div class="body-section">
                    <h4>请求体</h4>
                    <textarea id="request-body" placeholder="请输入JSON格式的请求体"></textarea>
                </div>
                <div class="actions">
                    <button id="send-request" class="primary-btn">发送请求</button>
                    <button id="clear-form" class="secondary-btn">清空</button>
                </div>
                <div class="response-section">
                    <h4>响应结果</h4>
                    <div id="response-status"></div>
                    <pre id="response-body"></pre>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 绑定事件
    modal.querySelector('.close-btn').addEventListener('click', function() {
        document.body.removeChild(modal);
    });
    
    modal.querySelector('#send-request').addEventListener('click', function() {
        sendAPIRequest();
    });
    
    modal.querySelector('#clear-form').addEventListener('click', function() {
        modal.querySelector('#headers').value = '';
        modal.querySelector('#request-body').value = '';
        modal.querySelector('#response-status').textContent = '';
        modal.querySelector('#response-body').textContent = '';
    });
    
    // 点击模态框外部关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

function sendAPIRequest() {
    const url = document.getElementById('api-url').value;
    const method = document.querySelector('.api-method').textContent;
    const headersText = document.getElementById('headers').value;
    const bodyText = document.getElementById('request-body').value;
    
    let headers = {};
    try {
        if (headersText.trim()) {
            headers = JSON.parse(headersText);
        }
    } catch (e) {
        alert('请求头格式错误,请使用有效的JSON格式');
        return;
    }
    
    const options = {
        method: method,
        headers: headers
    };
    
    if (bodyText.trim() && (method === 'POST' || method === 'PUT')) {
        try {
            JSON.parse(bodyText); // 验证JSON格式
            options.body = bodyText;
        } catch (e) {
            alert('请求体格式错误,请使用有效的JSON格式');
            return;
        }
    }
    
    const statusDiv = document.getElementById('response-status');
    const responseDiv = document.getElementById('response-body');
    
    statusDiv.innerHTML = '<div class="loading"></div> 发送请求中...';
    responseDiv.textContent = '';
    
    fetch(url, options)
        .then(response => {
            statusDiv.innerHTML = `
                <span class="status-badge ${response.ok ? 'success' : 'error'}">
                    ${response.status} ${response.statusText}
                </span>
            `;
            return response.json();
        })
        .then(data => {
            responseDiv.textContent = JSON.stringify(data, null, 2);
        })
        .catch(error => {
            statusDiv.innerHTML = '<span class="status-badge error">请求失败</span>';
            responseDiv.textContent = error.message;
        });
}

// 版本切换器
function initVersionSwitcher() {
    const versionInfo = document.querySelector('.version-info');
    if (versionInfo) {
        const currentVersion = '1.0.0'; // 从配置中获取
        const versions = ['1.0.0', '0.9.0', '0.8.0']; // 从API获取
        
        const versionSelect = document.createElement('select');
        versionSelect.className = 'version-selector';
        
        versions.forEach(version => {
            const option = document.createElement('option');
            option.value = version;
            option.textContent = `v${version}`;
            if (version === currentVersion) {
                option.selected = true;
            }
            versionSelect.appendChild(option);
        });
        
        versionSelect.addEventListener('change', function() {
            const selectedVersion = this.value;
            // 这里可以实现版本切换逻辑
            console.log('切换到版本:', selectedVersion);
        });
        
        versionInfo.appendChild(versionSelect);
    }
}

// 搜索增强
function initSearchEnhancement() {
    const searchInput = document.querySelector('[data-md-component="search-query"]');
    if (searchInput) {
        // 添加搜索建议
        const suggestions = [
            'API接口', '模型训练', '实时预测', '配置文档', 
            '部署指南', '开发者指南', '安全配置'
        ];
        
        searchInput.addEventListener('focus', function() {
            showSearchSuggestions(suggestions);
        });
        
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            const filteredSuggestions = suggestions.filter(s => 
                s.toLowerCase().includes(query)
            );
            showSearchSuggestions(filteredSuggestions);
        });
    }
}

function showSearchSuggestions(suggestions) {
    // 实现搜索建议显示逻辑
    console.log('搜索建议:', suggestions);
}

// 主题切换增强
function initThemeToggle() {
    const themeToggle = document.querySelector('[data-md-component="palette"]');
    if (themeToggle) {
        // 记住用户的主题偏好
        const savedTheme = localStorage.getItem('theme-preference');
        if (savedTheme) {
            document.documentElement.setAttribute('data-md-color-scheme', savedTheme);
        }
        
        // 监听主题切换
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'data-md-color-scheme') {
                    const theme = document.documentElement.getAttribute('data-md-color-scheme');
                    localStorage.setItem('theme-preference', theme);
                }
            });
        });
        
        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['data-md-color-scheme']
        });
    }
}

// 回到顶部按钮
function initScrollToTop() {
    const scrollButton = document.createElement('button');
    scrollButton.className = 'scroll-to-top';
    scrollButton.innerHTML = '↑';
    scrollButton.title = '回到顶部';
    scrollButton.style.display = 'none';
    
    document.body.appendChild(scrollButton);
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            scrollButton.style.display = 'block';
        } else {
            scrollButton.style.display = 'none';
        }
    });
    
    scrollButton.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// 目录增强
function initTableOfContents() {
    const tocLinks = document.querySelectorAll('.md-nav__link');
    
    tocLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href && href.startsWith('#')) {
                e.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    
                    // 更新URL
                    history.pushState(null, null, href);
                }
            }
        });
    });
    
    // 高亮当前章节
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(function(entry) {
            if (entry.isIntersecting) {
                const id = entry.target.id;
                if (id) {
                    // 移除所有活跃状态
                    document.querySelectorAll('.md-nav__link--active').forEach(function(link) {
                        link.classList.remove('md-nav__link--active');
                    });
                    
                    // 添加当前活跃状态
                    const currentLink = document.querySelector(`a[href="#${id}"]`);
                    if (currentLink) {
                        currentLink.classList.add('md-nav__link--active');
                    }
                }
            }
        });
    }, {
        rootMargin: '-20% 0px -80% 0px'
    });
    
    // 观察所有标题
    document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(function(heading) {
        if (heading.id) {
            observer.observe(heading);
        }
    });
}

// 工具函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 添加CSS样式
const style = document.createElement('style');
style.textContent = `
.copy-button {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

pre:hover .copy-button {
    opacity: 1;
}

.api-tester-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: var(--md-default-bg-color);
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90%;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--md-default-fg-color--lightest);
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--md-default-fg-color);
}

.modal-body {
    padding: 1rem;
}

.api-info {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1rem;
}

#api-url {
    flex: 1;
    padding: 8px;
    border: 1px solid var(--md-default-fg-color--lightest);
    border-radius: 4px;
    background: var(--md-default-bg-color);
    color: var(--md-default-fg-color);
}

.headers-section, .body-section, .response-section {
    margin: 1rem 0;
}

textarea {
    width: 100%;
    height: 100px;
    padding: 8px;
    border: 1px solid var(--md-default-fg-color--lightest);
    border-radius: 4px;
    background: var(--md-default-bg-color);
    color: var(--md-default-fg-color);
    font-family: monospace;
    resize: vertical;
}

.actions {
    display: flex;
    gap: 10px;
    margin: 1rem 0;
}

.primary-btn, .secondary-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.primary-btn {
    background: var(--md-primary-fg-color);
    color: white;
}

.secondary-btn {
    background: transparent;
    color: var(--md-primary-fg-color);
    border: 1px solid var(--md-primary-fg-color);
}

.scroll-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: var(--md-primary-fg-color);
    color: white;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    z-index: 999;
}

.scroll-to-top:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

.test-api-btn {
    background: var(--md-accent-fg-color);
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.version-selector {
    margin-left: 10px;
    padding: 4px 8px;
    border: 1px solid var(--md-default-fg-color--lightest);
    border-radius: 4px;
    background: var(--md-default-bg-color);
    color: var(--md-default-fg-color);
}
`;

document.head.appendChild(style);