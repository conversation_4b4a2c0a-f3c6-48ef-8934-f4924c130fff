#!/bin/bash
# 请使用bash 执行脚本
# ! 在industryai根目录下执行, 不要在scripts目录下执行
# ! 请事先激活配置好的python虚拟环境,默认为 conda activate exp
# sh scripts/run_opt.sh

# 检查当前conda环境
current_env=$(conda info --envs | grep '*' | awk '{print $1}')
# 打印当前环境
echo "当前使用的python环境为: $current_env"
# 根据which python判断打印当前python的位置
python_path=$(which python)
echo "python路径: $python_path"

export PYTHONPATH=${PYTHONPATH:+$PYTHONPATH:}$(pwd)
echo "项目路径: $PYTHONPATH"

# 项目配置数组,格式:项目名|间隔时间|决策类型
project_configs=(
    "回转窑|600|basic"
    # 可以添加更多项目配置
    # "其他项目|300|classic"
)

# 构建批量处理的配置参数
config_args=()
for config in "${project_configs[@]}"; do
    config_args+=("--config" "$config")
done

echo "开始批量决策优化处理..."
echo "配置项目数量: ${#project_configs[@]}"

# 使用批量处理接口一次性处理所有项目
python src/backend/requestes/requests_opt.py batch "${config_args[@]}"

echo "批量决策优化处理完成"
