site_name: IndustryAI 文档
site_url: https://industryai.example.com/
repo_url: https://github.com/lyh/IndustryAI
repo_name: lyh/IndustryAI
edit_uri: "edit/main/docs/docs/"
dev_addr: 0.0.0.0:8000

theme:
  name: material
  language: zh
  palette:
    - scheme: default
      primary: indigo
      accent: indigo
      toggle:
        icon: material/brightness-7
        name: 切换到深色模式
    - scheme: slate
      primary: black
      accent: indigo
      toggle:
        icon: material/brightness-4
        name: 切换到浅色模式
  features:
    - navigation.tabs
    - navigation.sections
    - toc.integrate
    - navigation.top
    - search.suggest
    - search.highlight
    - content.tabs.link
    - content.code.annotation
    - content.code.copy
  logo: assets/logo.png
  favicon: assets/favicon.ico

# 插件配置
plugins:
  - search
  - git-revision-date-localized:
      type: timeago
      locale: zh
      enable_creation_date: true
  - minify:
      minify_html: true
      minify_js: true
      minify_css: true
  - awesome-pages
  - macros


# 扩展
markdown_extensions:
  - pymdownx.highlight:
      anchor_linenums: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - admonition
  - pymdownx.details
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.mark
  - attr_list
  - md_in_html
  - tables
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg

# 自定义
extra_css:
  - assets/extra.css
extra_javascript:
  - assets/extra.js

# 监视文件变动
watch:
  - ../src
  - ../README.md

# 文档目录
docs_dir: docs
site_dir: site

# 导航菜单
nav:
  - 简介: index.md
  - 开发者指南: developer_guide.md
  - 架构设计: architecture.md
  - 配置: configuration.md
  - API文档:
    - '总览': 'api_doc/index.md'
    - '训练接口': 'api_doc/train.md'
    - '实时预测接口': 'api_doc/realtime_predict.md'
    - '优化决策接口': 'api_doc/decision_making.md'
    - '系统端点': 'api_doc/system_endpoints.md'
  - 中间件:
    - 'URL解码中间件': 'middleware/url_decoder.md'
  - 部署指南:
    - '概述': 'deploy.md'
    - 'Linux部署': 'deploy/deploy_linux.md'
    - 'Windows部署': 'deploy/deploy_windows.md'
  - 命令指南: just_guide.md
  - 用户指南: user_guide.md