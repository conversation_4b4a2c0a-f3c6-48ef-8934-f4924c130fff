# 配置文档

## 概述

IndustryAI平台支持通过环境变量和配置文件进行灵活配置。本文档详细说明了所有可用的配置选项。

## 配置方式

### 1. 环境变量配置

推荐使用环境变量进行配置,支持以下方式:

- 直接设置环境变量
- 使用 `.env` 文件
- Docker环境变量
- 系统环境变量

### 2. 配置文件

支持YAML和JSON格式的配置文件:

- `config/config.yaml` - 主配置文件
- `config/config.dev.yaml` - 开发环境配置
- `config/config.prod.yaml` - 生产环境配置

## 核心配置项

### 服务配置

```yaml
# 服务基础配置
SERVICE_NAME: "IndustryAI"
SERVICE_VERSION: "1.0.0"
SERVICE_HOST: "0.0.0.0"
SERVICE_PORT: 8000
SERVICE_DEBUG: false
SERVICE_RELOAD: false
SERVICE_WORKERS: 4

# API配置
API_VERSION: "v1"
API_PREFIX: "/api"
API_TITLE: "IndustryAI API"
API_DESCRIPTION: "工业智能平台API接口"

# CORS配置
CORS_ORIGINS: "*"
CORS_METHODS: "GET,POST,PUT,DELETE,OPTIONS"
CORS_HEADERS: "*"
```

### 工厂配置

```yaml
# 工厂基础信息
FACTORY_NAME: "示例工厂"
FACTORY_CODE: "FACTORY_001"
FACTORY_LOCATION: "中国"
FACTORY_TIMEZONE: "Asia/Shanghai"

# 生产线配置
PRODUCTION_LINES: [
  {
    "line_id": "LINE_001",
    "line_name": "生产线1",
    "capacity": 1000,
    "status": "active"
  }
]

# 设备配置
EQUIPMENT_CONFIG: {
  "sampling_interval": 60,
  "data_retention_days": 30,
  "alert_threshold": 0.8
}
```

### 数据库配置

#### PostgreSQL配置

```yaml
# 主数据库
DB_HOST: "localhost"
DB_PORT: 5432
DB_NAME: "industryai"
DB_USER: "postgres"
DB_PASSWORD: "your_password"
DB_SCHEMA: "public"

# 连接池配置
DB_POOL_SIZE: 20
DB_MAX_OVERFLOW: 30
DB_POOL_TIMEOUT: 30
DB_POOL_RECYCLE: 3600

# SSL配置
DB_SSL_MODE: "prefer"
DB_SSL_CERT: ""
DB_SSL_KEY: ""
DB_SSL_CA: ""

# 异步数据库配置
ASYNC_DB_URL: "postgresql+asyncpg://user:pass@localhost:5432/industryai"
```

#### Redis配置

```yaml
# Redis基础配置
REDIS_HOST: "localhost"
REDIS_PORT: 6379
REDIS_DB: 0
REDIS_PASSWORD: ""
REDIS_USERNAME: ""

# 连接配置
REDIS_SOCKET_TIMEOUT: 5
REDIS_SOCKET_CONNECT_TIMEOUT: 5
REDIS_CONNECTION_POOL_MAX_CONNECTIONS: 50

# 集群配置
REDIS_CLUSTER_ENABLED: false
REDIS_CLUSTER_NODES: []

# 缓存配置
CACHE_TTL: 3600
CACHE_PREFIX: "industryai:"
```

#### InfluxDB配置

```yaml
# InfluxDB配置(时序数据)
INFLUXDB_HOST: "localhost"
INFLUXDB_PORT: 8086
INFLUXDB_DATABASE: "industryai_timeseries"
INFLUXDB_USERNAME: "admin"
INFLUXDB_PASSWORD: "your_password"
INFLUXDB_RETENTION_POLICY: "autogen"

# 批量写入配置
INFLUXDB_BATCH_SIZE: 1000
INFLUXDB_FLUSH_INTERVAL: 10
```

### 安全配置

```yaml
# JWT配置
JWT_SECRET_KEY: "your-secret-key-here"
JWT_ALGORITHM: "HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES: 30
JWT_REFRESH_TOKEN_EXPIRE_DAYS: 7

# FastAPI Guard配置
GUARD_ENABLED: true
GUARD_IP_WHITELIST: ["127.0.0.1", "::1"]
GUARD_IP_BLACKLIST: []
GUARD_COUNTRY_BLACKLIST: []
GUARD_USER_AGENT_BLACKLIST: []

# 自动封禁配置
GUARD_AUTO_BAN_ENABLED: true
GUARD_AUTO_BAN_THRESHOLD: 10
GUARD_AUTO_BAN_DURATION: 3600

# 速率限制
RATE_LIMIT_ENABLED: true
RATE_LIMIT_REQUESTS: 100
RATE_LIMIT_WINDOW: 60
```

### 日志配置

```yaml
# 日志级别
LOG_LEVEL: "INFO"
LOG_FORMAT: "json"
LOG_COLORIZE: true

# 日志文件配置
LOG_FILE_ENABLED: true
LOG_FILE_PATH: "logs/industryai.log"
LOG_FILE_ROTATION: "1 day"
LOG_FILE_RETENTION: "30 days"
LOG_FILE_MAX_SIZE: "100 MB"

# 日志组件配置
LOG_SQL_ENABLED: false
LOG_HTTP_ENABLED: true
LOG_PERFORMANCE_ENABLED: true

# 远程日志配置
LOG_REMOTE_ENABLED: false
LOG_REMOTE_URL: ""
LOG_REMOTE_TOKEN: ""
```

### 算法配置

```yaml
# 模型训练配置
TRAINING_CONFIG:
  default_epochs: 100
  default_batch_size: 32
  default_learning_rate: 0.001
  early_stopping_patience: 10
  model_save_path: "models/"
  checkpoint_interval: 10

# 预测配置
PREDICTION_CONFIG:
  default_sequence_length: 96
  default_prediction_length: 24
  max_batch_size: 1000
  cache_predictions: true
  prediction_timeout: 30

# 优化配置
OPTIMIZATION_CONFIG:
  default_algorithm: "NSGA2"
  default_population_size: 100
  default_generations: 50
  parallel_processes: 4
  convergence_threshold: 0.001
```

### 监控配置

```yaml
# 系统监控
MONITOR_ENABLED: true
MONITOR_INTERVAL: 60
MONITOR_METRICS_RETENTION: 30

# 健康检查
HEALTH_CHECK_ENABLED: true
HEALTH_CHECK_INTERVAL: 30
HEALTH_CHECK_TIMEOUT: 10

# 告警配置
ALERT_ENABLED: true
ALERT_EMAIL_ENABLED: false
ALERT_EMAIL_SMTP_HOST: ""
ALERT_EMAIL_SMTP_PORT: 587
ALERT_EMAIL_USERNAME: ""
ALERT_EMAIL_PASSWORD: ""
ALERT_EMAIL_RECIPIENTS: []

# 性能阈值
PERFORMANCE_THRESHOLDS:
  cpu_usage: 80
  memory_usage: 85
  disk_usage: 90
  response_time_ms: 1000
```

### 开发配置

```yaml
# 开发模式配置
DEVELOPMENT_MODE: false
DEBUG_SQL: false
DEBUG_PERFORMANCE: false

# 测试配置
TEST_DATABASE_URL: "sqlite:///test.db"
TEST_REDIS_URL: "redis://localhost:6379/1"

# 代码热重载
HOT_RELOAD_ENABLED: false
HOT_RELOAD_DIRS: ["src/"]

# API文档配置
DOCS_ENABLED: true
DOCS_URL: "/docs"
REDOC_URL: "/redoc"
```

## 环境特定配置

### 开发环境 (.env.dev)

```bash
# 服务配置
SERVICE_DEBUG=true
SERVICE_RELOAD=true
SERVICE_WORKERS=1

# 数据库配置
DB_HOST=localhost
DB_NAME=industryai_dev
DB_USER=dev_user
DB_PASSWORD=dev_password

# Redis配置
REDIS_DB=1

# 日志配置
LOG_LEVEL=DEBUG
LOG_COLORIZE=true

# 开发工具
DEVELOPMENT_MODE=true
HOT_RELOAD_ENABLED=true
DOCS_ENABLED=true
```

### 测试环境 (.env.test)

```bash
# 服务配置
SERVICE_DEBUG=false
SERVICE_WORKERS=2

# 数据库配置
DB_HOST=test-db-host
DB_NAME=industryai_test
DB_USER=test_user
DB_PASSWORD=test_password

# Redis配置
REDIS_DB=2

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_ENABLED=true

# 测试配置
TEST_MODE=true
```

### 生产环境 (.env.prod)

```bash
# 服务配置
SERVICE_DEBUG=false
SERVICE_RELOAD=false
SERVICE_WORKERS=8

# 数据库配置
DB_HOST=prod-db-host
DB_NAME=industryai_prod
DB_USER=prod_user
DB_PASSWORD=${DB_PASSWORD}

# Redis配置
REDIS_HOST=prod-redis-host
REDIS_PASSWORD=${REDIS_PASSWORD}

# 安全配置
JWT_SECRET_KEY=${JWT_SECRET_KEY}
GUARD_ENABLED=true

# 日志配置
LOG_LEVEL=WARNING
LOG_FILE_ENABLED=true
LOG_REMOTE_ENABLED=true

# 监控配置
MONITOR_ENABLED=true
ALERT_ENABLED=true
ALERT_EMAIL_ENABLED=true

# 性能优化
DB_POOL_SIZE=50
REDIS_CONNECTION_POOL_MAX_CONNECTIONS=100
```

## 配置验证

### 使用Just命令验证配置

```bash
# 检查配置完整性
just config-check

# 显示当前配置
just config-show

# 测试数据库连接
just db-test

# 测试Redis连接
just redis-test

# 验证所有服务
just health-check
```

### 配置验证脚本

```python
# scripts/validate_config.py
import os
from typing import Dict, Any
from pydantic import BaseSettings, validator

class IndustryAIConfig(BaseSettings):
    # 服务配置
    service_name: str = "IndustryAI"
    service_host: str = "0.0.0.0"
    service_port: int = 8000
    
    # 数据库配置
    db_host: str
    db_port: int = 5432
    db_name: str
    db_user: str
    db_password: str
    
    @validator('service_port')
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError('端口号必须在1-65535之间')
        return v
    
    @validator('db_password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('数据库密码长度不能少于8位')
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# 验证配置
def validate_config():
    try:
        config = IndustryAIConfig()
        print("✅ 配置验证通过")
        return config
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return None

if __name__ == "__main__":
    validate_config()
```

## 配置最佳实践

### 1. 敏感信息管理

```bash
# 使用环境变量存储敏感信息
export DB_PASSWORD="$(cat /run/secrets/db_password)"
export JWT_SECRET_KEY="$(openssl rand -hex 32)"
export REDIS_PASSWORD="$(cat /run/secrets/redis_password)"
```

### 2. 配置分层

```yaml
# 基础配置 (config/base.yaml)
service:
  name: "IndustryAI"
  version: "1.0.0"

# 环境特定配置 (config/prod.yaml)
service:
  debug: false
  workers: 8

database:
  pool_size: 50
  ssl_mode: "require"
```

### 3. 配置模板

```bash
# 创建配置模板
cp .env.example .env

# 使用envsubst替换变量
envsubst < config/config.template.yaml > config/config.yaml
```

### 4. 配置备份

```bash
# 备份配置
just config-backup

# 恢复配置
just config-restore backup-20240115.tar.gz
```

## 故障排除

### 常见配置问题

1. **数据库连接失败**
   ```bash
   # 检查数据库配置
   just db-test
   
   # 检查网络连接
   telnet $DB_HOST $DB_PORT
   ```

2. **Redis连接失败**
   ```bash
   # 测试Redis连接
   redis-cli -h $REDIS_HOST -p $REDIS_PORT ping
   ```

3. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8000
   
   # 修改服务端口
   export SERVICE_PORT=8001
   ```

4. **权限问题**
   ```bash
   # 检查文件权限
   ls -la logs/
   
   # 修复权限
   chmod 755 logs/
   chown app:app logs/
   ```

### 配置调试

```python
# 启用配置调试
import os
os.environ['DEBUG_CONFIG'] = 'true'

# 查看加载的配置
from industryai.core.config import settings
print(settings.dict())
```

## 相关文档

- [部署指南](deploy.md)
- [Just命令指南](just_guide.md)
- [API文档](api_doc/index.md)
- [用户指南](user_guide.md)