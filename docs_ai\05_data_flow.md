
# 5. 数据流向图

本部分描述了两种核心场景下的数据流和控制流:**API 调用** 和 **后台任务执行**。

## 5.1. 场景一:API 请求启动一个训练任务

此流程展示了当用户通过 API 请求启动一个定时训练任务时,系统内部的交互过程。

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant WebServer as uvicorn/main.py
    participant App as app.py
    participant Router as routers/train.py
    participant Scheduler as utils/task_scheduler.py
    participant Trainer as industrytslib.Trainer

    Client->>+WebServer: POST /train/train (json: TrainModelRequestBody)
    WebServer->>+App: Forward Request
    App->>+Router: Route to train()
    Router->>Router: 解析 RequestBody
    Router->>+Scheduler: train_scheduler.add_job(trainer.main, ...)
    Scheduler-->>-Router: Job Scheduled (ID: project_name)
    Router-->>-App: HTTP 200 OK
    App-->>-WebServer: HTTP 200 OK
    WebServer-->>-Client: {"code": 200, "message": "..."}

    loop 定时触发 (e.g., every 2 hours)
        Scheduler->>+Trainer: main()
        Trainer->>Trainer: 执行训练逻辑...
        Trainer-->>-Scheduler: Task Finished
    end
```

**流程说明:**

1.  **客户端** 发送一个 POST 请求到 `/train/train`。
2.  **Uvicorn** 接收请求并传递给 **`main.py`**,最终到达 **`app.py`**。
3.  **FastAPI (`app.py`)** 根据 URL 将请求路由到 **`routers/train.py`** 中的 `train` 函数。
4.  **`train` 函数** 解析请求体,验证参数。
5.  它调用 **`train_scheduler.add_job()`**,将 `industrytslib` 中 `Trainer` 类的 `main` 方法注册为一个新的定时任务。任务的 ID 被设置为 `project_name`。
6.  调度器立即返回,API 请求成功响应。
7.  此后,**`train_scheduler`** 会在每个指定的时间间隔(如2小时)自动在后台线程池中执行 `Trainer.main()` 方法,完成模型训练。

## 5.2. 场景二:后台心跳检测

此流程展示了应用内部的后台任务如何独立运行。

```mermaid
sequenceDiagram
    participant App as app.py (lifespan)
    participant Scheduler as APScheduler (Global)
    participant Heartbeat as industrytslib.SystemOperationStatus

    App->>+Scheduler: add_job(heartbeat_task.run, trigger='interval', ...)
    Scheduler-->>-App: Job Scheduled

    loop 定时触发 (e.g., every 10 seconds)
        Scheduler->>+Heartbeat: run()
        Heartbeat->>Heartbeat: 检查系统状态
        Heartbeat->>Database: 写入心跳信号
        Heartbeat-->>-Scheduler: Task Finished
    end
```

**流程说明:**

1.  在应用启动时,**`app.py`** 的 `lifespan` 管理器会执行初始化逻辑。
2.  如果心跳任务被启用 (`_HEARTBEAT_ENABLED=true`),它会创建一个 `SystemOperationStatus` 实例。
3.  `add_job` 被调用,将 `heartbeat_task.run` 方法注册到全局调度器 `scheduler` 中,设置为每10秒执行一次。
4.  应用启动完成后,`scheduler` 会在后台独立运行,每10秒钟调用一次 `Heartbeat.run()` 方法,将心跳信息写入数据库,从而实现系统状态的持续监控。
