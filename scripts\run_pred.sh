#!/bin/bash
# 请使用bash 执行脚本
# ! 在industryai根目录下执行, 不要在scripts目录下执行
# ! 请事先激活配置好的python虚拟环境,默认为 conda activate exp
# sh scripts/run_pred.sh

# 检查当前conda环境
current_env=$(conda info --envs | grep '*' | awk '{print $1}')
# 打印当前环境
echo "当前使用的python环境为: $current_env"
# 根据which python判断打印当前python的位置
python_path=$(which python)
echo "python路径: $python_path"

export PYTHONPATH=${PYTHONPATH:+$PYTHONPATH:}$(pwd)
echo "项目路径: $PYTHONPATH"

# 项目配置数组,格式:项目名|间隔时间|预测器类型
project_configs=(
    "Bubble缺陷样本_Bubble缺陷样本_CNN|60|classic_mo" 
    "Knot缺陷样本_Knot缺陷样本_CNN|60|classic_mo" 
    "Stone缺陷样本_Stone缺陷样本_CNN|60|classic_mo"
    "PointDefect缺陷样本_PointDefect缺陷样本_CNN|60|classic_mo"
)

# 构建批量预测的命令行参数
config_args=()
for config in "${project_configs[@]}"; do
    config_args+=("--config" "$config")
done

echo "开始批量请求实时预测，共 ${#project_configs[@]} 个项目"
echo "项目配置列表:"
for config in "${project_configs[@]}"; do
    IFS='|' read -r project_name interval predictor_type <<< "$config"
    echo "  - $project_name (间隔: ${interval}秒, 类型: $predictor_type)"
done
echo "---"

# 使用新的批量处理接口
python src/backend/requestes/requests_predictor.py batch "${config_args[@]}"

echo "批量预测请求完成"

# 或者使用简短的参数形式:
# python requestes/requests_predictor.py -p "二线回转窑电耗样本_二线回转窑电耗样本_CNN" -i 30 -t "classic"