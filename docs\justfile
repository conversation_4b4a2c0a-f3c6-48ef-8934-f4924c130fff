# MkDocs documentation tasks for industrytslib
# This justfile provides cross-platform commands for building and serving documentation

# Show available commands
help:
    @just --list

# Build the documentation (generates static files in site/ directory)
build:
    #!/usr/bin/env bash
    if command -v pwsh &> /dev/null; then
        pwsh -Command "uv run mkdocs build"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
        powershell.exe -Command "uv run mkdocs build"
    else
        uv run mkdocs build
    fi

# Serve the documentation locally (default: http://localhost:8000)
serve:
    #!/usr/bin/env bash
    if command -v pwsh &> /dev/null; then
        pwsh -Command "uv run mkdocs serve"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
        powershell.exe -Command "uv run mkdocs serve"
    else
        uv run mkdocs serve
    fi

# Clean build artifacts
clean:
    #!/usr/bin/env bash
    if command -v pwsh &> /dev/null; then
        pwsh -Command "Remove-Item -Recurse -Force site -ErrorAction SilentlyContinue"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
        powershell.exe -Command "Remove-Item -Recurse -Force site -ErrorAction SilentlyContinue"
    else
        rm -rf site
    fi

# Build and serve in one command
dev: build serve