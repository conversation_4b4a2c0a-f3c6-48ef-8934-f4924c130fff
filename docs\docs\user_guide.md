# IndustryAI 用户指南

本指南将帮助您快速上手IndustryAI工业智能平台,包括安装、配置和使用说明。

## 系统概述

IndustryAI是一个专业的工业智能平台,基于FastAPI和PyTorch构建,专注于工业时间序列建模、实时预测和智能决策。平台使用industrytslib作为核心算法库,提供完整的工业AI解决方案。

### 主要功能模块

- **🏭 模型训练**: 支持TimesNet、Informer、PatchTST、Mamba等先进算法
- **🔄 实时预测**: 高性能异步预测管道,支持序列预测、软测量、质量预测
- **🧠 智能决策**: 基于强化学习和多目标优化的决策系统
- **📊 数据处理**: 基于Polars的高性能数据处理引擎
- **🔒 安全防护**: FastAPI Guard安全中间件,企业级安全保护
- **💾 数据存储**: 支持PostgreSQL、MSSQL、InfluxDB、Redis等多种数据库

## 系统要求

### 硬件要求
- **CPU**: Intel i5 8代或AMD Ryzen 5 3600及以上
- **内存**: 最低16GB,推荐32GB或更多
- **存储**: 至少50GB可用空间,推荐SSD
- **GPU**: 可选,支持CUDA 12.8+的NVIDIA显卡(用于深度学习加速)
- **网络**: 稳定的网络连接,支持高并发访问

### 软件要求
- **操作系统**: Windows 10/11, Ubuntu 20.04+, CentOS 8+
- **Python**: 3.11及以上版本(必需)
- **Just**: 统一命令管理工具
- **Docker**: 20.10及以上版本(可选,用于容器化部署)
- **数据库**: PostgreSQL 13+, Redis 6+, InfluxDB 2.0+(可选)

## 安装指南

### 快速安装 (推荐)

使用Just命令工具进行一键部署:

```bash
# 1. 安装Just命令工具
# Ubuntu/Debian
sudo apt install just

# 或使用Cargo安装
cargo install just

# 2. 查看可用命令
just --list

# 3. 启动服务(默认阳泉工厂)
just server

# 或指定工厂
just server "北京工厂"
```

### 详细安装步骤

#### 1. 环境准备

**检查Python版本**:
```bash
python --version  # 需要3.11+
```

**安装Just命令工具**:
```bash
# Ubuntu/Debian
sudo apt install just

# CentOS/RHEL
sudo dnf install just

# 或使用Cargo
cargo install just
```

#### 2. 获取项目

```bash
# 克隆项目(如果有Git仓库)
git clone <repository-url> industryai
cd industryai

# 或解压项目包
tar -xzf industryai.tar.gz
cd industryai
```

#### 3. 环境配置

**创建虚拟环境**:
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 .\venv\Scripts\Activate.ps1  # Windows
```

**安装依赖**:
```bash
pip install -r requirements.txt

# 如果需要GPU支持
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

**配置环境变量**:
```bash
cp .env.example .env
nano .env  # 编辑配置文件
```

#### 4. 数据库配置

**PostgreSQL**:
```bash
# Ubuntu/Debian
sudo apt install postgresql postgresql-contrib

# 创建数据库
sudo -u postgres createdb industryai
```

**Redis**:
```bash
# Ubuntu/Debian
sudo apt install redis-server

# 启动Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### 5. 启动服务

**使用Just命令**:
```bash
# 开发模式
just dev

# 生产模式
just server

# 指定工厂
just server "工厂名称"
```

**手动启动**:
```bash
# 激活虚拟环境
source venv/bin/activate

# 启动服务
uvicorn main:app --host 0.0.0.0 --port 8081 --workers 4

# 或直接运行
python main.py
```

## Docker部署指南

### 前置条件
- 已安装Docker 20.10+和Docker Compose v2
- 确保端口8081未被占用
- 至少4GB可用内存

### 部署步骤

#### 1. 使用Just命令部署 (推荐)

```bash
# 构建Docker镜像
just docker-build

# 启动Docker服务
just docker-up

# 停止Docker服务
just docker-down

# 查看服务状态
just docker-status
```

#### 2. 手动Docker部署

**构建镜像**:
```bash
# 构建基础镜像
docker build -t industryai:latest .

# 构建多阶段镜像(生产环境)
docker build -f Dockerfile.prod -t industryai:prod .
```

**运行容器**:
```bash
# 开发环境
docker run -d \
  --name industryai-dev \
  -p 8081:8081 \
  -v $(pwd):/app \
  -e ENVIRONMENT=development \
  industryai:latest

# 生产环境
docker run -d \
  --name industryai-prod \
  -p 8081:8081 \
  -e ENVIRONMENT=production \
  -e FACTORY_NAME="生产工厂" \
  --restart unless-stopped \
  industryai:prod
```

#### 3. 使用Docker Compose (推荐)

**创建docker-compose.yml**:
```yaml
version: '3.8'

services:
  industryai:
    build: .
    ports:
      - "8081:8081"
    environment:
      - ENVIRONMENT=production
      - FACTORY_NAME=Docker工厂
      - DATABASE_URL=**************************************/industryai
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=industryai
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - industryai

volumes:
  postgres_data:
  redis_data:
```

**启动服务栈**:
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f industryai

# 停止服务
docker-compose down
```

### 环境变量配置

创建`.env`文件用于Docker环境:
```env
# 服务配置
ENVIRONMENT=production
FACTORY_NAME=Docker工厂
HOST=0.0.0.0
PORT=8081
WORKERS=4

# 数据库配置
DATABASE_URL=**************************************/industryai
REDIS_URL=redis://redis:6379/0

# 安全配置
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/app/logs/industryai.log
```

### 验证部署

```bash
# 检查容器状态
docker ps

# 测试API接口
curl http://localhost:8081/

# 检查健康状态
curl http://localhost:8081/health

# 访问API文档
open http://localhost:8081/docs
```

### 生产环境优化

**资源限制**:
```yaml
services:
  industryai:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
```

**健康检查**:
```yaml
services:
  industryai:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

### 镜像导出与导入

1. 导出压缩镜像
   ```bash
   sudo docker save industryai | gzip > industryai.tar.gz
   ```

2. 导入压缩镜像
   ```bash
   gunzip -c industryai.tar.gz | sudo docker load
   ```

3. 验证镜像
   ```bash
   docker images | grep industryai
   ```

### Docker日志查看
```bash
# 实时查看容器日志
docker logs -f <container_id>

# 查看最近100行日志
docker logs --tail 100 <container_id>

# 查看最近30分钟的日志
docker logs --since 30m <container_id>
```

## 配置管理

### 环境变量配置

系统使用`.env`文件进行配置管理,支持多环境配置。

#### 基础配置文件

**复制配置模板**:
```bash
cp .env.example .env
```

**主要配置项**:
```env
# ==================== 服务配置 ====================
ENVIRONMENT=production          # 运行环境: development, production
HOST=0.0.0.0                   # 服务监听地址
PORT=8081                      # 服务端口
WORKERS=4                      # 工作进程数
RELOAD=false                   # 热重载(仅开发环境)

# ==================== 工厂配置 ====================
FACTORY_NAME=阳泉工厂           # 工厂名称
FACTORY_CODE=YQ001             # 工厂代码
FACTORY_LOCATION=山西阳泉       # 工厂位置
TIMEZONE=Asia/Shanghai         # 时区设置

# ==================== 数据库配置 ====================
# PostgreSQL主数据库
DATABASE_URL=postgresql://user:password@localhost:5432/industryai
DB_POOL_SIZE=20                # 连接池大小
DB_MAX_OVERFLOW=30             # 最大溢出连接
DB_POOL_TIMEOUT=30             # 连接超时(秒)

# Redis缓存数据库
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=                # Redis密码(可选)
REDIS_MAX_CONNECTIONS=50       # 最大连接数

# InfluxDB时序数据库(可选)
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-token
INFLUXDB_ORG=your-org
INFLUXDB_BUCKET=industryai

# ==================== 安全配置 ====================
SECRET_KEY=your-super-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# FastAPI Guard配置
GUARD_ENABLED=true             # 启用安全防护
GUARD_IP_WHITELIST=127.0.0.1,***********/24
GUARD_RATE_LIMIT=100           # 每分钟请求限制
GUARD_BLOCK_DURATION=3600      # 封禁时长(秒)

# ==================== 日志配置 ====================
LOG_LEVEL=INFO                 # 日志级别: DEBUG, INFO, WARNING, ERROR
LOG_FILE=logs/industryai.log   # 日志文件路径
LOG_MAX_SIZE=100MB             # 单个日志文件最大大小
LOG_BACKUP_COUNT=10            # 保留日志文件数量
LOG_FORMAT=detailed            # 日志格式: simple, detailed, json

# ==================== 算法配置 ====================
# 模型存储路径
MODEL_STORAGE_PATH=./models
MODEL_CACHE_SIZE=100           # 模型缓存数量

# GPU配置
CUDA_VISIBLE_DEVICES=0         # 可用GPU设备
USE_GPU=true                   # 是否使用GPU
GPU_MEMORY_FRACTION=0.8        # GPU内存使用比例

# 训练配置
MAX_TRAINING_JOBS=5            # 最大并行训练任务数
TRAINING_TIMEOUT=3600          # 训练超时时间(秒)

# 预测配置
MAX_PREDICTION_JOBS=20         # 最大并行预测任务数
PREDICTION_TIMEOUT=300         # 预测超时时间(秒)
PREDICTION_BATCH_SIZE=1000     # 预测批次大小

# ==================== 监控配置 ====================
# 性能监控
MONITORING_ENABLED=true        # 启用性能监控
METRICS_ENDPOINT=/metrics      # 指标端点
HEALTH_CHECK_INTERVAL=30       # 健康检查间隔(秒)

# 告警配置
ALERT_EMAIL_ENABLED=false      # 邮件告警
ALERT_EMAIL_SMTP_HOST=smtp.gmail.com
ALERT_EMAIL_SMTP_PORT=587
ALERT_EMAIL_USERNAME=<EMAIL>
ALERT_EMAIL_PASSWORD=your-app-password
ALERT_EMAIL_TO=<EMAIL>

# ==================== 开发配置 ====================
# 仅在开发环境使用
DEBUG=false                    # 调试模式
AUTO_RELOAD=false              # 自动重载
API_DOCS_ENABLED=true          # API文档
CORS_ENABLED=true              # 跨域支持
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
```

### 配置文件层级

系统支持多层级配置,优先级从高到低:

1. **环境变量** - 系统环境变量
2. **.env.local** - 本地开发配置(不提交到版本控制)
3. **.env.{environment}** - 环境特定配置
4. **.env** - 默认配置文件
5. **默认值** - 代码中的默认值

### 环境特定配置

**开发环境** (`.env.development`):
```env
ENVIRONMENT=development
DEBUG=true
RELOAD=true
LOG_LEVEL=DEBUG
API_DOCS_ENABLED=true
WORKERS=1
```

**测试环境** (`.env.testing`):
```env
ENVIRONMENT=testing
DATABASE_URL=postgresql://test:test@localhost:5432/industryai_test
REDIS_URL=redis://localhost:6379/1
LOG_LEVEL=WARNING
```

**生产环境** (`.env.production`):
```env
ENVIRONMENT=production
DEBUG=false
RELOAD=false
LOG_LEVEL=INFO
API_DOCS_ENABLED=false
WORKERS=8
GUARD_ENABLED=true
```

### 配置验证

系统启动时会自动验证配置:

```bash
# 验证配置文件
just config-check

# 显示当前配置
just config-show

# 测试数据库连接
just db-test
```

### 敏感信息管理

**生产环境建议**:
1. 使用环境变量存储敏感信息
2. 不要将`.env`文件提交到版本控制
3. 使用密钥管理服务(如HashiCorp Vault)
4. 定期轮换密钥和密码

**示例环境变量设置**:
```bash
# Linux/macOS
export DATABASE_URL="postgresql://user:password@localhost:5432/industryai"
export SECRET_KEY="your-secret-key"

# Windows
set DATABASE_URL=postgresql://user:password@localhost:5432/industryai
set SECRET_KEY=your-secret-key
```

## 授权管理

### 获取授权

1. 获取硬件ID
   ```bash
   python license_tool.py get-id
   ```

2. 生成授权文件
   ```bash
   # 标准授权(365天)
   python license_tool.py generate --days 365 --hardware-id <hardware_id>

   # 试用版授权(30天)
   python license_tool.py generate --days 30 --trial

   # 带硬件绑定的试用版授权
   python license_tool.py generate --days 30 --hardware-id <hardware_id> --trial
   ```

### 程序打包

```bash
# 打包标准版
python license_tool.py pack

# 打包试用版(30天试用期)
python license_tool.py pack --trial-days 30
```

## 使用指南

### 服务操作
系统提供三种主要操作模式:

1. 训练模式
   ```bash
   sh run_train.sh
   ```

2. 实时预测模式
   ```bash
   sh run_pred.sh
   ```

3. 优化决策模式
   ```bash
   sh run_opt.sh
   ```

### API文档
详细的API文档可以在`docs/api_doc`目录中找到。

## 注意事项
1. 项目使用Docker作为主要的打包部署工具,因为项目结构和依赖较为复杂,不支持nuitka打包。
2. 项目使用pyarmor进行代码加密,并使用pyarmor-webui进行授权管理(开发中)。
3. 可以使用pyc打包作为替代的发布部署方式,运行`python pyc_build.py`即可。

## 故障排除
如果遇到问题,请检查:
1. Python环境是否正确配置(3.11版本)
2. 授权文件是否有效
3. Docker容器的日志输出
4. 网络端口(8000)是否被占用

## 技术支持
如需技术支持,请参考API文档或提交issue到项目仓库。
