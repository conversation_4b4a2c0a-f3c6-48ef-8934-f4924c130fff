#!/bin/bash
# 请使用bash 执行脚本
# ! 在industryai根目录下执行, 不要在scripts目录下执行
# ! 请事先激活配置好的python虚拟环境,默认为 conda activate exp
# sh scripts/run.sh

# 检查当前conda环境
current_env=$(conda info --envs | grep '*' | awk '{print $1}')
# 打印当前环境
echo "当前使用的python环境为: $current_env"

# 根据which python判断打印当前python的位置
python_path=$(which python)
echo "python路径: $python_path"

# 加载 .env 文件(如果存在)
if [ -f .env ]; then
  echo "正在加载 .env 文件..."
  # 逐行读取 .env 文件,清理并导出变量,以兼容 "KEY = VALUE" 格式
  while IFS= read -r line; do
    # 跳过注释和空行
    [[ "$line" =~ ^\s*# ]] || [[ -z "$line" ]] && continue
    # 导出清理后的行
    export "$(echo "$line" | sed -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//' -e 's/\s*=\s*/=/')"
  done < .env
fi

export PYTHONPATH=${PYTHONPATH:+$PYTHONPATH:}$(pwd)
echo "项目路径: $PYTHONPATH"

# 从环境变量读取PLANT,如果不存在则使用默认值"阳泉"
export PLANT=${PLANT:-阳泉}
echo "PLANT: $PLANT"

# 从环境变量读取BACKGROUND_TASK,如果不存在则使用默认值"True"
export BACKGROUND_TASK=${BACKGROUND_TASK:-True}
echo "BACKGROUND_TASK: $BACKGROUND_TASK"

# 需要事先激活配置好的python虚拟环境
# uvicorn main:app --host 0.0.0.0 --port 8999
# 后台任务配置直接通过.env中的Background_task变量控制
# TODO 读取环境变量的host和port
python src/backend/main.py --host "${HOST:-0.0.0.0}" --port "${PORT:-8999}"
