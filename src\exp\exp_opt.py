"""
运行决策测试
"""
import os
import sys
import time

from datetime import datetime

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.executors.pool import ThreadPoolExecutor
from industrytslib.core import get_decision_agent
from industrytslib.utils.readconfig import read_config_toml
from industrytslib.utils.logutils import get_logger


# 配置日志
logger = get_logger(
    logger_name="opt_mo",
    logger_type="exp",
    level="INFO",
    console_level="INFO",
    rotation="00:00",
    retention="14 days"
)

# Add both current directory and parent directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
os.chdir(parent_dir)  # 切换到项目根目录

# 决策优化项目列表
opt_list = [
    "天然气优化_天然气优化_OPT"
]

# 读取配置文件
config = read_config_toml("config/database_config.toml")

def decision_task(agent, project_name):
    """单个决策智能体的任务函数"""
    try:
        logger.info(f"执行决策优化任务: {project_name}")
        agent.main()
        logger.info(f"决策优化任务完成: {project_name}")
    except Exception as e:
        logger.error(f"决策优化任务出错 {project_name}: {str(e)}")

def main_with_scheduler():
    """使用APScheduler进行任务调度的主函数"""
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # 创建决策智能体列表
    agent_list = []
    for project_name in opt_list:
        try:
            agent = get_decision_agent(project_name, config)
            logger.info(f"决策智能体 {project_name} 创建成功!")
            agent_list.append((agent, project_name))
        except Exception as e:
            logger.error(f"创建决策智能体 {project_name} 失败: {str(e)}")
    
    # 配置调度器
    executors = {
        'default': ThreadPoolExecutor(max_workers=len(agent_list) + 2)
    }
    
    scheduler = BackgroundScheduler(executors=executors)
    
    # 将每个决策智能体的任务添加到调度器
    for agent, project_name in agent_list:
        scheduler.add_job(
            decision_task, 
            'interval',
            args=[agent, project_name],
            seconds=300,  # 决策优化任务间隔更长,5分钟
            id=f"decision_{project_name}",
            max_instances=1,
            next_run_time=datetime.now(),  # 设置立即执行
            coalesce=True,  # 合并执行错过的任务
            misfire_grace_time=60  # 任务错过执行时间的容忍度
        )
    
    try:
        logger.info("启动决策优化调度器...")
        scheduler.start()
        
        # 保持程序运行
        while True:
            time.sleep(60)
            
    except (KeyboardInterrupt, SystemExit):
        logger.info("正在关闭决策优化调度器...")
        scheduler.shutdown()
        logger.info("决策优化调度器已关闭")

if __name__ == "__main__":
    main_with_scheduler()
