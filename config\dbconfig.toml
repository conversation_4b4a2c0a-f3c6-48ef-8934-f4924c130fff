[mssql.web]
# Web Server
type = "mssql"
target = "web"
server = "10.2.0.77"
port = 1433
database = "eladmin"
username = "sa"
password = "ysdxdckj@666"

[mssql.realtimepredict]
# Real-time Prediction Data Server
type = "mssql"
target = "realtime_predict"
server = "10.2.0.77"
port = 1433
database = "RealtimePredict"
username = "sa"
password = "ysdxdckj@666"

[mssql.timeseries]
# Time Series Data Server
type = "mssql"
target = "timeseries"
server = "10.2.0.77"
port = 1433
database = "控制器数据库"
username = "sa"
password = "ysdxdckj@666"

[mssql.decision_history]
type = "mssql"
target = "decision_history"
server = "10.2.0.77"
port = 1433
database = "控制器数据库"
username = "sa"
password = "ysdxdckj@666"

[mssql.predict_transaction_source]
type = "mssql"
target = "predict_transaction_source"
server = "10.2.0.77"
port = 1433
database = "RealtimePredict"
username = "sa"
password = "ysdxdckj@666"

[mssql.predict_transaction_target]
type = "mssql"
target = "predict_transaction_target"
server = "10.2.0.77"
port = 1433
database = "控制器数据库"
username = "sa"
password = "ysdxdckj@666"

[mssql.quality]
# Quality Inspection Data Server
type = "mssql"
target = "quality"
server = "10.2.0.77"
port = 1433
database = "样本检验结果"
username = "sa"
password = "ysdxdckj@666"

[mongodb]
type = "mongodb"
url = "mongodb://10.2.0.77:27017"
database = "timeseries"
username = "admin"
password = "ysdxdckj@666"

[influxdb]
# InfluxDB
type = "influxdb"
url = "http://192.168.31.5:8086"
token = "qAAxOG6EbMOZkVQY2nSKWVASRH5k-YNVNVkJKHUbNc9w9oA54OUfEaXg4VBPLxXeNaTIr_hDp29r414wrY9q0Q=="
org = "dckj"

[sqlite.model_info]
type = "sqllite"
database = "config/model_info.db"

[model_info_location]
# model info location, including sqlite, mssql, toml, default is sqlite
# location = "sqlite"
location = "toml"
