
# AI 生成的 `src/backend` 文档

本文档由 AI 根据 `src/backend` 的源代码自动生成,旨在为开发人员提供一个高信息密度的参考。

## 文档结构

1.  [**架构概览 (`01_architecture_overview.md`)**](./01_architecture_overview.md):
    提供项目的高层视图,包括技术栈、架构风格和组件职责。

2.  [**目录结构 (`02_directory_structure.md`)**](./02_directory_structure.md):
    详细解释每个目录和关键文件的用途。

3.  [**依赖关系图 (`03_dependency_graph.md`)**](./03_dependency_graph.md):
    使用 Mermaid 图展示了关键模块之间的依赖关系。

4.  [**核心类/接口 (`04_core_classes_interfaces.md`)**](./04_core_classes_interfaces.md):
    介绍了项目中的主要类和 Pydantic 数据模型。

5.  [**数据流向图 (`05_data_flow.md`)**](./05_data_flow.md):
    通过序列图展示了关键场景下的数据和控制流。

6.  [**API 接口清单 (`06_api_endpoints.md`)**](./06_api_endpoints.md):
    列出了所有可用的 API 端点及其功能。

7.  [**代码模式和约定 (`07_conventions_and_patterns.md`)**](./07_conventions_and_patterns.md):
    总结了项目中常见的编程模式和设计约定。

## 如何使用

- **快速入门**: 从 [架构概览](./01_architecture_overview.md) 开始,了解项目的整体结构。
- **功能查找**: 如果你想了解特定功能(如模型训练),可以从 [API 接口清单](./06_api_endpoints.md) 入手,找到对应的路由,然后追溯到相关的代码。
- **代码修改**: 在修改或添加代码前,请参考 [代码模式和约定](./07_conventions_and_patterns.md),以保持代码风格的一致性。
