"""
任务状态监控服务

本程序作为一个独立的后台服务运行,其主要职责是:
1. 定期通过HTTP请求,向FastAPI应用服务器查询当前所有正在运行的任务。
2. 解析从API返回的任务数据,这些数据分为训练、预测和决策三大类。
3. 将获取到的任务状态实时、准确地写入数据库,以便其他系统进行监控和展示。
4. 程序被设计为可持续运行的模式,并具备错误处理能力,确保服务的稳定性。
"""
import httpx
import sys
import os
from datetime import datetime
from dotenv import load_dotenv
from apscheduler.schedulers.blocking import BlockingScheduler


from industrytslib.utils.logutils_tamga import get_logger
from industrytslib.utils.database import create_web_client
from industrytslib import read_config_toml

# --- 初始化环境变量 ---
load_dotenv()

# --- 从环境变量读取服务配置 ---
# 如果未设置,则使用默认值
HOST = os.getenv('HOST', '127.0.0.1')
PORT = int(os.getenv('PORT', 8999))

# 对于客户端连接,如果HOST是0.0.0.0,则应连接到localhost
CONNECT_HOST = '127.0.0.1' if HOST == '0.0.0.0' else HOST
API_BASE_URL = f"http://{CONNECT_HOST}:{PORT}"

QUERY_ALL_RUNNING_TASKS_URL = f"{API_BASE_URL}/query_all_running_tasks"
# 从.env文件读取监控轮询间隔,若未配置则默认为60秒
TASK_MONITOR_INTERVAL_SECONDS = int(os.getenv('TASK_MONITOR_INTERVAL_SECONDS', 60))


# --- 日志记录器初始化 ---
logger = get_logger(
    logger_name='task_monitor_service',
    logger_type='sys',
    level="DEBUG",
    console_level="WARNING",
)


def process_tasks(running_tasks, web_client):
    """
    处理从API获取的任务数据,并更新到数据库。

    Args:
        running_tasks (dict): 从API /query_all_running_tasks 获取的任务字典。
        web_client: 数据库Web客户端实例。
    """
    # 首先清空旧的状态记录
    web_client.fresh_thread_status_table()
    logger.debug("旧任务状态表已清空。")

    # 分别获取各类任务
    train_jobs = running_tasks.get('train_scheduler', [])
    predict_jobs = running_tasks.get('predict_scheduler', [])
    decision_jobs = running_tasks.get('decision_scheduler', [])

    total_tasks = len(train_jobs) + len(predict_jobs) + len(decision_jobs)
    logger.info(f"获取到 {total_tasks} 个正在运行的任务: "
                f"{len(train_jobs)} 训练, {len(predict_jobs)} 预测, {len(decision_jobs)} 决策。")

    # 处理训练任务
    for job in train_jobs:
        project_name = job.get('id', 'unknown_train_job')
        logger.debug(f"更新训练任务状态: {project_name}")
        web_client.update_thread_status(
            update_time=datetime.now(),
            project_name=project_name,
            project_type='predict',
            train_status=1,
            predict_status=0
        )

    # 处理预测任务
    for job in predict_jobs:
        project_name = job.get('id', 'unknown_predict_job')
        logger.debug(f"更新预测任务状态: {project_name}")
        web_client.update_thread_status(
            update_time=datetime.now(),
            project_name=project_name,
            project_type='predict',
            train_status=0,
            predict_status=1
        )

    # 处理决策任务
    for job in decision_jobs:
        project_name = job.get('id', 'unknown_decision_job')
        logger.debug(f"更新决策任务状态: {project_name}")
        web_client.update_thread_status(
            update_time=datetime.now(),
            project_name=project_name,
            project_type='decision',
            train_status=0,
            predict_status=1
        )
    logger.info("所有任务状态更新完成。")


def run_monitoring_cycle(web_client):
    """
    执行单次监控周期。

    包括从API获取数据、解析数据和更新数据库。
    内置了错误处理机制以确保单次失败不影响整体服务。

    Args:
        web_client: 数据库Web客户端实例。
    """
    logger.debug("开始新一轮任务状态监控...")
    try:
        # 1. 查询所有在运行的任务
        response = httpx.get(QUERY_ALL_RUNNING_TASKS_URL, timeout=10)
        response.raise_for_status()  # 如果状态码是 4xx 或 5xx,则抛出异常
        running_tasks = response.json()

        # 2. 处理任务并更新数据库
        process_tasks(running_tasks, web_client)

    except httpx.RequestError as e:
        logger.error(f"请求API失败,请检查网络连接或FastAPI服务是否已启动: {e}")
    except httpx.HTTPStatusError as e:
        logger.error(f"API返回错误状态码 {e.response.status_code}: {e.response.text}")
    except Exception as e:
        # 捕获其他所有未知异常,如JSON解码失败等
        logger.error(f"处理任务时发生未知错误: {e}")


def main():
    """
    程序主入口函数。

    负责初始化并启动一个基于APScheduler的调度器,以固定的时间间隔
    周期性地执行监控任务。
    """
    logger.info("任务状态监控服务启动。")
    logger.info(f"任务监控间隔设置为: {TASK_MONITOR_INTERVAL_SECONDS} 秒。")

    db_config = read_config_toml('config/database_config.toml')['web']
    web_client = create_web_client(db_config)
    # 初始化数据库表
    web_client.init_thread_status_table()
    logger.info("数据库连接和表初始化完成。")

    scheduler = BlockingScheduler(timezone="Asia/Shanghai")
    scheduler.add_job(
        run_monitoring_cycle,
        'interval',
        seconds=TASK_MONITOR_INTERVAL_SECONDS,
        args=[web_client],
        next_run_time=datetime.now(),  # 确保启动后立即执行一次
        misfire_grace_time=30,  # 允许任务错过执行时间30秒
        coalesce=True  # 堆积的任务只运行一次
    )

    try:
        logger.info("调度器已启动,按 Ctrl+C 退出。")
        scheduler.start()
    except (KeyboardInterrupt, SystemExit):
        logger.warning("接收到停止信号,正在关闭调度器...")
        scheduler.shutdown()
        logger.info("监控服务已安全关闭。")
        sys.exit(0)
    except Exception as e:
        logger.critical(f"服务运行时发生致命错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

