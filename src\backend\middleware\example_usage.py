"""URL解码中间件使用示例

本文件展示了如何在FastAPI应用中集成URL解码中间件。
当需要启用中间件时,可以参考此示例进行配置。

注意: 此文件仅作为示例,实际使用时需要在app.py中进行配置。
"""

from fastapi import FastAPI
from middleware.url_decoder import URLDecoderMiddleware

# 创建FastAPI应用实例
app = FastAPI(
    title='Industry AI Server with URL Decoder',
    description='工业AI服务器 - 支持URL解码',
    version='2025.07.preview'
)

# 添加URL解码中间件
# 注意: 中间件的添加顺序很重要,URL解码中间件应该在其他业务中间件之前添加
app.add_middleware(URLDecoderMiddleware)

# 示例API端点
@app.post("/test/decode")
async def test_decode(data: dict):
    """
    测试URL解码功能的端点
    
    Args:
        data: 包含可能URL编码的JSON数据
        
    Returns:
        dict: 解码后的数据和处理信息
        
    示例请求:
        POST /test/decode
        Content-Type: application/json
        {
            "project_name": "%E4%B8%AD%E6%96%87%E9%A1%B9%E7%9B%AE",
            "description": "%E6%B5%8B%E8%AF%95%E6%8F%8F%E8%BF%B0"
        }
        
    预期响应:
        {
            "code": 200,
            "message": "数据接收成功",
            "received_data": {
                "project_name": "中文项目",
                "description": "测试描述"
            }
        }
    """
    return {
        "code": 200,
        "message": "数据接收成功",
        "received_data": data
    }


# 在app.py中的集成示例:
"""
# 在现有的app.py文件中添加以下代码:

# 1. 导入中间件
from middleware.url_decoder import URLDecoderMiddleware

# 2. 在创建FastAPI应用后,路由注册前添加中间件
app = FastAPI(
    title='Industry AI Server',
    description='工业AI服务器',
    version='2025.07.preview',
    lifespan=lifespan,
)

# 添加URL解码中间件
app.add_middleware(URLDecoderMiddleware)

# 然后继续注册路由
app.include_router(train_router, prefix='/train', tags=['训练'])
app.include_router(realtime_predict_router, prefix='/realtime_predict', tags=['实时预测'])
app.include_router(decision_making_router, prefix='/decision_making', tags=['决策制定'])
"""


# Java客户端测试代码示例:
"""
// Java客户端发送URL编码的JSON数据
public class URLEncodedJsonTest {
    public static void main(String[] args) throws Exception {
        String url = "http://localhost:8999/test/decode";
        
        // 创建包含中文的JSON数据
        JSONObject json = new JSONObject();
        json.put("project_name", URLEncoder.encode("中文项目", "UTF-8"));
        json.put("description", URLEncoder.encode("测试描述", "UTF-8"));
        
        // 发送HTTP请求
        HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
        conn.setDoOutput(true);
        
        try (OutputStream os = conn.getOutputStream()) {
            byte[] input = json.toString().getBytes("utf-8");
            os.write(input, 0, input.length);
        }
        
        // 读取响应
        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(conn.getInputStream(), "utf-8"))) {
            StringBuilder response = new StringBuilder();
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
            System.out.println(response.toString());
        }
    }
}
"""


if __name__ == "__main__":
    import uvicorn
    
    # 运行测试服务器
    print("启动URL解码中间件测试服务器...")
    print("测试端点: POST http://localhost:8000/test/decode")
    print("发送包含URL编码中文的JSON数据进行测试")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)