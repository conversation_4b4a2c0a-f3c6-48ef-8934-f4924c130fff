import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Train from '../views/Train.vue'
import TrainDetail from '../views/TrainDetail.vue'
import Predict from '../views/Predict.vue'
import Decision from '../views/Decision.vue'
import Monitor from '../views/Monitor.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/train',
    name: 'Train',
    component: Train,
    meta: {
      title: '模型训练'
    }
  },
  {
    path: '/train/detail/:projectName/:configId?',
    name: 'TrainDetail',
    component: TrainDetail,
    meta: {
      title: '训练详情'
    }
  },
  {
    path: '/predict',
    name: 'Predict',
    component: Predict,
    meta: {
      title: '实时预测'
    }
  },
  {
    path: '/decision',
    name: 'Decision',
    component: Decision,
    meta: {
      title: '决策优化'
    }
  },
  {
    path: '/monitor',
    name: 'Monitor',
    component: Monitor,
    meta: {
      title: '系统监控'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = `${to.meta.title} - 工业AI时间序列分析系统`
  }
  next()
})

export default router