# 优化决策模块 API (`/opt`)

## 概述

优化决策模块提供智能决策和工艺优化功能。该模块能够根据配置启动周期性的决策任务。所有与优化决策相关的API都以 `/opt` 为路径前缀。

## 接口列表

### 1. 启动优化决策任务

-   **接口地址**: `POST /opt/decision_making`
-   **功能描述**: 启动一个周期性执行的优化决策任务。
-   **注意**: 如果已存在同名项目 (`project_name`) 的任务,旧任务将被移除,新任务将替换它。

#### 请求体

```json
{
  "project_name": "string",
  "interval": "integer",
  "decision_type": "string"
}
```

| 参数名          | 类型    | 必填 | 描述                                       |
| --------------- | ------- | ---- | ------------------------------------------ |
| `project_name`  | string  | 是   | 项目的唯一名称,也用作后台任务的 `job_id`。 |
| `interval`      | integer | 是   | 决策任务的执行间隔(秒)。                 |
| `decision_type` | string  | 是   | 指定使用的决策代理类型。                   |

#### `decision_type` 可选值

目前只支持一种类型:
- `"basic"`

#### 响应示例

```json
{
  "code": 200,
  "message": "Decision Making <project_name> Request Success!!!"
}
```

---

### 2. 停止指定的优化决策任务

-   **接口地址**: `POST /opt/stop_decision_making`
-   **功能描述**: 停止一个指定的优化决策任务。

#### 请求体

```json
{
  "project_name": "string"
}
```

| 参数名         | 类型   | 必填 | 描述             |
| -------------- | ------ | ---- | ---------------- |
| `project_name` | string | 是   | 要停止的项目名称。 |

#### 响应示例

```json
{
  "code": 200,
  "message": "停止决策<project_name>请求成功!!!"
}
```

---

### 3. 停止所有优化决策任务

-   **接口地址**: `POST /opt/stop_decision_making_all`
-   **功能描述**: 停止调度器中所有 `job_id` 包含 `decision_making` 字符串的任务。
-   **请求参数**: 无
-   **重要说明**: 由于决策任务使用项目名称作为`job_id`,此接口只能停止项目名称中包含`'decision_making'`字符串的任务。
-   **响应示例**:
    ```json
    {
      "code": 200,
      "message": "停止所有决策任务!!!"
    }
    ```

---

### 4. 查询正在运行的决策任务

-   **接口地址**: `GET /opt/get_decision_making`
-   **功能描述**: 查询当前所有正在运行的决策任务。
-   **请求参数**: 无
-   **重要说明**: 当前该接口查找`job_id`中包含`'decision_making'`字符串的任务,但决策任务实际使用项目名称作为`job_id`,因此可能无法查询到决策任务(除非项目名称包含`'decision_making'`字符串)。
-   **响应示例**:
    ```json
    {
        "code": 200,
        "message": "查询成功",
        "data": [
            {
                "id": "optimization_project",
                "project_name": "optimization_project",
                "next_run_time": "2024-07-30T16:00:00.456+08:00",
                "last_run_time": null
            }
        ]
    }
    ```

## Python 调用示例

此示例演示了如何启动决策任务,并正常停止它。

```python
import requests
import time
import json

BASE_URL = "http://localhost:8081"
HEADERS = {"Content-Type": "application/json"}

def start_decision_making(project_name: str, interval: int):
    """启动决策任务"""
    print(f"\n--- 启动项目 '{project_name}' 的决策任务 ---")
    payload = {
        "project_name": project_name,
        "interval": interval,
        "decision_type": "basic" 
    }
    try:
        response = requests.post(f"{BASE_URL}/opt/decision_making", json=payload, headers=HEADERS)
        response.raise_for_status()
        print("响应:", response.json())
        return True
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return False

def stop_decision_making(project_name: str):
    """停止指定的决策任务"""
    print(f"\n--- 停止项目 '{project_name}' 的决策任务 ---")
    payload = {"project_name": project_name}
    try:
        response = requests.post(f"{BASE_URL}/opt/stop_decision_making", json=payload, headers=HEADERS)
        response.raise_for_status()
        print("响应:", response.json())
        return True
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return False

def stop_all_decision_making():
    """停止所有决策任务"""
    print(f"\n--- 停止所有决策任务 ---")
    try:
        response = requests.post(f"{BASE_URL}/opt/stop_decision_making_all", headers=HEADERS)
        response.raise_for_status()
        print("响应:", response.json())
        return True
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return False

def query_decision_jobs():
    """查询所有运行中的决策任务"""
    print("\n--- 查询所有决策任务 ---")
    try:
        response = requests.get(f"{BASE_URL}/opt/get_decision_making", headers=HEADERS)
        response.raise_for_status()
        tasks = response.json()
        print(json.dumps(tasks, indent=2, ensure_ascii=False))
        return tasks.get('data', [])
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return []

if __name__ == "__main__":
    PROJECT_NAME = "rotary_kiln_optimization"
    
    # 1. 启动决策任务
    start_decision_making(PROJECT_NAME, interval=60)
    
    # 2. 等待片刻,让任务出现在调度器中
    print("\n等待5秒...")
    time.sleep(5)
    
    # 3. 查询任务,确认已启动
    query_decision_jobs()
    
    # 4. 停止指定任务
    stop_decision_making(PROJECT_NAME)
    
    # 5. 再次查询,确认任务已被停止
    print("\n等待2秒后再次查询...")
    time.sleep(2)
    query_decision_jobs()

```
