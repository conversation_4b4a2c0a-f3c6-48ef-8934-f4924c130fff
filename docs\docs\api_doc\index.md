# IndustryAI API 接口文档

## 服务信息

- **服务名称**: IndustryAI 工业智能平台
- **版本**: 2025.07.preview
- **基础URL**: `http://localhost:8081`
- **交互式文档**: `http://localhost:8081/docs`

## 认证方式

当前版本服务为开放访问,无特殊认证要求。

### 请求头要求
```http
Content-Type: application/json
```

## 接口模块

### 🏭 训练模块 (Training)
- **路径前缀**: `/train`
- **功能**: 模型训练管理,支持多种训练算法。
- **详细文档**: [训练接口文档](train.md)

### 🔄 实时预测模块 (Real-time Prediction)
- **路径前缀**: `/rt`
- **功能**: 高性能异步预测服务,支持序列预测、软测量等。
- **详细文档**: [实时预测接口文档](realtime_predict.md)

### 🧠 优化决策模块 (Decision Making)
- **路径前缀**: `/opt`
- **功能**: 智能决策和工艺优化。
- **详细文档**: [优化决策接口文档](decision_making.md)

### 💓 系统端点 (System Endpoints)
- **路径前缀**: `/`
- **功能**: 查询系统级的任务状态和信息。
- **详细文档**: [系统端点接口文档](system_endpoints.md)

## 通用响应格式

### 成功响应
```json
{
    "code": 200,
    "message": "操作成功的描述信息",
    "data": {
        "result": "具体返回数据"
    }
}
```

### 错误响应
```json
{
    "detail": [
        {
            "loc": ["body", "project_name"],
            "msg": "field required",
            "type": "value_error.missing"
        }
    ]
}
```

## 常见HTTP状态码

| 状态码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 请求成功 | - |
| 404 | 接口不存在 | 检查请求路径是否正确。 |
| 422 | 请求体验证失败 | 检查请求体格式、必填参数和数据类型是否符合接口要求。 |
| 500 | 服务器内部错误 | 服务器处理请求时发生未知错误,请联系技术支持。 |

## 数据类型说明

所有接口的请求体和响应体都基于 `pydantic` 模型,具有严格的数据类型校验。详细的数据类型定义请参考各个模块的接口文档。

### `AlgorithmType_Train` (训练算法类型)
```python
# 定义于 src/backend/api/requestbody.py
AlgorithmType_Train = Literal[
    "sequence", "sequence_online", "classic", "classic_online",
    "gan", "rl", "time_series_sequence", "time_series_classic",
    "multi_input_time_series_classic", "joint_loss", "joint_gan",
    "quality_sequence", "joint_trainer_sequence",
    "joint_quality_monitor", "joint_trainer_final",
    "multi_task_seq_pretrainer", "ml"
]
```

### `PredictorType_RealTime` (预测器类型)
```python
# 定义于 src/backend/api/requestbody.py
PredictorType_RealTime = Literal[
    "basic", "classic", "classic_mo", "classic_standard",
    "sequence", "quality", "sequence_soft", "sequence_mo_soft",
    "time_series"
]
```

### `DecisionType_DecisionMaking` (决策类型)
```python
# 定义于 src/backend/api/requestbody.py
DecisionType_DecisionMaking = Literal["basic"]
```

## 请求限制

- **超时时间**: 建议客户端设置合理的请求超时时间,例如30-60秒。
- **并发限制**: 由部署环境和服务器配置决定。

## 开发工具

- **Swagger UI**: `http://localhost:8081/docs` (自动生成的交互式API文档)
- **ReDoc**: `http://localhost:8081/redoc` (另一种风格的API文档)
- **OpenAPI Schema**: `http://localhost:8081/openapi.json` (API定义的JSON文件)

## 最佳实践

1.  **错误处理**: 始终检查HTTP状态码,对于 `422 Unprocessable Entity` 错误,应解析响应体中的 `detail` 字段以获取详细的验证错误信息。
2.  **异步任务**: 训练、预测和决策接口会启动后台任务。调用接口成功仅表示任务已成功提交,不代表任务已执行完成。请使用对应的查询接口来跟踪任务状态。
3.  **日志记录**: 客户端应记录关键的请求和响应,以便于问题追踪和调试。

## 快速开始

### 基础接口测试

```bash
# 测试服务是否正常运行 (根路径)
curl -X GET "http://localhost:8081/"
# 预期响应: {"message":"Welcome to Industry AI Server of DCKJ!!!"}
```

### Python调用示例

```python
import requests
import json

# 基础配置
base_url = "http://localhost:8081"
headers = {"Content-Type": "application/json"}

# 启动模型训练
train_data = {
    "project_name": "demo_project_01",
    "interval": 30, # 该参数在当前实现中未被使用
    "algorithm_type": "classic"
}

try:
    response = requests.post(
        f"{base_url}/train/train",
        headers=headers,
        json=train_data  # 使用 json 参数自动处理序列化
    )
    
    response.raise_for_status() # 如果状态码不是 2xx,则抛出异常
    
    print("请求成功:")
    print(response.json())

except requests.exceptions.HTTPError as http_err:
    print(f"HTTP 错误: {http_err}")
    print("响应内容:", response.text)
except requests.exceptions.RequestException as req_err:
    print(f"请求异常: {req_err}")

```