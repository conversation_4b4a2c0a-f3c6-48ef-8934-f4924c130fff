import threading
import time

from datetime import datetime
from typing_extensions import deprecated

from industrytslib import (
    get_logger, 
    database_builder
)


@deprecated("OperationStatus is deprecated, use OperationStatusV2 instead")
class OperationStatus:
    def __init__(self, dbconfig):
        self.dbconfig = dbconfig["web"]
        self.db_mssql_web = database_builder(self.dbconfig)
        self.logger = get_logger(__name__, 'operation_status')

        self.db_mssql_web.ensure_table_exist_operation_status()

    # 判断当前有哪些线程在运行
    def judge_operation_status(self) -> None:
        self.db_mssql_web.init_operation_status()
        update_time = datetime.now()
        self.logger.info("当前运行中的线程有:")
        for thread in threading.enumerate():
            if hasattr(thread, 'id') and hasattr(thread, 'task_type'):
                id = thread.id
                task_type = thread.task_type
                project_name = thread.project_name
                self.logger.info(f"项目名称:{project_name}, 项目id:{id}, 项目类型:{task_type}")
                self.db_mssql_web.update_operation_status(
                    update_time = update_time, 
                    project_name = project_name, 
                    project_type = task_type
                )

    def run(self) -> None:
        while True:
            try:
                self.judge_operation_status()
            except Exception as e:
                self.logger.error(f"判断运行状态时发生错误: {e}")
            time.sleep(60)
