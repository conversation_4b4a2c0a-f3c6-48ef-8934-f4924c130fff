about-time==4.2.1
absl-py==2.1.0
accelerate==1.5.1
aiocsv==1.3.2
aiofiles==24.1.0
aiohappyeyeballs==2.4.4
aiohttp==3.11.16
aioodbc==0.5.0
aioredis==2.0.1
aiosignal==1.3.2
aiosqlite==0.21.0
alabaster==1.0.0
alive-progress==3.2.0
amqp==5.3.1
AMQPStorm==2.10.6
annotated-types==0.7.0
anyio==4.8.0
APScheduler==3.11.0
async-timeout==5.0.1
asyncio==3.4.3
asyncpg==0.30.0
attrs==24.3.0
auto_run_on_remote==0.4
autograd==1.7.0
Automat==24.8.1
axial_positional_embedding==0.3.5
babel==2.16.0
bcrypt==4.2.1
beartype==0.19.0
beautifulsoup4==4.12.3
billiard==4.2.1
bitsandbytes==0.45.0
catboost==1.2.8
causal-conv1d==1.5.0.post8
celery==5.4.0
certifi==2024.12.14
cffi==1.17.1
chained_mode_time_tool==0.4
chardet==5.2.0
charset-normalizer==3.4.1
ciso8601==2.3.2
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cma==3.2.2
colorama==0.4.6
CoLT5-attention==0.10.19
concurrent-log-handler==0.9.23
constantly==23.10.4
contourpy==1.3.1
cryptography==44.0.1
cycler==0.12.1
datasets==3.2.0
decorator==5.1.1
Deprecated==1.2.14
dill==0.3.8
dnspython==2.7.0
docker==7.1.0
dockerpyze==2.1.1
docutils==0.21.2
dynaconf==3.2.10
einops==0.8.0
einops-exts==0.0.4
einx==0.3.0
EMD-signal==1.6.4
fabric2==2.6.0
fairscale==0.4.13
fast_pytorch_kmeans==0.2.2
fastapi==0.115.12
filelock==3.16.1
fire==0.7.0
flash_attn==2.7.4.post1
flower==2.0.1
fonttools==4.55.3
frozendict==2.4.6
frozenlist==1.5.0
fsspec==2024.9.0
funboost==48.0
furo==2024.8.6
ghp-import==2.1.0
grapheme==0.6.0
graphviz==0.20.3
greenlet==3.1.1
griffe==1.5.4
grpcio==1.69.0
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
huggingface-hub==0.27.1
humanize==4.12.0
hyper-connections==0.1.8
hyperlink==21.0.0
idna==3.10
imagesize==1.4.1
incremental==24.7.2
influxdb-client==1.48.0
iniconfig==2.0.0
invoke==1.7.3
Jinja2==3.1.5
joblib==1.4.2
kiwisolver==1.4.8
kombu==5.4.2
lightning-utilities==0.11.9
lion-pytorch==0.2.3
llvmlite==0.44.0
local-attention==1.11.0
logging_tree==1.10
loguru==0.7.3
mamba-r1==0.0.2
mamba-ssm==2.2.4
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.10.1
mdurl==0.1.2
mergedeep==1.3.4
mkdocs==1.6.1
mkdocs-autorefs==1.3.0
mkdocs-get-deps==0.2.0
mkdocs-material==9.5.49
mkdocs-material-extensions==1.3.1
mkdocstrings==0.27.0
mkdocstrings-python==1.13.0
mpmath==1.3.0
multidict==6.1.0
multiprocess==0.70.16
narwhals==1.26.0
nb-libs==1.8
nb-log==13.2
nb-time==2.2
nb_filelock==0.8
networkx==3.4.2
ninja==********
Nuitka==2.7
numba==0.61.2
numpy==2.2.1
ordered-set==4.1.0
orjson==3.10.15
packaging==24.2
paginate==0.5.7
paho-mqtt==2.1.0
pamqp==2.3.0
pandas==2.2.3
paramiko==3.5.1
pathlib2==2.3.7.post1
pathos==0.3.2
pathspec==0.12.1
persist-queue==1.0.0
pikav0==0.1.23
pikav1==1.0.13
pillow==11.1.0
platformdirs==4.3.6
plotly==6.0.0
pluggy==1.5.0
poetry-core==2.1.2
polars==1.29.0
portalocker==3.1.1
pox==0.3.5
ppft==*******
prettytable==3.12.0
product_key_memory==0.2.11
progressbar==2.5
prometheus_client==0.21.1
prompt_toolkit==3.0.50
propcache==0.2.1
protobuf==5.29.3
psutil==6.1.1
psycopg==3.2.6
psycopg-binary==3.2.6
psycopg-pool==3.2.4
pyarmor==9.1.1
pyarmor-webui==2.6
pyarmor.cli.core==7.6.4
pyarrow==19.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.11.4
pydantic-settings==2.7.1
pydantic_core==2.33.2
pyecharts==2.0.8
Pygments==2.19.1
pykan==0.2.8
pymdown-extensions==10.14
pymongo==4.11.2
pymoo==*******
PyNaCl==1.5.0
pyodbc==5.2.0
pyparsing==3.2.1
PySnooper==1.2.1
pytest==8.3.4
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-json-logger==0.1.10
pytorch-wavelets==1.3.0
pytz==2024.2
PyWavelets==1.8.0
PyYAML==6.0.2
pyyaml_env_tag==0.1
rabbitpy==2.0.1
reactivex==4.0.4
redis==5.2.1
redis2==********
redis3==*******
redis5==*******
reformer-pytorch==1.4.4
regex==2024.11.6
requests==2.32.3
rich==13.9.4
rtoml==0.12.0
safetensors==0.5.2
scikit-learn==1.6.1
scipy==1.15.2
seaborn==0.13.2
semantic-version==2.10.0
sentencepiece==0.2.0
service-identity==24.2.0
setuptools-rust==1.10.2
shellingham==1.5.4
simplejson==3.19.3
six==1.17.0
sniffio==1.3.1
snowballstemmer==2.2.0
soupsieve==2.6
Sphinx==8.1.3
sphinx-basic-ng==1.0.0b2
sphinxcontrib-applehelp==2.0.0
sphinxcontrib-devhelp==2.0.0
sphinxcontrib-htmlhelp==2.1.0
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==2.0.0
sphinxcontrib-serializinghtml==2.0.0
SQLAlchemy==2.0.38
starlette==0.41.3
sympy==1.13.1
tensorboard==2.19.0
tensorboard-data-server==0.7.2
termcolor==2.5.0
threadpoolctl==3.5.0
tiktoken==0.8.0
timm==1.0.13
tokenizers==0.21.0
tokenmonster==1.1.12
toml==0.10.2
tomorrow3==1.1.0
torchdiffeq==0.2.5
torchinfo==1.8.0
torch==2.5.1
torchmetrics==1.6.1
tornado==6.4.2
tqdm==4.67.1
transformers==4.48.3
tslearn==0.6.3
Twisted==24.11.0
typer==0.15.2
typing
typing-inspection==0.4.0
typing_extensions==4.12.2
tzdata==2024.2
tzlocal==5.2
urllib3==2.3.0
uvicorn==0.34.0
vector-quantize-pytorch==1.12.0
vine==5.1.0
watchdog==6.0.0
wcwidth==0.2.13
Werkzeug==3.1.3
wrapt==1.17.2
xxhash==3.5.0
yacs==0.1.8
yarl==1.18.3
zetascale==0.9.1
zope.interface==7.2
zstandard==0.23.0
