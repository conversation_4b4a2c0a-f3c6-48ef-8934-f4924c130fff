# Windows 环境部署指南

本文档详细说明如何在Windows环境下部署IndustryAI工业智能平台。

## 系统要求

### 硬件要求
- **CPU**: Intel i5 8代或AMD Ryzen 5 3600及以上
- **内存**: 最低8GB,推荐16GB或更多
- **存储**: 至少50GB可用空间,推荐SSD
- **GPU**: 可选,支持CUDA 11.8+的NVIDIA显卡(用于深度学习加速)

### 软件要求
- **操作系统**: Windows 10/11 (64位)
- **Python**: 3.11或更高版本
- **Git**: 用于代码管理
- **PowerShell**: 5.1或更高版本

## 环境准备

### 1. 安装Python

从[Python官网](https://www.python.org/downloads/windows/)下载Python 3.11+:

```powershell
# 验证Python安装
python --version
pip --version

# 升级pip
python -m pip install --upgrade pip
```

### 2. 安装Git

从[Git官网](https://git-scm.com/download/win)下载并安装Git for Windows。

### 3. 安装Just命令工具

#### 方法一:使用Cargo安装
```powershell
# 安装Rust和Cargo
winget install Rustlang.Rustup

# 重启PowerShell后安装Just
cargo install just
```

#### 方法二:使用预编译二进制
```powershell
# 下载Just二进制文件
Invoke-WebRequest -Uri "https://github.com/casey/just/releases/latest/download/just-x86_64-pc-windows-msvc.zip" -OutFile "just.zip"

# 解压到系统PATH目录
Expand-Archive -Path "just.zip" -DestinationPath "C:\Windows\System32"
```

## 项目部署

### 1. 获取项目代码

```powershell
# 克隆项目(如果有Git仓库)
git clone <repository-url> industryai
cd industryai

# 或者解压项目包
Expand-Archive -Path "industryai.zip" -DestinationPath "."
cd industryai
```

### 2. 创建虚拟环境

```powershell
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
.\venv\Scripts\Activate.ps1

# 如果执行策略限制,先运行:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 3. 安装依赖

```powershell
# 安装Python依赖
pip install -r requirements.txt

# 如果有GPU支持需求,安装CUDA版本的PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 4. 配置环境变量

创建`.env`文件:
```powershell
# 复制环境变量模板
copy .env.example .env

# 编辑配置文件
notepad .env
```

配置示例:
```env
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/industryai
REDIS_URL=redis://localhost:6379/0

# 服务配置
HOST=0.0.0.0
PORT=8081
WORKERS=4

# 工厂配置
FACTORY_NAME=北京工厂
FACTORY_CODE=BJ001

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/industryai.log
```

### 5. 数据库配置

#### PostgreSQL安装
```powershell
# 使用winget安装PostgreSQL
winget install PostgreSQL.PostgreSQL

# 或下载安装包
# https://www.postgresql.org/download/windows/
```

#### Redis安装
```powershell
# 下载Redis for Windows
Invoke-WebRequest -Uri "https://github.com/microsoftarchive/redis/releases/download/win-3.2.100/Redis-x64-3.2.100.msi" -OutFile "redis.msi"

# 安装Redis
Start-Process msiexec.exe -Wait -ArgumentList '/I redis.msi /quiet'
```

#### 数据库初始化
```powershell
# 创建数据库
psql -U postgres -c "CREATE DATABASE industryai;"

# 运行数据库迁移(如果有)
python manage.py migrate
```

## 服务启动

### 1. 使用Just命令启动

```powershell
# 查看可用命令
just --list

# 启动服务(默认工厂)
just server

# 指定工厂启动
just server "北京工厂"

# 开发模式启动
just dev
```

### 2. 手动启动

```powershell
# 激活虚拟环境
.\venv\Scripts\Activate.ps1

# 启动FastAPI服务
uvicorn main:app --host 0.0.0.0 --port 8081 --workers 4

# 或使用Python直接运行
python main.py
```

### 3. 后台服务启动

#### 使用NSSM (推荐)
```powershell
# 下载NSSM
Invoke-WebRequest -Uri "https://nssm.cc/release/nssm-2.24.zip" -OutFile "nssm.zip"
Expand-Archive -Path "nssm.zip" -DestinationPath "C:\nssm"

# 安装服务
C:\nssm\win64\nssm.exe install IndustryAI

# 配置服务
C:\nssm\win64\nssm.exe set IndustryAI Application "C:\path\to\industryai\venv\Scripts\python.exe"
C:\nssm\win64\nssm.exe set IndustryAI AppParameters "main.py"
C:\nssm\win64\nssm.exe set IndustryAI AppDirectory "C:\path\to\industryai"

# 启动服务
C:\nssm\win64\nssm.exe start IndustryAI
```

## 验证部署

### 1. 检查服务状态

```powershell
# 检查端口占用
netstat -an | findstr :8081

# 测试API接口
Invoke-RestMethod -Uri "http://localhost:8081/" -Method GET

# 检查训练模块
Invoke-RestMethod -Uri "http://localhost:8081/train/" -Method GET
```

### 2. 访问Web界面

- **API文档**: http://localhost:8081/docs
- **ReDoc文档**: http://localhost:8081/redoc
- **系统状态**: http://localhost:8081/

### 3. 日志检查

```powershell
# 查看日志文件
Get-Content -Path "logs\industryai.log" -Tail 50

# 实时监控日志
Get-Content -Path "logs\industryai.log" -Wait
```

## 故障排除

### 常见问题

#### 1. Python模块导入错误
```powershell
# 检查虚拟环境是否激活
where python

# 重新安装依赖
pip install -r requirements.txt --force-reinstall
```

#### 2. 端口占用问题
```powershell
# 查找占用端口的进程
netstat -ano | findstr :8081

# 终止进程
taskkill /PID <PID> /F
```

#### 3. 数据库连接问题
```powershell
# 检查PostgreSQL服务
Get-Service postgresql*

# 启动PostgreSQL服务
Start-Service postgresql-x64-14
```

#### 4. 权限问题
```powershell
# 以管理员身份运行PowerShell
Start-Process powershell -Verb runAs

# 设置执行策略
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope LocalMachine
```

## 技术支持

如遇到部署问题,请:

1. 检查系统日志和应用日志
2. 确认所有依赖服务正常运行
3. 验证网络连接和防火墙设置
4. 联系技术支持团队