"""
实时预测相关路由
本模块提供实时预测相关的API接口,包括:
- 实时预测任务
- 序列实时预测任务
- 序列软测量任务
- 停止各类预测任务
"""
from datetime import datetime
from fastapi import APIRouter

from api.requestbody import (
    RealtimePredictRequestBody, 
    StopRealtimePredictRequestBody
)
from utils.task_scheduler import predict_scheduler

from industrytslib import (
    get_logger,
    read_config_toml,
)
from industrytslib import get_realtime_predictor


router = APIRouter()
rt_logger = get_logger(
    logger_name='realtime_predictor',
    logger_type='routers',
    level='DEBUG',
    console_level='WARNING'
)
dbconfig = read_config_toml('config/database_config.toml')


@router.get("/")
async def rt_index():
    """
    实时预测模块首页
    返回实时预测模块的基本信息
    """
    return {"message": "realtime predict information page"}

@router.post('/realtime_predict')
async def realtime_predict(request: RealtimePredictRequestBody):
    """
    启动实时预测任务
    
    参数:
        request: 包含项目名称、预测器类型和预测间隔等信息的请求体
        
    流程:
        1. 记录实时预测请求信息
        2. 如果已存在同名预测任务,则先移除
        3. 获取对应项目和预测器类型的预测器
        4. 将预测任务添加到调度器中并立即执行
        5. 返回实时预测请求成功的消息
    """
    rt_logger.info(f'实时预测请求: {request}')

    # 创建任务
    job_id = request.project_name
    if predict_scheduler.get_job(job_id):
        predict_scheduler.remove_job(job_id)

    predictor = get_realtime_predictor(
        project_name=request.project_name,
        dbconfig=dbconfig,
        predictor_type=request.predictor_type
    )

    # 添加任务到调度器
    predict_scheduler.add_job(
        predictor.main,
        trigger='interval',
        seconds=request.interval,
        id=job_id,
        name=request.project_name,
        replace_existing=True,
        next_run_time=datetime.now(),  # 设置立即执行
        misfire_grace_time=30  # 允许错过的任务立即执行
    )

    rt_logger.info(f'实时预测{request.project_name}请求成功!!!')

    return {'code': 200, 'message': f'实时预测{request.project_name}请求成功!!!'}

@router.post('/stop_realtime_predict')
async def stop_realtime_predict(request: StopRealtimePredictRequestBody):
    """
    停止指定的实时预测任务
    
    参数:
        request: 包含要停止预测的项目名称的请求体
        
    流程:
        1. 记录停止实时预测请求信息
        2. 根据项目名称构建任务ID
        3. 如果存在对应的预测任务,则从调度器中移除
        4. 返回停止预测成功的消息
    """
    rt_logger.info(f'停止实时预测请求: {request}')
    # 创建任务
    job_id = request.project_name
    if predict_scheduler.get_job(job_id):
        predict_scheduler.remove_job(job_id)

    rt_logger.info(f'停止实时预测{request.project_name}请求成功!!!')

    return {'code': 200, 'message': '停止实时预测请求成功!!!'}

@router.post('/stop_realtime_predict_all')
async def stop_realtime_predict_all():
    """
    停止所有正在进行的预测任务(包括实时预测、序列预测和序列软测量)
    
    流程:
        1. 记录停止所有预测任务的请求
        2. 获取调度器中的所有任务
        3. 遍历任务列表,移除ID中包含特定前缀的任务
        4. 返回停止所有预测任务成功的消息
    """
    rt_logger.info('停止所有实时预测任务!!!')
    # 停止所有实时预测任务
    jobs = predict_scheduler.get_jobs()
    for job in jobs:
        if any(prefix in job.id for prefix in ['realtime_predict_', 'sequence_predict_', 'sequence_soft_']):
            predict_scheduler.remove_job(job.id)
            rt_logger.info(f'停止预测任务{job.id}成功!!!')

    rt_logger.info('停止所有实时预测任务!!!')

    return {'code': 200, 'message': '停止所有实时预测任务!!!'}

@router.post('/sequence_realtime_predict')
async def sequence_realtime_predict(request: RealtimePredictRequestBody):
    """
    启动序列实时预测任务
    
    参数:
        request: 包含项目名称和预测间隔等信息的请求体
        
    流程:
        1. 记录序列实时预测请求信息
        2. 如果已存在同名序列预测任务,则先移除
        3. 获取序列预测器
        4. 将序列预测任务添加到调度器中并立即执行
        5. 返回序列预测请求成功的消息
    """
    rt_logger.info(f'序列实时预测请求: {request}')
    
    # 创建任务
    job_id = f"sequence_predict_{request.project_name}"
    if predict_scheduler.get_job(job_id):
        predict_scheduler.remove_job(job_id)

    predictor = get_realtime_predictor(request.project_name, dbconfig, "sequence")

    # 添加任务到调度器
    predict_scheduler.add_job(
        predictor.main,
        trigger='interval',
        seconds=request.interval,
        id=job_id,
        name=request.project_name,
        replace_existing=True,
        next_run_time=datetime.now(),  # 设置立即执行
        misfire_grace_time=None  # 允许错过的任务立即执行
    )

    rt_logger.info(f'序列实时预测{request.project_name}请求成功!!!')

    return {'code': 200, 'message': f'序列实时预测{request.project_name}请求成功!!!'}

@router.post('/sequence_soft_sensor')
async def sequence_soft_sensor(request: RealtimePredictRequestBody):
    """
    启动序列软测量任务
    
    参数:
        request: 包含项目名称和测量间隔等信息的请求体
        
    流程:
        1. 记录序列软测量请求信息
        2. 如果已存在同名软测量任务,则先移除
        3. 获取序列软测量器
        4. 将软测量任务添加到调度器中并立即执行
        5. 返回软测量请求成功的消息
    """
    rt_logger.info(f'序列软测量请求: {request}')
    
    # 创建任务
    job_id = f"sequence_soft_{request.project_name}"
    if predict_scheduler.get_job(job_id):
        predict_scheduler.remove_job(job_id)

    predictor = get_realtime_predictor(
        project_name=request.project_name,
        dbconfig=dbconfig,
        predictor_type="sequence_soft"
    )

    # 添加任务到调度器
    predict_scheduler.add_job(
        predictor.main,
        trigger='interval',
        seconds=request.interval,
        id=job_id,
        name=request.project_name,
        replace_existing=True,
        next_run_time=datetime.now(),  # 设置立即执行
        misfire_grace_time=None  # 允许错过的任务立即执行
    )

    rt_logger.info(f'序列软测量{request.project_name}请求成功!!!')

    return {'code': 200, 'message': f'序列软测量{request.project_name}请求成功!!!'}

@router.post('/stop_sequence_predict')
async def stop_sequence_predict(request: StopRealtimePredictRequestBody):
    """
    停止指定的序列预测任务
    
    参数:
        request: 包含要停止序列预测的项目名称的请求体
        
    流程:
        1. 记录停止序列预测请求信息
        2. 根据项目名称构建任务ID
        3. 如果存在对应的序列预测任务,则从调度器中移除
        4. 返回停止序列预测成功的消息
    """
    rt_logger.info(f'停止序列预测请求: {request}')
    
    job_id = f"sequence_predict_{request.project_name}"
    if predict_scheduler.get_job(job_id):
        predict_scheduler.remove_job(job_id)

    rt_logger.info(f'停止序列预测{request.project_name}请求成功!!!')

    return {'code': 200, 'message': '停止序列预测请求成功!!!'}

@router.post('/stop_sequence_soft')
async def stop_sequence_soft(request: StopRealtimePredictRequestBody):
    """
    停止指定的序列软测量任务
    
    参数:
        request: 包含要停止软测量的项目名称的请求体
        
    流程:
        1. 记录停止软测量请求信息
        2. 根据项目名称构建任务ID
        3. 如果存在对应的软测量任务,则从调度器中移除
        4. 返回停止软测量成功的消息
    """
    rt_logger.info(f'停止序列软测量请求: {request}')
    
    job_id = f"sequence_soft_{request.project_name}"
    if predict_scheduler.get_job(job_id):
        predict_scheduler.remove_job(job_id)

    rt_logger.info(f'停止序列软测量{request.project_name}请求成功!!!')

    return {'code': 200, 'message': '停止序列软测量请求成功!!!'}


# 查询当前正在运行的实时预测任务
@router.get('/get_realtime_predict_jobs')
async def get_realtime_predict_jobs():
    """
    查询当前有哪些正在运行的实时预测任务
    
    流程:
        1. 获取调度器中的所有任务
        2. 筛选出ID中包含预测任务前缀的任务
        3. 返回这些预测任务的信息
    """
    rt_logger.info('查询当前正在运行的实时预测任务')
    
    # 获取所有预测任务
    jobs = predict_scheduler.get_jobs()
    predict_tasks = []
    
    for job in jobs:
        if any(prefix in job.id for prefix in ['realtime_predict_', 'sequence_predict_', 'sequence_soft_']):
            # 确定任务类型
            task_type = ''
            if 'realtime_predict_' in job.id:
                task_type = '实时预测'
            elif 'sequence_predict_' in job.id:
                task_type = '序列预测'
            elif 'sequence_soft_' in job.id:
                task_type = '序列软测量'
            
            predict_tasks.append({
                'id': job.id,
                'project_name': job.name,
                'task_type': task_type,
                'next_run_time': job.next_run_time,
                'last_run_time': getattr(job, 'last_run_time', None)
            })
    
    rt_logger.info(f'当前有 {len(predict_tasks)} 个预测任务正在运行')
    
    return {'code': 200, 'message': '查询成功', 'data': predict_tasks}
