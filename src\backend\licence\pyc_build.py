import compileall
import py_compile
import shutil
import sys
from pathlib import Path

def build_app():
    # 创建发布目录
    dist_dir = Path("pyc_dist/app")
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    dist_dir.mkdir(parents=True)
    
    # 要排除的文件和目录
    exclude_patterns = {
        '__pycache__', 
        '*.pyc', 
        '*.pyo', 
        '*.pyd',
        '.git',
        '.idea',
        'build',
        'dist',
        'tests',
        '*.egg-info'
    }
    
    def should_copy(path):
        return not any(p in str(path) for p in exclude_patterns)
    
    # 编译所有 Python 文件
    for py_file in Path('.').rglob('*.py'):
        if should_copy(py_file):
            # 创建目标目录结构
            rel_path = py_file.relative_to('.')
            dest_dir = dist_dir / rel_path.parent
            dest_dir.mkdir(parents=True, exist_ok=True)
            
            # 编译为优化的字节码
            pyc_path = dest_dir / (py_file.stem + '.pyc')
            py_compile.compile(
                py_file,
                pyc_path,
                optimize=2  # 使用最高级别的优化
            )
    
    # 复制其他必要的资源文件
    for pattern in ['*.json', '*.yaml', '*.txt', '*.toml']:  # 添加 .toml
        for file in Path('.').rglob(pattern):
            if should_copy(file):
                dest = dist_dir / file.relative_to('.')
                dest.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(file, dest)
    
    # 特别处理 config 文件夹中的 toml 文件
    config_dir = Path('config')
    if config_dir.exists():
        for toml_file in config_dir.rglob('*.toml'):
            dest = dist_dir / toml_file.relative_to('.')
            dest.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(toml_file, dest)
            print(f"已复制配置文件: {toml_file.relative_to('.')}")
    
    # 创建启动脚本
    create_launcher(dist_dir)
    
    # 创建 zip 文件
    shutil.make_archive(
        'dist/app',  # 输出文件名(不包含扩展名)
        'zip',       # 压缩格式
        dist_dir     # 要压缩的目录
    )
    
    print(f"应用程序已打包到: {dist_dir}")
    print(f"压缩包已创建: dist/app.zip")

def create_launcher(dist_dir):
    """创建启动脚本"""
    if sys.platform == "win32":
        # Windows 批处理文件
        launcher = """@echo off
set PYTHONDONTWRITEBYTECODE=1
python main.pyc
pause
"""
        with open(dist_dir / "start.bat", "w") as f:
            f.write(launcher)
    else:
        # Unix shell 脚本
        launcher = """#!/bin/bash
export PYTHONDONTWRITEBYTECODE=1
python main.pyc
"""
        launcher_path = dist_dir / "start.sh"
        with open(launcher_path, "w") as f:
            f.write(launcher)
        # 设置可执行权限
        launcher_path.chmod(0o755)

if __name__ == "__main__":
    build_app()
