"""
优化决策相关路由
本模块提供优化决策相关的API接口,包括:
- 启动优化决策任务
- 停止指定优化决策任务
- 停止所有优化决策任务
"""
import datetime

from fastapi import APIRouter

from api.requestbody import (
    DecisionMakingRequestBody, 
    StopDecisionMakingRequestBody
)

from utils.task_scheduler import decision_scheduler

from industrytslib import (
    get_logger, 
    read_config_toml, 
    get_decision_agent
)


router = APIRouter()
opt_logger = get_logger(
    logger_name='decision_making',
    logger_type='routers',
    level='DEBUG',
    console_level='WARNING'
)
dbconfig = read_config_toml('config/database_config.toml')


@router.post('/decision_making')
async def decision_making(request: DecisionMakingRequestBody):
    """
    启动优化决策任务
    
    参数:
        request: 包含项目名称和决策间隔等信息的请求体
        
    流程:
        1. 记录优化决策请求信息
        2. 如果已存在同名决策任务,则先移除
        3. 获取对应项目的决策代理
        4. 将决策任务添加到调度器中并立即执行
        5. 返回优化决策请求成功的消息
    """
    opt_logger.info(f'优化决策请求: {request}')
    # 创建任务
    job_id = request.project_name
    if decision_scheduler.get_job(job_id):
        decision_scheduler.remove_job(job_id)

    decision_maker = get_decision_agent(
        project_name=request.project_name, 
        dbconfig=dbconfig,
        decision_type=request.decision_type
    )
    # 添加任务到调度器
    decision_scheduler.add_job(
        decision_maker.main,
        trigger='interval',
        seconds=request.interval,
        id=job_id,
        name=request.project_name,
        replace_existing=True,
        next_run_time=datetime.datetime.now(),  # 设置立即执行
        misfire_grace_time=180  # 错过执行时间超过3分钟(180秒)的任务不再执行
    )  
    opt_logger.info(f'Decision Making {request.project_name} Request Success!!!')
    return {'code': 200, 'message': f'Decision Making {request.project_name} Request Success!!!'}

@router.post('/stop_decision_making')
async def stop_decision_making(request: StopDecisionMakingRequestBody):
    """
    停止指定的优化决策任务
    
    参数:
        request: 包含要停止决策的项目名称的请求体
        
    流程:
        1. 记录停止决策请求信息
        2. 根据项目名称构建任务ID
        3. 如果存在对应的决策任务,则从调度器中移除
        4. 返回停止决策成功的消息
    """
    opt_logger.info(f'停止决策请求: {request}')
    # 创建任务
    job_id = request.project_name
    if decision_scheduler.get_job(job_id):
        decision_scheduler.remove_job(job_id)

    opt_logger.info(f'停止决策{request.project_name}请求成功!!!')

    return {'code': 200, 'message': f'停止决策{request.project_name}请求成功!!!'}

@router.post('/stop_decision_making_all')
async def stop_decision_making_all():
    """
    停止所有正在进行的优化决策任务
    
    流程:
        1. 记录停止所有决策任务的请求
        2. 获取调度器中的所有任务
        3. 遍历任务列表,移除ID中包含'decision_making'的任务
        4. 返回停止所有决策任务成功的消息
    """
    opt_logger.info('停止所有决策任务!!!')
    # 停止所有决策任务
    # 查询job_id中有decision_making的任务
    jobs = decision_scheduler.get_jobs()
    for job in jobs:
        if 'decision_making' in job.id:
            decision_scheduler.remove_job(job.id)
            opt_logger.info(f'停止决策任务{job.id}成功!!!')

    opt_logger.info('停止所有决策任务!!!')

    return {'code': 200, 'message': '停止所有决策任务!!!'}


# 查询当前有哪些运行的决策任务
@router.get('/get_decision_making')
async def get_decision_making():
    """
    查询当前有哪些正在运行的决策任务
    
    流程:
        1. 获取调度器中的所有任务
        2. 筛选出ID中包含'decision_making'的任务
        3. 返回这些决策任务的信息
    """
    opt_logger.info('查询当前正在运行的决策任务')
    
    # 获取所有决策任务
    jobs = decision_scheduler.get_jobs()
    decision_tasks = []
    
    for job in jobs:
        if 'decision_making' in job.id:
            decision_tasks.append({
                'id': job.id,
                'project_name': job.name,
                'next_run_time': job.next_run_time,
                'last_run_time': getattr(job, 'last_run_time', None)
            })
    
    opt_logger.info(f'当前有 {len(decision_tasks)} 个决策任务正在运行')
    
    return {'code': 200, 'message': '查询成功', 'data': decision_tasks}
