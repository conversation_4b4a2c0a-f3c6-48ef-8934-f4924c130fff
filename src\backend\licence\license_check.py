def check_trial_status():
    """检查试用版状态"""
    try:
        # pyarmor会在运行时注入 __pyarmor__
        if hasattr(check_trial_status, '__pyarmor__'):
            # 获取绑定的代码
            bind_code = check_trial_status.__pyarmor__.get_bind_data()
            return bind_code == 'TRIAL'
        return False
    except:
        return False

def is_trial_version():
    """判断是否为试用版"""
    return check_trial_status() 