#!/bin/bash

# 运行前请确认:
# 1. 你已经在项目根目录 (industryai/)
# 2. 你已经激活了正确的conda环境
#
# 该脚本将启动独立的任务状态监控服务

# 加载 .env 文件(如果存在)
if [ -f .env ]; then
  echo "正在加载 .env 文件..."
  # 逐行读取 .env 文件,清理并导出变量,以兼容 "KEY = VALUE" 格式
  while IFS= read -r line; do
    # 跳过注释和空行
    [[ "$line" =~ ^\s*# ]] || [[ -z "$line" ]] && continue
    # 导出清理后的行
    export "$(echo "$line" | sed -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//' -e 's/\s*=\s*/=/')"
  done < .env
fi

# 将当前目录(即项目根目录)添加到PYTHONPATH
export PYTHONPATH=${PYTHONPATH:+$PYTHONPATH:}$(pwd)
echo "PYTHONPATH set to: $PYTHONPATH"
echo "Using Python from: $(which python)"

# 运行任务监控服务
python src/operation_status/task_monitor.py 