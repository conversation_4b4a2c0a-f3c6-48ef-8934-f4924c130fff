<template>
  <div class="monitor">
    <div class="page-title">系统监控</div>
    
    <!-- 系统状态概览 -->
    <div class="card">
      <h3>系统状态概览</h3>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon" style="background: #67C23A;">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ systemStatus.status }}</div>
              <div class="stat-label">系统状态</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon" style="background: #409EFF;">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ systemStatus.uptime }}</div>
              <div class="stat-label">运行时间</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon" style="background: #E6A23C;">
              <el-icon><Cpu /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ systemStatus.cpuUsage }}%</div>
              <div class="stat-label">CPU使用率</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon" style="background: #F56C6C;">
              <el-icon><MemoryCard /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ systemStatus.memoryUsage }}%</div>
              <div class="stat-label">内存使用率</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 实时监控图表 -->
    <div class="card">
      <h3>实时监控</h3>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="chart-container">
            <v-chart :option="cpuChartOption" autoresize />
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-container">
            <v-chart :option="memoryChartOption" autoresize />
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="12">
          <div class="chart-container">
            <v-chart :option="networkChartOption" autoresize />
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-container">
            <v-chart :option="diskChartOption" autoresize />
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 任务监控 -->
    <div class="card">
      <h3>任务监控</h3>
      <div class="toolbar">
        <el-button @click="refreshTasks" :loading="loading.tasks">
          <el-icon><Refresh /></el-icon>
          刷新任务
        </el-button>
        <el-select v-model="taskFilter" placeholder="任务类型" style="width: 150px; margin-left: 10px;">
          <el-option label="全部" value="" />
          <el-option label="训练任务" value="train" />
          <el-option label="预测任务" value="predict" />
          <el-option label="决策任务" value="decision" />
        </el-select>
        <el-select v-model="statusFilter" placeholder="任务状态" style="width: 150px; margin-left: 10px;">
          <el-option label="全部" value="" />
          <el-option label="运行中" value="running" />
          <el-option label="已完成" value="completed" />
          <el-option label="已停止" value="stopped" />
          <el-option label="失败" value="failed" />
        </el-select>
      </div>
      
      <el-table :data="filteredTasks" style="width: 100%" v-loading="loading.tasks">
        <el-table-column prop="id" label="任务ID" width="100" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="getTaskTypeColor(scope.row.type)">{{ getTaskTypeName(scope.row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="项目名称" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">{{ getStatusName(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="运行时长" width="120" />
        <el-table-column prop="progress" label="进度" width="120">
          <template #default="scope">
            <el-progress :percentage="scope.row.progress" :stroke-width="8" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button 
              v-if="scope.row.status === 'running'" 
              size="small" 
              type="warning" 
              @click="stopTask(scope.row)"
            >
              停止
            </el-button>
            <el-button size="small" @click="viewTaskDetail(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 系统日志 -->
    <div class="card">
      <h3>系统日志</h3>
      <div class="toolbar">
        <el-button @click="refreshLogs" :loading="loading.logs">
          <el-icon><Refresh /></el-icon>
          刷新日志
        </el-button>
        <el-select v-model="logLevel" placeholder="日志级别" style="width: 120px; margin-left: 10px;">
          <el-option label="全部" value="" />
          <el-option label="INFO" value="info" />
          <el-option label="WARNING" value="warning" />
          <el-option label="ERROR" value="error" />
          <el-option label="DEBUG" value="debug" />
        </el-select>
        <el-button @click="clearLogs" type="danger" plain style="margin-left: 10px;">
          <el-icon><Delete /></el-icon>
          清空日志
        </el-button>
      </div>
      
      <div class="log-container">
        <div 
          v-for="log in filteredLogs" 
          :key="log.id" 
          :class="['log-item', `log-${log.level}`]"
        >
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        
        <div v-if="filteredLogs.length === 0" class="no-logs">
          暂无日志记录
        </div>
      </div>
    </div>

    <!-- 性能指标 -->
    <div class="card">
      <h3>性能指标</h3>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <span>API响应时间</span>
            </template>
            <div class="metric-item">
              <div class="metric-value">{{ performanceMetrics.apiResponseTime }}ms</div>
              <div class="metric-trend" :class="performanceMetrics.apiTrend">
                <el-icon><TrendCharts /></el-icon>
                {{ performanceMetrics.apiTrend === 'up' ? '↑' : '↓' }} 5.2%
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <span>数据库连接</span>
            </template>
            <div class="metric-item">
              <div class="metric-value">{{ performanceMetrics.dbConnections }}</div>
              <div class="metric-label">活跃连接数</div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <span>错误率</span>
            </template>
            <div class="metric-item">
              <div class="metric-value">{{ performanceMetrics.errorRate }}%</div>
              <div class="metric-trend" :class="performanceMetrics.errorTrend">
                <el-icon><Warning /></el-icon>
                {{ performanceMetrics.errorTrend === 'up' ? '↑' : '↓' }} 1.1%
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 任务详情对话框 -->
    <el-dialog v-model="taskDetailVisible" title="任务详情" width="60%">
      <div v-if="selectedTask">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ selectedTask.id }}</el-descriptions-item>
          <el-descriptions-item label="任务类型">{{ getTaskTypeName(selectedTask.type) }}</el-descriptions-item>
          <el-descriptions-item label="项目名称">{{ selectedTask.projectName }}</el-descriptions-item>
          <el-descriptions-item label="状态">{{ getStatusName(selectedTask.status) }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatTime(selectedTask.startTime) }}</el-descriptions-item>
          <el-descriptions-item label="运行时长">{{ selectedTask.duration }}</el-descriptions-item>
          <el-descriptions-item label="进度">{{ selectedTask.progress }}%</el-descriptions-item>
          <el-descriptions-item label="配置参数">
            <pre>{{ JSON.stringify(selectedTask.config, null, 2) }}</pre>
          </el-descriptions-item>
        </el-descriptions>
        
        <div style="margin-top: 20px;">
          <h4>执行日志</h4>
          <div class="task-log-container">
            <div v-for="log in selectedTask.logs" :key="log.id" class="task-log-item">
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { systemAPI } from '../utils/api'
import db from '../utils/database'
import { useSystemStore } from '../stores/system'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const systemStore = useSystemStore()

const loading = reactive({
  tasks: false,
  logs: false
})

const systemStatus = reactive({
  status: '正常',
  uptime: '0天0小时',
  cpuUsage: 0,
  memoryUsage: 0
})

const tasks = ref([])
const logs = ref([])
const taskFilter = ref('')
const statusFilter = ref('')
const logLevel = ref('')
const taskDetailVisible = ref(false)
const selectedTask = ref(null)

const performanceMetrics = reactive({
  apiResponseTime: 125,
  apiTrend: 'down',
  dbConnections: 8,
  errorRate: 0.5,
  errorTrend: 'down'
})

// 监控数据
const monitoringData = reactive({
  cpu: [],
  memory: [],
  network: [],
  disk: []
})

let monitoringInterval = null

// 过滤后的任务
const filteredTasks = computed(() => {
  return tasks.value.filter(task => {
    const typeMatch = !taskFilter.value || task.type === taskFilter.value
    const statusMatch = !statusFilter.value || task.status === statusFilter.value
    return typeMatch && statusMatch
  })
})

// 过滤后的日志
const filteredLogs = computed(() => {
  return logs.value.filter(log => {
    return !logLevel.value || log.level === logLevel.value
  })
})

// CPU使用率图表配置
const cpuChartOption = computed(() => {
  return {
    title: {
      text: 'CPU使用率',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}<br/>CPU: {c}%'
    },
    xAxis: {
      type: 'time',
      boundaryGap: false
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        type: 'line',
        data: monitoringData.cpu,
        smooth: true,
        lineStyle: { color: '#409EFF' },
        areaStyle: { color: 'rgba(64, 158, 255, 0.1)' }
      }
    ]
  }
})

// 内存使用率图表配置
const memoryChartOption = computed(() => {
  return {
    title: {
      text: '内存使用率',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}<br/>内存: {c}%'
    },
    xAxis: {
      type: 'time',
      boundaryGap: false
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        type: 'line',
        data: monitoringData.memory,
        smooth: true,
        lineStyle: { color: '#67C23A' },
        areaStyle: { color: 'rgba(103, 194, 58, 0.1)' }
      }
    ]
  }
})

// 网络流量图表配置
const networkChartOption = computed(() => {
  return {
    title: {
      text: '网络流量',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      top: 30,
      data: ['上行', '下行']
    },
    xAxis: {
      type: 'time',
      boundaryGap: false
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} KB/s'
      }
    },
    series: [
      {
        name: '上行',
        type: 'line',
        data: monitoringData.network.map(item => [item[0], item[1]]),
        smooth: true,
        lineStyle: { color: '#E6A23C' }
      },
      {
        name: '下行',
        type: 'line',
        data: monitoringData.network.map(item => [item[0], item[2]]),
        smooth: true,
        lineStyle: { color: '#F56C6C' }
      }
    ]
  }
})

// 磁盘I/O图表配置
const diskChartOption = computed(() => {
  return {
    title: {
      text: '磁盘I/O',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      top: 30,
      data: ['读取', '写入']
    },
    xAxis: {
      type: 'time',
      boundaryGap: false
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} MB/s'
      }
    },
    series: [
      {
        name: '读取',
        type: 'line',
        data: monitoringData.disk.map(item => [item[0], item[1]]),
        smooth: true,
        lineStyle: { color: '#909399' }
      },
      {
        name: '写入',
        type: 'line',
        data: monitoringData.disk.map(item => [item[0], item[2]]),
        smooth: true,
        lineStyle: { color: '#606266' }
      }
    ]
  }
})

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 获取任务类型颜色
const getTaskTypeColor = (type) => {
  const colors = {
    train: 'primary',
    predict: 'success',
    decision: 'warning'
  }
  return colors[type] || ''
}

// 获取任务类型名称
const getTaskTypeName = (type) => {
  const names = {
    train: '训练任务',
    predict: '预测任务',
    decision: '决策任务'
  }
  return names[type] || type
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colors = {
    running: 'primary',
    completed: 'success',
    stopped: 'warning',
    failed: 'danger'
  }
  return colors[status] || ''
}

// 获取状态名称
const getStatusName = (status) => {
  const names = {
    running: '运行中',
    completed: '已完成',
    stopped: '已停止',
    failed: '失败'
  }
  return names[status] || status
}

// 生成监控数据
const generateMonitoringData = () => {
  const now = Date.now()
  const maxPoints = 50
  
  // 生成CPU数据
  if (monitoringData.cpu.length >= maxPoints) {
    monitoringData.cpu.shift()
  }
  monitoringData.cpu.push([
    now,
    Math.max(0, Math.min(100, systemStatus.cpuUsage + (Math.random() - 0.5) * 10))
  ])
  
  // 生成内存数据
  if (monitoringData.memory.length >= maxPoints) {
    monitoringData.memory.shift()
  }
  monitoringData.memory.push([
    now,
    Math.max(0, Math.min(100, systemStatus.memoryUsage + (Math.random() - 0.5) * 8))
  ])
  
  // 生成网络数据
  if (monitoringData.network.length >= maxPoints) {
    monitoringData.network.shift()
  }
  monitoringData.network.push([
    now,
    Math.random() * 100 + 50, // 上行
    Math.random() * 200 + 100  // 下行
  ])
  
  // 生成磁盘数据
  if (monitoringData.disk.length >= maxPoints) {
    monitoringData.disk.shift()
  }
  monitoringData.disk.push([
    now,
    Math.random() * 50 + 10,  // 读取
    Math.random() * 30 + 5    // 写入
  ])
  
  // 更新系统状态
  systemStatus.cpuUsage = Math.max(0, Math.min(100, systemStatus.cpuUsage + (Math.random() - 0.5) * 5))
  systemStatus.memoryUsage = Math.max(0, Math.min(100, systemStatus.memoryUsage + (Math.random() - 0.5) * 3))
}

// 刷新任务列表
const refreshTasks = async () => {
  try {
    loading.tasks = true
    
    // 模拟任务数据
    tasks.value = [
      {
        id: 'T001',
        type: 'train',
        projectName: 'project_alpha',
        status: 'running',
        startTime: Date.now() - 3600000,
        duration: '1小时',
        progress: 75,
        config: { algorithm: 'TimesNet', epochs: 100 },
        logs: [
          { id: 1, timestamp: Date.now() - 3600000, message: '开始训练模型' },
          { id: 2, timestamp: Date.now() - 3000000, message: 'Epoch 1/100 完成' },
          { id: 3, timestamp: Date.now() - 1800000, message: 'Epoch 50/100 完成' }
        ]
      },
      {
        id: 'P001',
        type: 'predict',
        projectName: 'project_beta',
        status: 'running',
        startTime: Date.now() - 1800000,
        duration: '30分钟',
        progress: 100,
        config: { predictor: 'sequence_realtime', interval: 60 },
        logs: [
          { id: 1, timestamp: Date.now() - 1800000, message: '开始实时预测' },
          { id: 2, timestamp: Date.now() - 900000, message: '预测数据更新' }
        ]
      },
      {
        id: 'D001',
        type: 'decision',
        projectName: 'project_gamma',
        status: 'completed',
        startTime: Date.now() - 7200000,
        duration: '2小时',
        progress: 100,
        config: { objective: 'maximize_yield', interval: 120 },
        logs: [
          { id: 1, timestamp: Date.now() - 7200000, message: '开始决策优化' },
          { id: 2, timestamp: Date.now() - 3600000, message: '找到最优解' },
          { id: 3, timestamp: Date.now() - 1800000, message: '决策任务完成' }
        ]
      }
    ]
    
  } catch (error) {
    console.error('刷新任务失败:', error)
  } finally {
    loading.tasks = false
  }
}

// 刷新日志
const refreshLogs = async () => {
  try {
    loading.logs = true
    logs.value = await db.getSystemLogs(100)
    
    // 如果没有日志,添加一些模拟日志
    if (logs.value.length === 0) {
      const mockLogs = [
        { id: 1, timestamp: Date.now() - 3600000, level: 'info', message: '系统启动完成' },
        { id: 2, timestamp: Date.now() - 3000000, level: 'info', message: '数据库连接成功' },
        { id: 3, timestamp: Date.now() - 2400000, level: 'warning', message: 'CPU使用率较高: 85%' },
        { id: 4, timestamp: Date.now() - 1800000, level: 'info', message: '训练任务 T001 启动' },
        { id: 5, timestamp: Date.now() - 1200000, level: 'error', message: '预测任务 P002 执行失败' },
        { id: 6, timestamp: Date.now() - 600000, level: 'info', message: '决策任务 D001 完成' }
      ]
      
      for (const log of mockLogs) {
        await db.addSystemLog(log)
      }
      
      logs.value = await db.getSystemLogs(100)
    }
    
  } catch (error) {
    console.error('刷新日志失败:', error)
  } finally {
    loading.logs = false
  }
}

// 停止任务
const stopTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要停止任务 ${task.id} 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用相应的停止API
    task.status = 'stopped'
    ElMessage.success('任务已停止')
    
    // 记录日志
    await db.addSystemLog({
      level: 'info',
      message: `任务 ${task.id} 已被手动停止`
    })
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止任务失败:', error)
    }
  }
}

// 查看任务详情
const viewTaskDetail = (task) => {
  selectedTask.value = task
  taskDetailVisible.value = true
}

// 清空日志
const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有系统日志吗？此操作不可恢复。',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await db.clearStore('systemLogs')
    await refreshLogs()
    ElMessage.success('系统日志已清空')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空日志失败:', error)
    }
  }
}

// 更新系统运行时间
const updateUptime = () => {
  const startTime = systemStore.systemInfo.startTime || Date.now()
  const uptime = Date.now() - startTime
  const days = Math.floor(uptime / (1000 * 60 * 60 * 24))
  const hours = Math.floor((uptime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60))
  
  systemStatus.uptime = `${days}天${hours}小时${minutes}分钟`
}

onMounted(async () => {
  // 初始化系统状态
  systemStatus.cpuUsage = Math.random() * 30 + 20
  systemStatus.memoryUsage = Math.random() * 40 + 30
  
  // 加载数据
  await refreshTasks()
  await refreshLogs()
  
  // 启动监控数据生成
  monitoringInterval = setInterval(() => {
    generateMonitoringData()
    updateUptime()
  }, 2000)
})

onUnmounted(() => {
  if (monitoringInterval) {
    clearInterval(monitoringInterval)
  }
})
</script>

<style scoped>
.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  margin-right: 15px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.chart-container {
  height: 250px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  background: #fafafa;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  width: 180px;
  color: #909399;
  margin-right: 10px;
}

.log-level {
  width: 80px;
  margin-right: 10px;
  font-weight: bold;
}

.log-message {
  flex: 1;
}

.log-info .log-level {
  color: #409EFF;
}

.log-warning .log-level {
  color: #E6A23C;
}

.log-error .log-level {
  color: #F56C6C;
}

.log-debug .log-level {
  color: #909399;
}

.no-logs {
  text-align: center;
  color: #909399;
  padding: 40px;
}

.metric-item {
  text-align: center;
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.metric-label {
  font-size: 14px;
  color: #909399;
}

.metric-trend {
  font-size: 14px;
  margin-top: 5px;
}

.metric-trend.up {
  color: #F56C6C;
}

.metric-trend.down {
  color: #67C23A;
}

.task-log-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  background: #fafafa;
}

.task-log-item {
  display: flex;
  padding: 5px 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.task-log-item .log-time {
  width: 150px;
  color: #909399;
  margin-right: 10px;
}

.task-log-item .log-message {
  flex: 1;
  color: #303133;
}
</style>