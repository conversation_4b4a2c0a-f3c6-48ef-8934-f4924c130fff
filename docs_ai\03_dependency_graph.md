
# 3. 关键模块依赖关系图

本图展示了 `src/backend` 中关键模块之间的依赖关系。

```mermaid
graph TD
    subgraph "Entrypoints"
        CLI(main.py)
        WebApp(app.py)
    end

    subgraph "Core Logic"
        Routers(routers/)
        Tasks(tasks/)
        Schedulers(utils/task_scheduler.py)
        Models(api/requestbody.py)
    end

    subgraph "External Libs"
        FastAPI
        Typer
        Uvicorn
        APScheduler
        IndustryTSLib
    end

    CLI -- uses --> Typer
    CLI -- runs --> WebApp
    
    WebApp -- uses --> FastAPI
    WebApp -- uses --> Uvicorn
    WebApp -- initializes --> Schedulers
    WebApp -- includes --> Routers
    WebApp -- uses --> IndustryTSLib

    Routers -- depends on --> Models
    Routers -- uses --> Schedulers
    Routers -- uses --> Tasks

    Tasks -- depends on --> IndustryTSLib

    Schedulers -- uses --> APScheduler
```

## 依赖说明

- **`main.py` (CLI)** 是最高层入口,它依赖 `Typer` 创建命令行,并最终通过 `uvicorn` 运行 `app.py`。
- **`app.py` (WebApp)** 是应用的核心,强依赖 `FastAPI`。它负责组装整个应用,包括:
    - 初始化 **`utils/task_scheduler.py`** 中的调度器。
    - 注册 **`routers/`** 中定义的所有路由。
    - 调用 **`industrytslib`** 中的功能(如日志、配置读取)。
- **`routers/`** (路由层) 是 HTTP 请求的直接处理者。它们依赖:
    - **`api/requestbody.py` (Models)** 来校验和解析请求数据。
    - **`utils/task_scheduler.py` (Schedulers)** 来调度新的后台任务。
    - **`tasks/`** (任务构建器) 来获取具体的任务执行对象 (Trainer, Predictor)。
- **`tasks/`** (任务层) 封装了对 `industrytslib` 的调用,这是执行核心业务逻辑的地方。
- **`utils/task_scheduler.py`** 是对 `APScheduler` 的封装和配置,为上层模块提供简单易用的调度接口。
