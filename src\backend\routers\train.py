""" 
train 相关路由
本模块提供模型训练相关的API接口,包括:
- 单个模型训练
- 批量模型训练
- 停止指定模型训练
- 停止所有模型训练
- 查询最新训练效果

训练任务设计逻辑:
1. 定时训练任务：通过APScheduler调度器管理,在独立子进程中执行训练
2. 批量训练任务：直接在子进程中执行,不使用调度器
3. 强制停止机制：通过终止子进程实现强制中断,无需等待训练任务协作

停止方法说明:
- stop_train: 停止指定项目的训练任务,先从调度器移除任务,再强制终止对应子进程
- stop_train_all: 停止所有训练任务,清空调度器并终止所有训练子进程
- 子进程终止后会自动从进程字典中清理,避免僵尸进程
"""
import multiprocessing
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional
from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse

from industrytslib import (
    get_logger, 
    read_config_toml, 
    get_trainer
)

from api import (
    TrainModelRequestBody, 
    TrainModelListRequestBody, 
    StopTrainRequestBody,
    QueryLatestTrainEffectRequestBody
)
from utils.task_scheduler import train_scheduler


router = APIRouter()
trainer_logger = get_logger(
    logger_name='trainer',
    logger_type='routers',
    level='DEBUG',
    console_level='WARNING'
)
dbconfig = read_config_toml('config/database_config.toml')

# 全局进程字典,用于管理训练子进程
# 键：项目名称,值：multiprocessing.Process对象
training_processes: Dict[str, multiprocessing.Process] = {}


def run_training_in_process(project_name: str, algorithm_type: str, dbconfig: dict):
    """
    在独立子进程中运行训练任务
    
    Args:
        project_name: 项目名称
        algorithm_type: 算法类型
        dbconfig: 数据库配置
        
    说明:
        此函数将在子进程中执行,与主进程完全隔离
        子进程被终止时,训练任务会立即停止,无需协作
    """
    try:
        # 在子进程中获取训练器并执行训练
        trainer = get_trainer(
            project_name=project_name, 
            dbconfig=dbconfig, 
            model_type=algorithm_type
        )
        trainer.main()
    except Exception as e:
        # 子进程中的异常处理
        print(f"训练进程 {project_name} 执行异常: {e}")
    finally:
        # 子进程结束时的清理工作
        print(f"训练进程 {project_name} 已结束")


def cleanup_finished_processes():
    """
    清理已结束的训练进程
    
    说明:
        定期调用此函数清理已结束的子进程,避免僵尸进程
        从training_processes字典中移除已结束的进程
    """
    finished_processes = []
    for project_name, process in training_processes.items():
        if not process.is_alive():
            finished_processes.append(project_name)
    
    for project_name in finished_processes:
        process = training_processes.pop(project_name)
        process.join()  # 确保进程完全结束
        trainer_logger.info(f"清理已结束的训练进程: {project_name}")


def force_stop_training_process(project_name: str) -> bool:
    """
    强制停止指定项目的训练进程
    
    Args:
        project_name: 项目名称
        
    Returns:
        bool: 是否成功停止进程
        
    说明:
        1. 首先尝试优雅终止(SIGTERM)
        2. 等待3秒后如果进程仍在运行,强制杀死(SIGKILL)
        3. 从进程字典中移除已停止的进程
    """
    if project_name not in training_processes:
        trainer_logger.warning(f"未找到项目 {project_name} 的训练进程")
        return False
    
    process = training_processes[project_name]
    
    if not process.is_alive():
        # 进程已经结束,直接清理
        training_processes.pop(project_name)
        trainer_logger.info(f"训练进程 {project_name} 已自然结束")
        return True
    
    try:
        # 尝试优雅终止
        trainer_logger.info(f"正在优雅终止训练进程: {project_name}")
        process.terminate()
        
        # 等待进程结束,最多等待3秒
        process.join(timeout=3)
        
        if process.is_alive():
            # 如果进程仍在运行,强制杀死
            trainer_logger.warning(f"优雅终止失败,强制杀死训练进程: {project_name}")
            process.kill()
            process.join()
        
        # 从字典中移除
        training_processes.pop(project_name)
        trainer_logger.info(f"成功停止训练进程: {project_name}")
        return True
        
    except Exception as e:
        trainer_logger.error(f"停止训练进程 {project_name} 时发生错误: {e}")
        return False


@router.get("/")
async def train_index():
    """
    训练模块首页
    返回训练模块的基本信息
    """
    return {"message": "train information page"}


@router.post("/train")
async def train(request: TrainModelRequestBody):
    r"""
    启动单个模型训练任务
    
    Args:
        request: 包含项目名称、算法类型和训练间隔等信息的请求体
        
    流程:
        1. 记录训练请求信息
        2. 如果已存在同名训练任务,则先停止(包括调度器任务和子进程)
        3. 创建包装函数,在子进程中执行训练
        4. 将训练任务添加到调度器中并立即执行
        5. 返回训练请求成功的消息
        
    设计说明:
        - 使用子进程隔离训练任务,避免影响主进程
        - 调度器负责定时触发,子进程负责实际执行
        - 支持强制终止：可以直接杀死子进程来停止训练
    """
    trainer_logger.info(f'训练模型请求: {request}')
    
    # 获取以秒为单位的间隔时间
    try:
        interval_seconds = request.get_interval_seconds()
    except ValueError as e:
        trainer_logger.error(f'训练间隔参数错误: {e}')
        return {'code': 400, 'message': f'训练间隔参数错误: {e}'}
    
    # 清理已结束的进程
    cleanup_finished_processes()
    
    # 如果已存在同名训练任务,先完全停止
    project_name = request.project_name
    job_id = project_name
    
    # 从调度器中移除任务
    if train_scheduler.get_job(job_id):
        train_scheduler.remove_job(job_id)
        trainer_logger.info(f"移除已存在的调度任务: {project_name}")
    
    # 强制停止已存在的训练进程
    if project_name in training_processes:
        force_stop_training_process(project_name)
        trainer_logger.info(f"停止已存在的训练进程: {project_name}")
    
    def start_training_process():
        """
        启动训练子进程的包装函数
        
        说明:
            此函数将被调度器调用,负责创建和管理训练子进程
            每次调度执行时都会创建新的子进程
        """
        try:
            # 清理可能存在的旧进程
            if project_name in training_processes:
                old_process = training_processes[project_name]
                if old_process.is_alive():
                    trainer_logger.warning(f"发现运行中的旧进程,正在终止: {project_name}")
                    force_stop_training_process(project_name)
            
            # 创建新的训练子进程
            trainer_logger.info(f"启动训练子进程: {project_name}")
            process = multiprocessing.Process(
                target=run_training_in_process,
                args=(project_name, request.algorithm_type, dbconfig),
                name=f"train_{project_name}"
            )
            
            # 启动进程并记录
            process.start()
            training_processes[project_name] = process
            trainer_logger.info(f"训练子进程已启动: {project_name}, PID: {process.pid}")
            
        except Exception as e:
            trainer_logger.error(f"启动训练子进程失败 {project_name}: {e}")
    
    # 定期执行,设置3天的错过任务容忍时间
    # 3天 = 3 * 24 * 3600 = 259200秒
    misfire_grace_time_days = 3 * 24 * 3600  # 3天的容忍时间
    
    train_scheduler.add_job(
        start_training_process,
        trigger='interval',
        seconds=interval_seconds,
        id=job_id,
        name=project_name,
        replace_existing=True,
        misfire_grace_time=misfire_grace_time_days,
        next_run_time=datetime.now()
    )

    trainer_logger.info(f'训练模型{project_name}请求成功!!! 间隔: {interval_seconds}秒, 错过任务容忍时间: {misfire_grace_time_days}秒(3天)')

    return {'code': 200, 'message': f'训练模型{project_name}请求成功!!!'}


@router.post('/train_list')
async def train_list(request: TrainModelListRequestBody):
    """
    批量启动多个模型训练任务
    
    参数:
        request: 包含项目名称列表和算法类型列表的请求体
        
    流程:
        1. 记录批量训练请求信息
        2. 遍历项目列表,对每个项目:
            - 如果已存在同名训练任务,则先停止
            - 在独立子进程中执行训练任务
        3. 返回批量训练请求成功的消息
        
    设计说明:
        - 批量训练不使用调度器,直接启动子进程
        - 每个项目在独立子进程中运行,互不影响
        - 支持强制停止：可以通过stop_train接口终止特定项目
    """
    trainer_logger.info(f'训练列表请求: {request}')
    train_project_name_list = request.project_name_list
    algorithm_type_list = request.algorithm_type_list
    
    # 获取以秒为单位的间隔时间列表 (批量训练直接执行,不使用间隔时间)
    try:
        # 验证间隔参数格式是否正确,但不实际使用
        request.get_interval_seconds_list()
    except ValueError as e:
        trainer_logger.error(f'训练间隔参数错误: {e}')
        return {'code': 400, 'message': f'训练间隔参数错误: {e}'}
    
    # 清理已结束的进程
    cleanup_finished_processes()
    
    for i, project_name in enumerate(train_project_name_list):
        # 如果已存在同名训练任务,先停止
        job_id = f"train_{project_name}"
        if train_scheduler.get_job(job_id):
            train_scheduler.remove_job(job_id)
            trainer_logger.info(f"移除已存在的调度任务: {project_name}")
        
        # 强制停止已存在的训练进程
        if project_name in training_processes:
            force_stop_training_process(project_name)
            trainer_logger.info(f"停止已存在的训练进程: {project_name}")
        
        # 在子进程中启动训练
        try:
            trainer_logger.info(f"启动批量训练子进程: {project_name}")
            process = multiprocessing.Process(
                target=run_training_in_process,
                args=(project_name, algorithm_type_list[i], dbconfig),
                name=f"batch_train_{project_name}"
            )
            
            process.start()
            training_processes[project_name] = process
            trainer_logger.info(f"批量训练子进程已启动: {project_name}, PID: {process.pid}")
            
        except Exception as e:
            trainer_logger.error(f"启动批量训练子进程失败 {project_name}: {e}")

    return {'code': 200, 'message': f'训练列表{train_project_name_list}请求成功!!!'}


@router.post('/stop_train')
async def stop_train(request: StopTrainRequestBody):
    """
    停止指定模型的训练任务
    
    参数:
        request: 包含要停止训练的项目名称的请求体
        
    流程:
        1. 记录停止训练请求信息
        2. 从调度器中移除定时任务
        3. 强制终止对应的训练子进程
        4. 返回停止训练成功的消息
        
    停止机制说明:
        - 双重停止：既停止调度器任务,也终止子进程
        - 强制终止：直接杀死子进程,无需等待训练任务协作
        - 进程清理：自动从进程字典中移除已停止的进程
    """
    trainer_logger.info(f'停止训练请求: {request}')
    
    project_name = request.project_name
    success_messages = []
    
    # 1. 从调度器中移除任务
    job_id = project_name
    if train_scheduler.get_job(job_id):
        train_scheduler.remove_job(job_id)
        success_messages.append(f"已从调度器移除任务: {project_name}")
        trainer_logger.info(f"从调度器移除训练任务: {project_name}")
    
    # 2. 强制停止训练进程
    if force_stop_training_process(project_name):
        success_messages.append(f"已强制停止训练进程: {project_name}")
    else:
        # 即使进程停止失败,也不算完全失败,因为可能进程本来就不存在
        success_messages.append(f"未找到运行中的训练进程: {project_name}")
    
    # 3. 清理已结束的进程
    cleanup_finished_processes()
    
    message = "; ".join(success_messages)
    trainer_logger.info(f'停止训练{project_name}完成: {message}')

    return {'code': 200, 'message': f'停止训练{project_name}成功!!! {message}'}


@router.post('/stop_train_all')
async def stop_train_all():
    """
    停止所有正在进行的训练任务
    
    流程:
        1. 记录停止所有训练任务的请求
        2. 从调度器中移除所有训练相关任务
        3. 强制终止所有训练子进程
        4. 清理进程字典
        5. 返回停止所有训练任务成功的消息
        
    全面停止机制:
        - 调度器清理：移除所有包含'train'的调度任务
        - 进程终止：强制杀死所有训练子进程
        - 资源清理：清空进程字典,避免内存泄漏
    """
    trainer_logger.info('开始停止所有训练任务')
    
    stopped_jobs = []
    stopped_processes = []
    
    # 1. 停止所有调度器中的训练任务
    jobs = train_scheduler.get_jobs()
    for job in jobs:
        if 'train' in job.id:
            train_scheduler.remove_job(job.id)
            stopped_jobs.append(job.id)
            trainer_logger.info(f'从调度器移除训练任务: {job.id}')
    
    # 2. 强制停止所有训练进程
    # 创建进程名称列表的副本,避免在迭代时修改字典
    process_names = list(training_processes.keys())
    for project_name in process_names:
        if force_stop_training_process(project_name):
            stopped_processes.append(project_name)
    
    # 3. 清理已结束的进程
    cleanup_finished_processes()
    
    # 4. 确保进程字典完全清空
    remaining_processes = list(training_processes.keys())
    if remaining_processes:
        trainer_logger.warning(f"仍有进程未清理: {remaining_processes}")
        # 强制清空字典
        training_processes.clear()
    
    trainer_logger.info(f'停止所有训练任务完成!!! 调度任务: {len(stopped_jobs)}个, 训练进程: {len(stopped_processes)}个')

    return {
        'code': 200, 
        'message': f'停止所有训练任务成功!!! 调度任务: {len(stopped_jobs)}个, 训练进程: {len(stopped_processes)}个',
        'details': {
            'stopped_jobs': stopped_jobs,
            'stopped_processes': stopped_processes
        }
    }


# 查询当前有哪些正在运行的训练任务
@router.get('/query_train_tasks')
async def query_train_tasks():
    """
    查询当前有哪些正在运行的训练任务
    
    流程:
        1. 获取调度器中的所有任务
        2. 筛选出ID中包含'train'的任务
        3. 获取正在运行的训练进程信息
        4. 返回完整的训练任务状态
        
    返回信息包括:
        - 调度器任务：定时训练任务的调度信息
        - 运行进程：当前正在执行的训练子进程
    """
    trainer_logger.info('查询当前正在运行的训练任务')
    
    # 清理已结束的进程
    cleanup_finished_processes()
    
    # 获取调度器中的训练任务
    jobs = train_scheduler.get_jobs()
    scheduled_tasks = []
    
    for job in jobs:
        if 'train' in job.id:
            scheduled_tasks.append({
                'id': job.id,
                'project_name': job.name,
                'next_run_time': job.next_run_time,
                'last_run_time': getattr(job, 'last_run_time', None),
                'type': 'scheduled'
            })
    
    # 获取正在运行的训练进程
    running_processes = []
    for project_name, process in training_processes.items():
        if process.is_alive():
            running_processes.append({
                'project_name': project_name,
                'pid': process.pid,
                'process_name': process.name,
                'is_alive': True,
                'type': 'running_process'
            })
    
    total_tasks = len(scheduled_tasks) + len(running_processes)
    trainer_logger.info(f'当前状态 - 调度任务: {len(scheduled_tasks)}个, 运行进程: {len(running_processes)}个')
    
    return {
        'code': 200, 
        'message': '查询成功', 
        'data': {
            'total_count': total_tasks,
            'scheduled_tasks': scheduled_tasks,
            'running_processes': running_processes,
            'summary': {
                'scheduled_count': len(scheduled_tasks),
                'running_count': len(running_processes)
            }
        }
    }

# 查询最新的训练效果,返回png图片
@router.post('/query_latest_train_effect')
async def query_latest_train_effect(request: QueryLatestTrainEffectRequestBody):
    """
    查询最新的训练效果,返回png图片
    
    图片存储文件夹:
        - resource/results/test_results/{project_name}
    先查看这个文件夹下是否有test_result_ndarray文件夹,如果有则进入这个文件夹,查看最新的图片并返回
    如果没有则查看这个文件夹下最新的文件夹,返回最新的文件夹下的true_pred_all.png
    
    Args:
        project_name: 项目名称
        
    流程:
        1. 构建基础路径
        2. 查找test_result_ndarray文件夹
        3. 如果存在,返回其中最新的图片
        4. 否则查找最新的时间戳文件夹中的true_pred_all.png
        5. 返回找到的图片文件
    
    Returns:
        FileResponse: PNG图片文件
        
    Raises:
        HTTPException: 当找不到训练效果图片时
    """
    project_name = request.project_name
    trainer_logger.info(f'查询最新的训练效果,项目名称: {project_name}')
    
    try:
        # 构建基础路径
        base_path = Path("resource") / "results" / "test_results" / project_name
        trainer_logger.info(f'查找路径: {base_path}')
        
        # 检查基础路径是否存在
        if not base_path.exists():
            trainer_logger.warning(f'基础路径不存在: {base_path}')
            raise HTTPException(
                status_code=404, 
                detail=f"训练结果路径不存在: {project_name}"
            )
        
        # 查找图片文件
        image_path = await _find_latest_train_effect_image(base_path)
        
        if image_path is None:
            trainer_logger.error(f'未找到训练效果图片: {project_name}')
            raise HTTPException(
                status_code=404, 
                detail=f"未找到项目 {project_name} 的训练效果图片"
            )
        
        trainer_logger.info(f'找到训练效果图片: {image_path}')
        
        # 返回图片文件
        return FileResponse(
            path=str(image_path),
            media_type="image/png",
            filename=f"{project_name}.png"
        )
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        trainer_logger.error(f'查询训练效果时发生错误: {str(e)}')
        raise HTTPException(
            status_code=500, 
            detail=f"查询训练效果时发生内部错误: {str(e)}"
        )


async def _find_latest_train_effect_image(base_path: Path) -> Optional[Path]:
    """
    查找最新的训练效果图片
    
    Args:
        base_path: 基础路径 (resource/results/test_results/{project_name})
        
    Returns:
        Path: 找到的图片文件路径,如果未找到则返回None
    """
    trainer_logger.info(f'开始查找训练效果图片,基础路径: {base_path}')
    
    # 方法1: 优先查找 test_result_ndarray 文件夹
    ndarray_path = base_path / "test_result_ndarray"
    if ndarray_path.exists() and ndarray_path.is_dir():
        trainer_logger.info(f'找到test_result_ndarray文件夹: {ndarray_path}')
        
        # 查找该文件夹下最新的PNG图片
        png_files = list(ndarray_path.glob("*.png"))
        if png_files:
            # 按修改时间排序,获取最新的文件
            latest_png = max(png_files, key=lambda p: p.stat().st_mtime)
            trainer_logger.info(f'找到最新的PNG图片: {latest_png}')
            return latest_png
        else:
            trainer_logger.warning('test_result_ndarray文件夹中没有PNG文件')
    
    # 方法2: 查找最新的时间戳文件夹中的 true_pred_all.png
    trainer_logger.info('查找最新的时间戳文件夹')
    
    # 获取所有子文件夹
    subdirs = [d for d in base_path.iterdir() if d.is_dir()]
    if not subdirs:
        trainer_logger.warning('没有找到任何子文件夹')
        return None
    
    # 按修改时间排序,获取最新的文件夹
    latest_dir = max(subdirs, key=lambda d: d.stat().st_mtime)
    trainer_logger.info(f'找到最新的文件夹: {latest_dir}')
    
    # 查找 true_pred_all.png
    true_pred_file = latest_dir / "true_pred_all.png"
    if true_pred_file.exists():
        trainer_logger.info(f'找到true_pred_all.png: {true_pred_file}')
        return true_pred_file
    
    # 如果没有找到 true_pred_all.png,查找该文件夹下的任何PNG文件
    png_files = list(latest_dir.glob("*.png"))
    if png_files:
        # 按修改时间排序,获取最新的文件
        latest_png = max(png_files, key=lambda p: p.stat().st_mtime)
        trainer_logger.info(f'在最新文件夹中找到PNG图片: {latest_png}')
        return latest_png
    
    trainer_logger.warning('未找到任何PNG图片文件')
    return None
