
# 4. 核心类/接口功能说明

本部分介绍项目中的核心类和 Pydantic 模型。

## 4.1. Pydantic 请求模型 (`api/requestbody.py`)

这些模型定义了 API 的契约。

| 类名 | 用途 | 关键属性 |
| :--- | :--- | :--- |
| `TrainModelRequestBody` | 单个模型训练请求 | `project_name`, `interval_hours`, `algorithm_type` |
| `TrainModelListRequestBody` | 批量模型训练请求 | `project_name_list`, `interval_hours_list`, `algorithm_type_list` |
| `StopTrainRequestBody` | 停止训练请求 | `project_name` |
| `RealtimePredictRequestBody` | 实时预测请求 | `project_name`, `interval`, `predictor_type` |
| `StopRealtimePredictRequestBody` | 停止预测请求 | `project_name` |
| `DecisionMakingRequestBody` | 优化决策请求 | `project_name`, `interval`, `decision_type` |
| `StopDecisionMakingRequestBody` | 停止决策请求 | `project_name` |

## 4.2. 后台任务 (`background_tasks/__init__.py`)

| 类名 | 用途 | 关键方法 |
| :--- | :--- | :--- |
| `BackgroundTask` | 封装周期性执行的后台任务 | `start()`: 启动所有任务。<br>`clean()`: 关闭调度器。<br>`set_task_enabled()`: 动态启用/禁用特定任务。 |

## 4.3. 心跳监控 (`utils/heartbeat_checker.py`)

| 类名 | 用途 | 关键方法 |
| :--- | :--- | :--- |
| `HeartbeatMonitor` | 生成心跳文件,供外部系统监控应用健康状态 | `update_heartbeat()`: 更新心跳文件内容。<br>`get_heartbeat_status()`: 读取并返回心跳状态。<br>`cleanup_heartbeat_file()`: 应用关闭时更新心跳文件状态。 |

## 4.4. 任务调度器 (`utils/task_scheduler.py`)

这不是一个类,而是一个模块,提供了四个预先配置好的 `APScheduler` 实例。

| 变量名 | 线程池大小 | 用途 |
| :--- | :--- | :--- |
| `background_scheduler` | 10 | 用于通用的后台任务,如数据共享、心跳。 |
| `train_scheduler` | 3 | 专门用于模型训练任务,限制并发数以避免资源耗尽。 |
| `predict_scheduler` | 10 | 用于实时预测任务。 |
| `decision_scheduler` | 5 | 用于优化决策任务。 |
