import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: '/api', // 通过Vite代理转发到后端
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('发送训练请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('训练请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('收到训练响应:', response.status, response.config.url)
    return response
  },
  (error) => {
    console.error('训练响应错误:', error)
    
    let message = '训练请求失败'
    
    if (error.response) {
      const { status, data } = error.response
      switch (status) {
        case 400:
          message = data.detail || '训练参数错误'
          break
        case 401:
          message = '未授权访问'
          break
        case 403:
          message = '禁止访问'
          break
        case 404:
          message = '训练接口不存在'
          break
        case 500:
          message = '训练服务器内部错误'
          break
        default:
          message = data.detail || `训练请求失败 (${status})`
      }
    } else if (error.request) {
      message = '训练服务连接失败,请检查网络设置'
    } else {
      message = error.message || '训练未知错误'
    }
    
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// 训练API方法
const trainAPI = {
  // 启动单个模型训练
  async startTrain(data) {
    try {
      const response = await api.post('/train/train', data)
      return {
        success: true,
        data: response.data,
        message: '训练启动成功'
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || error.message,
        message: '训练启动失败'
      }
    }
  },
  
  // 启动批量模型训练
  async startBatchTrain(data) {
    try {
      const response = await api.post('/train/train_list', data)
      return {
        success: true,
        data: response.data,
        message: '批量训练启动成功'
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || error.message,
        message: '批量训练启动失败'
      }
    }
  },
  
  // 停止训练
  async stopTrain(data) {
    try {
      const response = await api.post('/train/stop_train', data)
      return {
        success: true,
        data: response.data,
        message: '训练停止成功'
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || error.message,
        message: '训练停止失败'
      }
    }
  },
  
  // 停止所有训练
  async stopAllTrain() {
    try {
      const response = await api.post('/train/stop_all_train')
      return {
        success: true,
        data: response.data,
        message: '所有训练停止成功'
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || error.message,
        message: '停止所有训练失败'
      }
    }
  },
  
  // 获取训练详情
  async getTrainDetail(projectName, configId) {
    try {
      const response = await api.get(`/train/detail/${projectName}/${configId}`)
      return {
        success: true,
        data: response.data,
        message: '获取训练详情成功'
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || error.message,
        message: '获取训练详情失败'
      }
    }
  },
  
  // 获取训练日志
  async getTrainLogs(projectName, configId) {
    try {
      const response = await api.get(`/train/logs/${projectName}/${configId}`)
      return {
        success: true,
        data: response.data,
        message: '获取训练日志成功'
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || error.message,
        message: '获取训练日志失败'
      }
    }
  },
  
  // 下载训练日志
  async downloadTrainLogs(projectName, configId) {
    try {
      const response = await api.get(`/train/logs/${projectName}/${configId}/download`, {
        responseType: 'blob'
      })
      return {
        success: true,
        data: response.data,
        message: '下载训练日志成功'
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || error.message,
        message: '下载训练日志失败'
      }
    }
  }
}

export default trainAPI