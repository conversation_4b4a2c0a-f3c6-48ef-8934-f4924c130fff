#!/bin/bash
# 请使用bash 执行脚本
# ! 在industryai根目录下执行, 不要在scripts目录下执行
# ! 请事先激活配置好的python虚拟环境,默认为 conda activate exp
# sh scripts/run_train.sh

# 检查当前conda环境
current_env=$(conda info --envs | grep '*' | awk '{print $1}')
# 打印当前环境
echo "当前使用的python环境为: $current_env"
# 根据which python判断打印当前python的位置
python_path=$(which python)
echo "python路径: $python_path"

# 在项目根目录下运行
export PYTHONPATH=${PYTHONPATH:+$PYTHONPATH:}$(pwd)
echo "项目路径: $PYTHONPATH"

# # 项目名列表
# project_names=(
#     "阳泉窑电耗样本_阳泉窑电耗样本_CNN" 
#     "阳泉窑实物煤耗样本_阳泉窑实物煤耗样本_CNN" 
#     "阳泉fcao样本_阳泉fcao样本_CNN"
# )

# # 使用命令行参数运行训练
# for project_name in "${project_names[@]}"; do
#     echo "请求训练项目: $project_name"
#     python requestes/requests_train.py \
#         --project-name "$project_name" \
#         --interval 300 \
#         --algorithm-type "classic"
#     echo "请求训练项目: $project_name 完成"
# done

# 或者使用简短的参数形式:
# python requestes/requests_train.py -p "二线回转窑电耗样本_二线回转窑电耗样本_CNN" -i 30 -a "classic_alter"

# 项目配置数组,格式:项目名|间隔时间|算法类型
project_configs=(
    "阳泉窑电耗样本_阳泉窑电耗样本_CNN|300|classic"
    "阳泉窑实物煤耗样本_阳泉窑实物煤耗样本_CNN|600|classic"
    "阳泉fcao样本_阳泉fcao样本_CNN|450|classic"
)

# 构建批量处理的配置参数
config_args=()
for config in "${project_configs[@]}"; do
    config_args+=("--config" "$config")
done

echo "开始批量训练处理..."
echo "配置项目数量: ${#project_configs[@]}"

# 使用批量处理接口一次性处理所有项目
python src/backend/requestes/requests_train.py batch "${config_args[@]}"

echo "批量训练处理完成"