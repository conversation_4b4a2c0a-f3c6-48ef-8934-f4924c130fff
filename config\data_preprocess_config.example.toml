# 数据预处理变量范围配置文件
# 用于定义各种变量的过滤范围
# 不同工厂可以根据实际情况修改这些范围值

[variable_ranges]
# ! 默认变量都写在这个下面
# 比表面积范围(当列名包含"bbmj"时使用)
bbmj = [250.0, 500.0]

# 细度范围(当列名包含"xd"时使用)
xd = [0.0, 30.0]

# 游离钙范围(当列名包含"f_cao"时使用)
f_cao = [0.0, 5.0]
# 备选范围(注释形式保留,可根据实际情况启用)
# f_cao = [0.0, 2.5]
# f_cao = [0.0, 1.8]

# 煤耗范围(当列名包含"煤耗"时使用)
"煤耗" = [150, 160.0]

# 电耗相关配置
# 电耗变量支持根据列名中的关键词使用不同的范围
[variable_ranges."电耗"]
# 默认电耗范围(当列名只包含"电耗"时使用)
default = [22, 30]

# 水泥磨电耗范围(当列名同时包含"水泥"和"磨"时使用)
"水泥_and_磨" = [0.1, 50.0]

# 窑或熟料电耗范围(当列名包含"窑"或"熟料"时使用)
"窑_or_熟料" = [22, 30]

# 生料或原料电耗范围(当列名包含"生料"或"原料"时使用)
"生料_or_原料" = [0.1, 50.0]

# 煤磨电耗范围(当列名包含"煤磨"时使用)
"煤磨" = [0.1, 60.0]

# 其他变量的范围配置
# 格式:变量关键词 = [最小值, 最大值]





# 工厂自定义变量范围示例
# 可以根据具体工厂的实际情况添加更多变量
# 例如:
# "窑喂料反馈" = [500.0, 1000.0]


# 配置说明:
# 1. 所有范围值都是 [最小值, 最大值] 的格式
# 2. 电耗变量会根据列名中的关键词自动选择合适的范围
# 3. 其他变量通过列名中是否包含对应的关键词来匹配范围
# 4. 如果列名不匹配任何已配置的变量,将使用默认范围 [-999999, 999999]
# 5. 不同工厂可以复制此文件并修改范围值以适应各自的生产特点
# 6. 中文变量名需要用双引号包围以确保正确解析