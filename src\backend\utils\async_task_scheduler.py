from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.executors.asyncio import AsyncIOExecutor
from apscheduler.jobstores.memory import MemoryJobStore


# 配置作业默认值
job_defaults = {
    'coalesce': True,  # 堆积的任务只运行一次
    'max_instances': 10,  # 同一个任务同时可以有10个实例运行
    'misfire_grace_time': 60  # 任务错过执行时间的容忍度(秒)
}

# 创建通用异步调度器,适用于普通异步任务
background_jobstore = MemoryJobStore()
background_executor = AsyncIOExecutor()
background_scheduler = AsyncIOScheduler(
    jobstores={'default': background_jobstore},
    executors={'default': background_executor},
    job_defaults=job_defaults
)

# 创建训练异步调度器,同时只允许2个训练任务
train_jobstore = MemoryJobStore()
train_executor = AsyncIOExecutor()
train_scheduler = AsyncIOScheduler(
    jobstores={'default': train_jobstore},
    executors={'default': train_executor},
    job_defaults={**job_defaults, 'max_instances': 2}  # 单独限制训练任务并发
)

# 创建预测异步调度器,同时只允许运行10个预测任务
predict_jobstore = MemoryJobStore()
predict_executor = AsyncIOExecutor()
predict_scheduler = AsyncIOScheduler(
    jobstores={'default': predict_jobstore},
    executors={'default': predict_executor},
    job_defaults={**job_defaults, 'max_instances': 10}
)

# 创建决策异步调度器,同时只允许3个决策任务
decision_jobstore = MemoryJobStore()
decision_executor = AsyncIOExecutor()
decision_scheduler = AsyncIOScheduler(
    jobstores={'default': decision_jobstore},
    executors={'default': decision_executor},
    job_defaults={**job_defaults, 'max_instances': 3}
)


def start_all_schedulers():
    """
    启动所有异步调度器。
    建议在FastAPI应用启动事件或主事件循环中调用。
    """
    background_scheduler.start()
    train_scheduler.start()
    predict_scheduler.start()
    decision_scheduler.start()

# 示例：如何添加异步任务
# async def example_async_job():
#     print("异步任务执行")
#
# background_scheduler.add_job(example_async_job, 'interval', seconds=10)

# 注意：
# - 调度器的启动应在事件循环已启动后进行(如FastAPI的startup事件中)。
# - 所有被调度的任务函数必须为async def定义。
