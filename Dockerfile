# 使用官方Python镜像作为基础镜像
FROM python:3.11-slim

# 添加元数据
LABEL version="2025.06.preview" \
    description="工业算法库后端" \
    maintainer="bahay<PERSON><EMAIL>"

# 设置环境变量记录版本
ENV APP_VERSION="2025.06.preview"

# 安装构建依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    unixodbc \
    unixodbc-dev \
    gcc \
    vim \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*
    
# 设置工作目录
WORKDIR /

# 复制依赖相关文件
COPY requirements.txt .
COPY libs/ ./libs/

# 安装依赖
RUN pip install -r scripts/requirements.txt --no-cache-dir --index-url https://mirrors.aliyun.com/pypi/simple/

# 暴露端口
EXPOSE 8000

# 复制其余项目文件
COPY . .

# 启动命令
CMD ["python", "src/backend/main.py", "--host", "0.0.0.0", "--port", "8999"]
