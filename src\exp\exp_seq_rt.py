import os
import sys
import time
from industrytslib.core import get_realtime_predictor
from industrytslib.utils.readconfig import read_config_toml


# Add both current directory and parent directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.extend([current_dir, parent_dir])


# seq_pred_list =[
#     "生料A磨孪生样本_生料A磨孪生样本_WaveForM",
#     "生料B磨孪生样本_生料B磨孪生样本_WaveForM",
#     "窑孪生样本_窑孪生样本_WaveForM",
#     "煤磨孪生样本_煤磨孪生样本_WaveForM",
#     "水泥A磨孪生样本_水泥A磨孪生样本_WaveForM",
#     "水泥B磨孪生样本_水泥B磨孪生样本_WaveForM",
# ]
seq_pred_list = [
    "三友窑孪生样本_三友窑孪生样本_WaveForM",
    "原料A磨孪生样本_原料A磨孪生样本_WaveForM",
    "原料B磨孪生样本_原料B磨孪生样本_WaveForM",
    "水泥A磨孪生样本_水泥A磨孪生样本_WaveForM",
    "水泥磨二线孪生样本_水泥磨二线孪生样本_WaveForM",
    "煤磨孪生样本_煤磨孪生样本_WaveForM",
]

config = read_config_toml("config/database_config.toml")

def main_sync():
    predictor_list = []
    for project_name in seq_pred_list:
        predictor = get_realtime_predictor(project_name, config, "time_series")
        print(f"task {project_name} create successfully!")
        predictor_list.append(predictor)

    while True:
        for predictor in predictor_list:
            try:
                predictor.main()
            except Exception as e:
                print(e)
        time.sleep(60)


if __name__ == "__main__":
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    main_sync()
