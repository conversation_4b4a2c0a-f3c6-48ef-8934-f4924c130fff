# 实时预测模块 API (`/rt`)

## 概述

实时预测模块提供了工业过程的实时预测功能,支持多种预测类型和算法。该模块基于后台调度器,可以创建周期性执行的预测任务。所有与实时预测相关的API都以 `/rt` 为路径前缀。

## 接口列表

### 1. 实时预测模块首页

-   **接口地址**: `GET /rt/`
-   **功能描述**: 返回实时预测模块的欢迎信息。
-   **请求参数**: 无
-   **响应示例**:
    ```json
    {
      "message": "realtime predict information page"
    }
    ```

---

### 2. 启动通用实时预测任务

-   **接口地址**: `POST /rt/realtime_predict`
-   **功能描述**: 启动一个通用的、周期性执行的实时预测任务。
-   **注意**: 如果已存在同名项目 (`project_name`) 的任务,旧任务将被移除,新任务将替换它。

#### 请求体

```json
{
  "project_name": "string",
  "interval": "integer",
  "predictor_type": "string"
}
```

| 参数名           | 类型    | 必填 | 描述                                       |
| ---------------- | ------- | ---- | ------------------------------------------ |
| `project_name`   | string  | 是   | 项目的唯一名称,也用作后台任务的 `job_id`。 |
| `interval`       | integer | 是   | 预测任务的执行间隔(秒)。                 |
| `predictor_type` | string  | 是   | 指定使用的预测器类型。                     |

#### `predictor_type` 可选值

```
"basic", "classic", "classic_mo", "classic_standard", "sequence", "quality", 
"sequence_soft", "sequence_mo_soft", "time_series"
```

#### 响应示例

```json
{
  "code": 200,
  "message": "实时预测<project_name>请求成功!!!"
}
```

---

### 3. 停止通用实时预测任务

-   **接口地址**: `POST /rt/stop_realtime_predict`
-   **功能描述**: 停止一个指定的通用实时预测任务。

#### 请求体

```json
{
  "project_name": "string"
}
```

| 参数名         | 类型   | 必填 | 描述             |
| -------------- | ------ | ---- | ---------------- |
| `project_name` | string | 是   | 要停止的项目名称。 |

#### 响应示例

```json
{
  "code": 200,
  "message": "停止实时预测请求成功!!!"
}
```

---

### 4. 启动与停止特定的预测任务

为了提供更可靠的启停控制,系统额外提供了针对特定预测类型的接口。这些接口的 `job_id` 具有唯一前缀,可以被对应的 `stop` 接口精确停止。

#### 4.1 序列实时预测 (Sequence Realtime Predict)

-   **启动接口**: `POST /rt/sequence_realtime_predict`
-   **停止接口**: `POST /rt/stop_sequence_predict`
-   **行为**:
    -   在后台创建一个 `job_id` 为 `f"sequence_predict_{project_name}"` 的任务。
    -   该任务内部固定使用 `"sequence"` 类型的预测器,会**忽略**请求体中传入的 `predictor_type` 值。

#### 4.2 序列软测量 (Sequence Soft Sensor)

-   **启动接口**: `POST /rt/sequence_soft_sensor`
-   **停止接口**: `POST /rt/stop_sequence_soft`
-   **行为**:
    -   在后台创建一个 `job_id` 为 `f"sequence_soft_{project_name}"` 的任务。
    -   该任务内部固定使用 `"sequence_soft"` 类型的预测器,会**忽略**请求体中传入的 `predictor_type` 值。

#### 请求体 (适用于所有启动接口)

```json
{
  "project_name": "string",
  "interval": "integer",
  "predictor_type": "string" // 此参数在特定预测接口中会被忽略
}
```

#### 请求体 (适用于所有停止接口)

```json
{
  "project_name": "string"
}
```

---

### 5. 停止所有实时预测任务

-   **接口地址**: `POST /rt/stop_realtime_predict_all`
-   **功能描述**: 停止调度器中所有 `job_id` 包含 `realtime_predict_`, `sequence_predict_` 或 `sequence_soft_` 前缀的任务。
-   **请求参数**: 无
-   **响应示例**:
    ```json
    {
      "code": 200,
      "message": "停止所有实时预测任务!!!"
    }
    ```

---

### 6. 查询正在运行的预测任务

-   **接口地址**: `GET /rt/get_realtime_predict_jobs`
-   **功能描述**: 查询当前所有正在运行的预测任务。
-   **请求参数**: 无
-   **重要说明**: 当前该接口只能查询到带有特定前缀的任务(`sequence_predict_`、`sequence_soft_`),无法查询到通过 `/rt/realtime_predict` 启动的通用实时预测任务(因为它们使用项目名称作为`job_id`)。
-   **响应示例**:
    ```json
    {
        "code": 200,
        "message": "查询成功",
        "data": [
            {
                "id": "sequence_predict_temp_forecast",
                "project_name": "temp_forecast",
                "task_type": "序列预测",
                "next_run_time": "2024-07-30T15:30:00.123+08:00",
                "last_run_time": "2024-07-30T15:29:00.123+08:00"
            }
        ]
    }
    ```

## Python 调用示例 (序列预测)

此示例演示了如何使用可以被正常停止的 `sequence_realtime_predict` 接口。

```python
import requests
import time
import json

BASE_URL = "http://localhost:8081"
HEADERS = {"Content-Type": "application/json"}

def start_seq_predict(project_name: str, interval: int):
    """启动序列预测任务"""
    print(f"\n--- 启动项目 '{project_name}' 的序列预测 ---")
    payload = {
        "project_name": project_name,
        "interval": interval,
        "predictor_type": "ignored_but_required" 
    }
    try:
        response = requests.post(f"{BASE_URL}/rt/sequence_realtime_predict", json=payload, headers=HEADERS)
        response.raise_for_status()
        print("响应:", response.json())
        return True
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return False

def stop_seq_predict(project_name: str):
    """停止序列预测任务"""
    print(f"\n--- 停止项目 '{project_name}' 的序列预测 ---")
    payload = {"project_name": project_name}
    try:
        response = requests.post(f"{BASE_URL}/rt/stop_sequence_predict", json=payload, headers=HEADERS)
        response.raise_for_status()
        print("响应:", response.json())
        return True
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return False

def query_predict_jobs():
    """查询所有运行中的预测任务"""
    print("\n--- 查询所有预测任务 ---")
    try:
        response = requests.get(f"{BASE_URL}/rt/get_realtime_predict_jobs", headers=HEADERS)
        response.raise_for_status()
        tasks = response.json()
        print(json.dumps(tasks, indent=2, ensure_ascii=False))
        return tasks.get('data', [])
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return []

if __name__ == "__main__":
    PROJECT_NAME = "temp_forecast_project"
    
    # 1. 查询初始任务状态
    query_predict_jobs()
    
    # 2. 启动序列预测任务
    start_seq_predict(PROJECT_NAME, interval=30)
    
    # 3. 等待片刻,让任务出现在调度器中
    print("\n等待5秒...")
    time.sleep(5)
    
    # 4. 再次查询,确认任务已启动
    query_predict_jobs()
    
    # 5. 停止该任务
    stop_seq_predict(PROJECT_NAME)
    
    # 6. 再次查询,确认任务已被停止
    print("\n等待2秒后再次查询...")
    time.sleep(2)
    query_predict_jobs()

```

---

## Python 调用示例 (通用实时预测)

此示例演示了如何使用通用实时预测接口,现在可以正常停止任务。

```python
import requests
import time
import json

BASE_URL = "http://localhost:8081"
HEADERS = {"Content-Type": "application/json"}

def start_realtime_predict(project_name: str, interval: int, predictor_type: str):
    """启动通用实时预测任务"""
    print(f"\n--- 启动项目 '{project_name}' 的实时预测 ---")
    payload = {
        "project_name": project_name,
        "interval": interval,
        "predictor_type": predictor_type
    }
    try:
        response = requests.post(f"{BASE_URL}/rt/realtime_predict", json=payload, headers=HEADERS)
        response.raise_for_status()
        print("响应:", response.json())
        return True
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return False

def stop_realtime_predict(project_name: str):
    """停止通用实时预测任务"""
    print(f"\n--- 停止项目 '{project_name}' 的实时预测 ---")
    payload = {"project_name": project_name}
    try:
        response = requests.post(f"{BASE_URL}/rt/stop_realtime_predict", json=payload, headers=HEADERS)
        response.raise_for_status()
        print("响应:", response.json())
        return True
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return False

def query_predict_jobs():
    """查询所有运行中的预测任务"""
    print("\n--- 查询所有预测任务 ---")
    try:
        response = requests.get(f"{BASE_URL}/rt/get_realtime_predict_jobs", headers=HEADERS)
        response.raise_for_status()
        tasks = response.json()
        print(json.dumps(tasks, indent=2, ensure_ascii=False))
        return tasks.get('data', [])
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return []

if __name__ == "__main__":
    PROJECT_NAME = "quality_prediction_project"
    
    # 1. 查询初始任务状态
    query_predict_jobs()
    
    # 2. 启动通用实时预测任务
    start_realtime_predict(PROJECT_NAME, interval=30, predictor_type="classic")
    
    # 3. 等待片刻,让任务出现在调度器中
    print("\n等待5秒...")
    time.sleep(5)
    
    # 4. 再次查询,确认任务已启动
    query_predict_jobs()
    
    # 5. 停止该任务
    stop_realtime_predict(PROJECT_NAME)
    
    # 6. 再次查询,确认任务已被停止
    print("\n等待2秒后再次查询...")
    time.sleep(2)
    query_predict_jobs()

```

---
