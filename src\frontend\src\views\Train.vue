<template>
  <div class="train">
    <div class="page-title">模型训练</div>
    
    <!-- 算法配置管理 -->
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <span>算法配置管理</span>
          <el-button type="primary" size="small" @click="createConfig">
            创建配置
          </el-button>
        </div>
      </template>
      
      <el-table :data="algorithmConfigs" v-loading="loading.configs">
        <el-table-column prop="name" label="配置名称" />
        <el-table-column prop="algorithm_type" label="算法类型">
          <template #default="{ row }">
            {{ getAlgorithmTypeText(row.algorithm_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="created_at" label="创建时间">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button size="small" @click="editConfig(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteConfig(row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="mt-4">
        <el-button @click="loadAlgorithmConfigs">刷新</el-button>
      </div>
    </el-card>

    <!-- 样本数据管理 -->
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <span>样本数据管理</span>
          <el-button type="primary" size="small" @click="createSample">
            添加样本
          </el-button>
        </div>
      </template>
      
      <el-table :data="sampleData" v-loading="loading.samples">
        <el-table-column prop="name" label="样本名称" />
        <el-table-column prop="project_name" label="项目名称" />
        <el-table-column prop="sample_count" label="样本数量" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="created_at" label="创建时间">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button size="small" type="danger" @click="deleteSample(row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="mt-4">
        <el-button @click="loadSampleData">刷新</el-button>
      </div>
    </el-card>

    <!-- 算法配置对话框 -->
    <ConfigDialog
      v-model:visible="showConfigDialog"
      :config="configForm"
      :is-editing="isEditingConfig"
      @save="saveConfig"
    />

    <!-- 样本数据对话框 -->
    <SampleDialog
      v-model:visible="showSampleDialog"
      @save="saveSample"
    />

    <!-- 训练表单 -->
    <div class="card">
      <h3>启动训练任务</h3>
      <el-form 
        ref="trainFormRef" 
        :model="trainForm" 
        :rules="trainRules" 
        label-width="120px" 
        class="form-container"
      >
        <el-form-item label="项目名称" prop="project_name">
          <el-input 
            v-model="trainForm.project_name" 
            placeholder="请输入项目名称"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="算法配置" prop="config_id">
          <el-select v-model="trainForm.config_id" placeholder="请选择算法配置" style="width: 100%" @change="onConfigChange">
            <el-option 
              v-for="config in algorithmConfigs" 
              :key="config.id" 
              :label="config.name" 
              :value="config.id"
            >
              <span>{{ config.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ getAlgorithmTypeText(config.algorithm_type) }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="样本数据" prop="sample_id">
          <el-select v-model="trainForm.sample_id" placeholder="请选择样本数据" style="width: 100%">
            <el-option 
              v-for="sample in filteredSampleData" 
              :key="sample.id" 
              :label="sample.name" 
              :value="sample.id"
            >
              <span>{{ sample.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ sample.sample_count }}条</span>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="训练间隔" prop="interval">
          <el-input-number 
            v-model="trainForm.interval" 
            :min="1" 
            :max="3600" 
            placeholder="秒"
            style="width: 100%"
          />
          <div class="form-help">训练任务执行间隔时间(秒)</div>
        </el-form-item>
        
        <div class="button-group">
          <el-button type="primary" @click="startTrain" :loading="loading.train">
            <el-icon><VideoPlay /></el-icon>
            启动训练
          </el-button>
          <el-button @click="resetForm">
            <el-icon><RefreshRight /></el-icon>
            重置表单
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 批量训练 -->
    <div class="card">
      <h3>批量训练</h3>
      <el-form 
        ref="batchFormRef" 
        :model="batchForm" 
        label-width="120px" 
        class="form-container"
      >
        <el-form-item label="项目列表">
          <el-input 
            v-model="batchProjectsInput" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入项目名称,每行一个"
          />
        </el-form-item>
        
        <el-form-item label="训练间隔">
          <el-input-number 
            v-model="batchForm.interval" 
            :min="1" 
            :max="3600" 
            placeholder="秒"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="算法类型">
          <el-select v-model="batchForm.algorithm_type" placeholder="请选择算法类型" style="width: 100%">
            <el-option label="经典算法" value="classic" />
            <el-option label="经典算法(替代)" value="classic_alter" />
            <el-option label="序列算法" value="sequence" />
            <el-option label="时间序列经典" value="time_series_classic" />
            <el-option label="时间序列序列" value="time_series_sequence" />
          </el-select>
        </el-form-item>
        
        <div class="button-group">
          <el-button type="primary" @click="startBatchTrain" :loading="loading.batchTrain">
            <el-icon><VideoPlay /></el-icon>
            启动批量训练
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 训练控制 -->
    <div class="card">
      <h3>训练控制</h3>
      <el-form label-width="120px" class="form-container">
        <el-form-item label="停止指定训练">
          <div style="display: flex; gap: 10px; align-items: center;">
            <el-input 
              v-model="stopProjectName" 
              placeholder="请输入要停止的项目名称"
              style="flex: 1;"
            />
            <el-button type="warning" @click="stopTrain" :loading="loading.stopTrain">
              <el-icon><VideoPause /></el-icon>
              停止训练
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="停止所有训练">
          <el-button type="danger" @click="stopAllTrain" :loading="loading.stopAllTrain">
            <el-icon><VideoStop /></el-icon>
            停止所有训练
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 训练记录 -->
    <div class="card">
      <h3>训练记录</h3>
      <div class="toolbar">
        <el-button @click="loadTrainRecords" :loading="loading.records">
          <el-icon><Refresh /></el-icon>
          刷新记录
        </el-button>
        <el-button @click="clearTrainRecords" type="danger" plain>
          <el-icon><Delete /></el-icon>
          清空记录
        </el-button>
      </div>
      
      <el-table :data="trainRecords" style="width: 100%" v-loading="loading.records">
        <el-table-column prop="timestamp" label="时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="项目名称" width="150" />
        <el-table-column prop="config_name" label="算法配置" width="150" />
        <el-table-column prop="sample_name" label="样本数据" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
              {{ scope.row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="描述" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button 
              v-if="scope.row.status === 'success' && scope.row.config_id" 
              size="small" 
              type="primary" 
              @click="viewTrainDetail(scope.row)"
            >
              <el-icon><View /></el-icon>
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, computed, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import trainAPI from '../api/train'
  import db from '../utils/database'
  import ConfigDialog from '../components/ConfigDialog.vue'
  import SampleDialog from '../components/SampleDialog.vue'

const router = useRouter()
const trainFormRef = ref()
const batchFormRef = ref()

const loading = reactive({
  train: false,
  batchTrain: false,
  stopTrain: false,
  stopAllTrain: false,
  records: false,
  configs: false,
  samples: false
})

const trainForm = reactive({
  project_name: '',
  config_id: '',
  sample_id: '',
  interval: 60
})

const batchForm = reactive({
  interval: 60,
  algorithm_type: ''
})

const batchProjectsInput = ref('')
const stopProjectName = ref('')
const trainRecords = ref([])

// 算法配置相关
const algorithmConfigs = ref([])
const showConfigDialog = ref(false)
const configForm = reactive({
  id: null,
  name: '',
  algorithm_type: '',
  description: '',
  parameters: {}
})
const isEditingConfig = ref(false)

// 样本数据相关
const sampleData = ref([])
const showSampleDialog = ref(false)
const sampleForm = reactive({
  name: '',
  project_name: '',
  data_path: '',
  sample_count: 0,
  description: ''
})

// 表单验证规则
const trainRules = {
  project_name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' }
  ],
  config_id: [
    { required: true, message: '请选择算法配置', trigger: 'change' }
  ],
  sample_id: [
    { required: true, message: '请选择样本数据', trigger: 'change' }
  ],
  interval: [
    { required: true, message: '请输入训练间隔', trigger: 'blur' },
    { type: 'number', min: 1, max: 3600, message: '间隔时间应在1-3600秒之间', trigger: 'blur' }
  ]
}

// 计算批量项目列表
const batchProjects = computed(() => {
  return batchProjectsInput.value
    .split('\n')
    .map(name => name.trim())
    .filter(name => name.length > 0)
})

// 根据项目名称过滤样本数据
const filteredSampleData = computed(() => {
  if (!trainForm.project_name) return sampleData.value
  return sampleData.value.filter(sample => 
    sample.project_name === trainForm.project_name
  )
})

// 获取算法类型文本
const getAlgorithmTypeText = (type) => {
  const typeMap = {
    'classic': '经典算法',
    'classic_alter': '经典算法(替代)',
    'sequence': '序列算法',
    'time_series_classic': '时间序列经典',
    'time_series_sequence': '时间序列序列',
    'lstm_kan': 'LSTM-KAN',
    'transformer': 'Transformer',
    'informer': 'Informer',
    'timesnet': 'TimesNet',
    'patchtst': 'PatchTST'
  }
  return typeMap[type] || type
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 启动单个训练
const startTrain = async () => {
  try {
    await trainFormRef.value.validate()
    loading.train = true
    
    // 获取选中的配置和样本信息
    const selectedConfig = algorithmConfigs.value.find(c => c.id === trainForm.config_id)
    const selectedSample = sampleData.value.find(s => s.id === trainForm.sample_id)
    
    const response = await trainAPI.startTrain(trainForm)
    
    // 记录到本地数据库
    await db.addTrainRecord({
      projectName: trainForm.project_name,
      config_name: selectedConfig?.name || '',
      sample_name: selectedSample?.name || '',
      config_id: trainForm.config_id,
      sample_id: trainForm.sample_id,
      interval: trainForm.interval,
      status: 'success',
      message: '训练任务启动成功'
    })
    
    ElMessage.success('训练任务启动成功')
    await loadTrainRecords()
    
  } catch (error) {
    console.error('启动训练失败:', error)
    
    // 记录失败信息
    await db.addTrainRecord({
      projectName: trainForm.project_name,
      config_name: '',
      sample_name: '',
      config_id: trainForm.config_id,
      sample_id: trainForm.sample_id,
      interval: trainForm.interval,
      status: 'failed',
      message: `训练任务启动失败: ${error.message}`
    })
    
  } finally {
    loading.train = false
  }
}

// 启动批量训练
const startBatchTrain = async () => {
  if (batchProjects.value.length === 0) {
    ElMessage.warning('请输入至少一个项目名称')
    return
  }
  
  if (!batchForm.algorithm_type) {
    ElMessage.warning('请选择算法类型')
    return
  }
  
  try {
    loading.batchTrain = true
    
    const requestData = {
      project_name_list: batchProjects.value,
      interval_list: batchProjects.value.map(() => batchForm.interval),
      algorithm_type_list: batchForm.algorithm_type
    }
    
    const response = await trainAPI.startBatchTrain(requestData)
    
    // 记录到本地数据库
    for (const projectName of batchProjects.value) {
      await db.addTrainRecord({
        projectName,
        algorithm_type: batchForm.algorithm_type,
        interval: batchForm.interval,
        status: 'success',
        message: '批量训练任务启动成功'
      })
    }
    
    ElMessage.success(`批量训练任务启动成功,共${batchProjects.value.length}个项目`)
    await loadTrainRecords()
    
  } catch (error) {
    console.error('启动批量训练失败:', error)
  } finally {
    loading.batchTrain = false
  }
}

// 停止指定训练
const stopTrain = async () => {
  if (!stopProjectName.value.trim()) {
    ElMessage.warning('请输入要停止的项目名称')
    return
  }
  
  try {
    loading.stopTrain = true
    
    const response = await trainAPI.stopTrain({
      project_name: stopProjectName.value.trim()
    })
    
    await db.addTrainRecord({
      projectName: stopProjectName.value.trim(),
      status: 'success',
      message: '训练任务停止成功'
    })
    
    ElMessage.success('训练任务停止成功')
    stopProjectName.value = ''
    await loadTrainRecords()
    
  } catch (error) {
    console.error('停止训练失败:', error)
  } finally {
    loading.stopTrain = false
  }
}

// 停止所有训练
const stopAllTrain = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要停止所有训练任务吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.stopAllTrain = true
    
    const response = await trainAPI.stopAllTrain()
    
    await db.addTrainRecord({
      projectName: 'ALL',
      status: 'success',
      message: '所有训练任务停止成功'
    })
    
    ElMessage.success('所有训练任务停止成功')
    await loadTrainRecords()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止所有训练失败:', error)
    }
  } finally {
    loading.stopAllTrain = false
  }
}

// 重置表单
const resetForm = () => {
  trainForm.project_name = ''
  trainForm.config_id = ''
  trainForm.sample_id = ''
  trainForm.interval = 60
  trainFormRef.value?.clearValidate()
}

// 加载训练记录
const loadTrainRecords = async () => {
  try {
    loading.records = true
    const records = await db.getTrainRecords(50)
    trainRecords.value = records
  } catch (error) {
    console.error('加载训练记录失败:', error)
    ElMessage.error('加载训练记录失败')
  } finally {
    loading.records = false
  }
}

// 加载算法配置
const loadAlgorithmConfigs = async () => {
  try {
    loading.configs = true
    const configs = await db.getAlgorithmConfigs()
    algorithmConfigs.value = configs
  } catch (error) {
    console.error('加载算法配置失败:', error)
    ElMessage.error('加载算法配置失败')
  } finally {
    loading.configs = false
  }
}

// 加载样本数据
const loadSampleData = async () => {
  try {
    loading.samples = true
    const samples = await db.getSampleData()
    sampleData.value = samples
  } catch (error) {
    console.error('加载样本数据失败:', error)
    ElMessage.error('加载样本数据失败')
  } finally {
    loading.samples = false
  }
}

// 创建算法配置
const createConfig = () => {
  configForm.id = null
  configForm.name = ''
  configForm.algorithm_type = ''
  configForm.description = ''
  configForm.parameters = {}
  isEditingConfig.value = false
  showConfigDialog.value = true
}

// 编辑算法配置
const editConfig = (config) => {
  configForm.id = config.id
  configForm.name = config.name
  configForm.algorithm_type = config.algorithm_type
  configForm.description = config.description
  configForm.parameters = { ...config.parameters }
  isEditingConfig.value = true
  showConfigDialog.value = true
}

// 保存算法配置
const saveConfig = async (configData) => {
  try {
    if (isEditingConfig.value && configData.id) {
      await db.updateAlgorithmConfig(configData.id, {
        name: configData.name,
        algorithm_type: configData.algorithm_type,
        description: configData.description,
        parameters: configData.parameters
      })
      ElMessage.success('配置更新成功')
    } else {
      await db.addAlgorithmConfig({
        name: configData.name,
        algorithm_type: configData.algorithm_type,
        description: configData.description,
        parameters: configData.parameters
      })
      ElMessage.success('配置创建成功')
    }
    showConfigDialog.value = false
    await loadAlgorithmConfigs()
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  }
}

// 删除算法配置
const deleteConfig = async (configId) => {
  try {
    await ElMessageBox.confirm('确定要删除这个配置吗？', '确认删除', {
      type: 'warning'
    })
    await db.deleteAlgorithmConfig(configId)
    ElMessage.success('配置删除成功')
    await loadAlgorithmConfigs()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除配置失败:', error)
      ElMessage.error('删除配置失败')
    }
  }
}

// 创建样本数据
const createSample = () => {
  sampleForm.name = ''
  sampleForm.project_name = ''
  sampleForm.data_path = ''
  sampleForm.sample_count = 0
  sampleForm.description = ''
  showSampleDialog.value = true
}

// 保存样本数据
const saveSample = async (sampleData) => {
  try {
    await db.addSampleData({
      name: sampleData.name,
      project_name: sampleData.project_name,
      data_path: sampleData.data_path,
      sample_count: sampleData.sample_count,
      description: sampleData.description
    })
    ElMessage.success('样本数据添加成功')
    showSampleDialog.value = false
    await loadSampleData()
  } catch (error) {
    console.error('保存样本数据失败:', error)
    ElMessage.error('保存样本数据失败')
  }
}

// 删除样本数据
const deleteSample = async (sampleId) => {
  try {
    await ElMessageBox.confirm('确定要删除这个样本数据吗？', '确认删除', {
      type: 'warning'
    })
    await db.deleteSampleData(sampleId)
    ElMessage.success('样本数据删除成功')
    await loadSampleData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除样本数据失败:', error)
      ElMessage.error('删除样本数据失败')
    }
  }
}

// 配置变更处理
const onConfigChange = () => {
  // 当配置变更时,可以在这里添加相关逻辑
}

// 查看训练详情
const viewTrainDetail = (record) => {
  router.push({
    name: 'TrainDetail',
    params: {
      projectName: record.project_name,
      configId: record.config_id
    }
  })
}

// 清空训练记录
const clearTrainRecords = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有训练记录吗？此操作不可恢复。',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await db.clearStore('trainRecords')
    await loadTrainRecords()
    ElMessage.success('训练记录已清空')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空训练记录失败:', error)
    }
  }
}

onMounted(async () => {
  await loadTrainRecords()
  await loadAlgorithmConfigs()
  await loadSampleData()
})
</script>

<style scoped>
.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>