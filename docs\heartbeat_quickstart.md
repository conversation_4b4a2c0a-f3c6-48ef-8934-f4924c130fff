# 心跳检测系统快速使用指南

## 系统架构

心跳检测系统采用**分离式监控模式**:

- **Python应用**:负责生成心跳文件,执行系统检测,提供API接口
- **Rust程序**:独立进程,专门监控心跳文件状态,处理故障和告警
- **解耦设计**:两个组件职责明确,独立运行,互不影响启动顺序

## 快速开始

### 1. 编译Rust程序

```bash
cd heartbeat_checker
cargo build --release
```

### 2. 配置环境变量

复制并编辑配置文件:
```bash
cp .env.example .env
```

关键配置项:
```bash
# 心跳检测配置
HEARTBEAT_ENABLED=True
HEARTBEAT_RUST_PATH=./heartbeat_checker/target/release/heartbeat_checker
HEARTBEAT_FILE=./heartbeat.json
HEARTBEAT_CHECK_POINTS=database,network,file_system
HEARTBEAT_UPDATE_INTERVAL=10
HEARTBEAT_TIMEOUT=30
```

### 3. 启动Python应用

```bash
# 安装依赖
pip install -r requirements.txt

# 启动应用(会自动开始生成心跳文件)
python app.py
```

### 4. 启动Rust监控程序

### 方式一:使用启动脚本(推荐)

```bash
# 使用启动脚本(会自动加载环境变量)
./scripts/start_heartbeat_monitor.sh
```

### 方式二:直接运行

```bash
# 直接运行(使用默认配置)
./heartbeat_checker/target/release/heartbeat_checker heartbeat.json 30 5

# 或使用环境变量配置
export HEARTBEAT_FILE=heartbeat.json
export HEARTBEAT_TIMEOUT=30
export HEARTBEAT_CHECK_INTERVAL=5
export HEARTBEAT_LOG_FILE=/tmp/heartbeat_monitor.log
export RUST_LOG=info
./heartbeat_checker/target/release/heartbeat_checker
```

### 查看帮助信息

```bash
./heartbeat_checker/target/release/heartbeat_checker --help
```

### 其他启动方式

```bash
# 启动心跳监控(后台运行)
./scripts/start_heartbeat_daemon.sh

# 或者前台运行(用于调试)
./scripts/start_heartbeat.sh
```

### 5. 验证运行状态

```bash
# 查看心跳监控状态
./scripts/status_heartbeat.sh

# 通过API检查
curl http://localhost:8000/health
curl http://localhost:8000/heartbeat/status

# 查看心跳文件内容
cat heartbeat.json
```

## 管理命令

### 启动相关

```bash
# 后台启动心跳检测程序
./scripts/start_heartbeat_daemon.sh

# 前台启动(用于调试,按Ctrl+C停止)
./scripts/start_heartbeat.sh
```

### 状态查看

```bash
# 查看详细状态
./scripts/status_heartbeat.sh

# 查看实时日志
tail -f logs/heartbeat.log

# 查看心跳文件内容
cat heartbeat.json
```

### 停止相关

```bash
# 停止心跳检测程序
./scripts/stop_heartbeat.sh

# 重启心跳检测程序
./scripts/stop_heartbeat.sh && ./scripts/start_heartbeat_daemon.sh
```

## 配置说明

### 环境变量配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `HEARTBEAT_ENABLED` | 是否启用心跳检测 | `True` |
| `HEARTBEAT_RUST_PATH` | Rust程序路径 | `./heartbeat_checker/target/release/heartbeat_checker` |
| `HEARTBEAT_FILE` | 心跳文件路径 | `./heartbeat.json` |
| `HEARTBEAT_CHECK_POINTS` | 检查点列表(逗号分隔) | `database,network,file_system` |
| `HEARTBEAT_UPDATE_INTERVAL` | Python应用更新心跳文件间隔(秒) | `10` |
| `HEARTBEAT_TIMEOUT` | Rust程序检测超时阈值(秒) | `30` |

### 检查点类型

- `database`: 数据库连接检查
- `network`: 网络连接检查
- `file_system`: 文件系统检查

#### Python应用配置(.env文件):

- `HEARTBEAT_CHECK_POINTS`: 检查点列表,逗号分隔
- `HEARTBEAT_UPDATE_INTERVAL`: 心跳更新间隔(秒)
- `HEARTBEAT_TIMEOUT`: 心跳超时阈值(秒,默认30)
- `HEARTBEAT_RUST_PATH`: Rust监控程序路径

#### Rust监控程序配置(.env.heartbeat_monitor文件):

- `HEARTBEAT_FILE`: 心跳文件路径(默认:heartbeat.json)
- `HEARTBEAT_TIMEOUT`: 超时阈值秒数(默认:30)
- `HEARTBEAT_CHECK_INTERVAL`: 检查间隔秒数(默认:5)
- `HEARTBEAT_MAX_FAILURES`: 最大连续失败次数(默认:3)
- `HEARTBEAT_LOG_FILE`: 日志文件路径(默认:/tmp/heartbeat_monitor.log)
- `HEARTBEAT_VERBOSE`: 详细日志模式(默认:false)
- `RUST_LOG`: 日志级别(可选:error, warn, info, debug, trace)

### 工作原理

1. **Python应用**每隔 `HEARTBEAT_UPDATE_INTERVAL` 秒更新心跳文件
2. **Rust程序**持续监控心跳文件,如果超过 `HEARTBEAT_TIMEOUT` 秒未更新则认为异常
3. **日志记录**:所有检查结果和异常情况都会记录到日志文件
4. **异常处理**:当检测到异常时,记录详细日志并可触发告警机制
5. 心跳文件包含时间戳、健康状态、应用状态和各检查点的结果

### 日志功能

- **控制台输出**:实时显示监控状态和异常信息
- **文件日志**:详细记录所有检查结果到指定日志文件
- **故障日志**:严重故障会额外记录到 `/tmp/heartbeat_failure.log`
- **日志级别**:支持不同级别的日志输出控制

## API接口

### 基础健康检查

```bash
curl http://localhost:8000/health
```

**正常响应**:
```json
{
  "status": "ok",
  "message": "系统运行正常",
  "last_check": "2024-01-01T12:00:00",
  "timestamp": "2024-01-01T12:00:00"
}
```

**异常响应** (HTTP 503):
```json
{
  "detail": {
    "status": "error",
    "message": "系统状态异常",
    "last_check": "2024-01-01T11:58:00",
    "timestamp": "2024-01-01T12:00:00"
  }
}
```

### 详细状态查询

```bash
curl http://localhost:8000/heartbeat/status
```

**响应示例**:
```json
{
  "enabled": true,
  "healthy": true,
  "last_check": "2024-01-01T12:00:00",
  "error_message": null,
  "config": {
    "check_point": "database",
    "check_interval": 30,
    "timeout_threshold": 60
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

## 常见问题

### Q: Rust程序编译失败

**A**: 确保已安装Rust:
```bash
# 安装Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# 重新编译
./scripts/build_heartbeat.sh
```

### Q: 心跳检测程序无法启动

**A**: 检查以下几点:
1. Rust程序是否编译成功
2. 是否有执行权限
3. 配置文件是否正确

```bash
# 检查状态
./scripts/status_heartbeat.sh

# 查看详细错误
tail -f logs/heartbeat.log
```

### Q: Python应用显示心跳检测异常

**A**: 检查心跳文件:
1. 文件是否存在
2. 文件是否及时更新
3. Rust程序是否正在运行

```bash
# 检查心跳文件
ls -la heartbeat.json
cat heartbeat.json

# 检查Rust程序状态
./scripts/status_heartbeat.sh
```

### Q: 如何更改检测点

**A**: 修改`.env`文件中的`HEARTBEAT_CHECK_POINT`,然后重启:
```bash
# 停止当前程序
./scripts/stop_heartbeat.sh

# 修改配置
vim .env

# 重新启动
./scripts/start_heartbeat_daemon.sh
```

## 生产环境建议

1. **使用后台模式**: `./scripts/start_heartbeat_daemon.sh`
2. **设置开机自启**: 将启动命令添加到系统服务或crontab
3. **监控日志**: 定期检查`logs/heartbeat.log`
4. **设置告警**: 监控`/health`接口,异常时发送告警
5. **备份配置**: 备份`.env`配置文件

## 系统服务配置(可选)

创建systemd服务文件:

```bash
sudo vim /etc/systemd/system/heartbeat-checker.service
```

内容:
```ini
[Unit]
Description=Industry AI Heartbeat Checker
After=network.target

[Service]
Type=simple
User=your_user
WorkingDirectory=/mnt/data/lyh/industryai
ExecStart=/mnt/data/lyh/industryai/scripts/start_heartbeat.sh
ExecStop=/mnt/data/lyh/industryai/scripts/stop_heartbeat.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务:
```bash
sudo systemctl daemon-reload
sudo systemctl enable heartbeat-checker
sudo systemctl start heartbeat-checker
```