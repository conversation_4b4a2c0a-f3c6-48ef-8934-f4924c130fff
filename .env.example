API_KEY=your_api_key_here
DATABASE_URL=postgres://user:password@localhost/dbname
DEBUG=True

# fastapi后台程序配置
host = 0.0.0.0
port = 8999

# 工厂名称
PLANT=阳泉

# 后台任务
Background_task=False
BACKGROUND_TASK=False

# 运行模式
RUN_MODE=offline

# 实时预测和优化决策数据源
# 直接从历史库读取数据还是从实时库读取数据
# history: 直接从历史库读取数据
# realtime: 从实时库读取数据
RT_DATA_SOURCE=history


# 心跳检测配置
# 是否启用心跳检测
Heartbeat_task=False

# 任务监控配置
TASK_MONITOR_INTERVAL_SECONDS = 120

# get train data从数据库如何读取数据
# 从视图还是历史表拼接转置
# view: 直接查询视图, pivot: 查询历史表并转置
DATA_FETCH_STRATEGY=view
