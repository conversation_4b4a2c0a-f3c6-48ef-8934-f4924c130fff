#!/usr/bin/env python3
"""
心跳检测模块
本模块负责:
- 心跳检测程序的管理
- 与外部Rust程序的交互
- 心跳状态的监控和报告
"""

import os
import subprocess
import json
import time
import threading
from pathlib import Path
from datetime import datetime
from typing import Dict, Optional, Any

from industrytslib import get_logger


class HeartbeatMonitor:
    """心跳监控类 - 负责生成心跳文件供Rust程序检测"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化心跳监控器
        
        参数:
            config: 心跳检测配置字典
        """
        self.config = config
        self.logger = get_logger('HeartbeatMonitor', "heartbeat", level="INFO")
        self._status = {
            'healthy': True,
            'last_update': None,
            'error_message': None
        }
        self._lock = threading.Lock()
    
    def update_heartbeat(self) -> bool:
        """
        更新心跳文件 - 由app.py定期调用
        
        返回:
            bool: 更新是否成功
        """
        try:
            # 检查各个系统组件的状态
            check_results = {}
            overall_healthy = True
            
            # 根据配置的检查点进行检查
            check_points = self.config.get('check_points', ['database', 'network', 'file_system'])
            if isinstance(check_points, str):
                check_points = [check_points]
            
            for point in check_points:
                status = self._check_point_status(point)
                check_results[point] = status
                if status != 'ok':
                    overall_healthy = False
            
            # 生成心跳数据
            heartbeat_data = {
                'timestamp': time.time(),
                'datetime': datetime.now().isoformat(),
                'healthy': overall_healthy,
                'check_results': check_results,
                'app_status': 'running',
                'version': '1.0.0'
            }
            
            # 确保心跳文件目录存在
            heartbeat_file = Path(self.config['heartbeat_file'])
            heartbeat_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入心跳文件
            with open(heartbeat_file, 'w') as f:
                json.dump(heartbeat_data, f, indent=2)
            
            # 更新内部状态
            with self._lock:
                self._status.update({
                    'healthy': overall_healthy,
                    'last_update': datetime.now(),
                    'error_message': None if overall_healthy else f"检查点状态异常: {check_results}"
                })
            
            if overall_healthy:
                self.logger.debug(f"心跳文件更新成功: {heartbeat_data}")
            else:
                self.logger.warning(f"心跳文件更新 - 检测到异常状态: {check_results}")
            
            return True
            
        except Exception as e:
            with self._lock:
                self._status.update({
                    'healthy': False,
                    'last_update': datetime.now(),
                    'error_message': f'更新心跳文件失败: {str(e)}'
                })
            self.logger.error(f"更新心跳文件失败: {e}")
            return False
    
    def get_heartbeat_status(self) -> Dict[str, Any]:
        """
        获取当前心跳状态(用于API接口)
        
        返回:
            Dict: 心跳状态字典
        """
        try:
            heartbeat_file = Path(self.config['heartbeat_file'])
            
            if not heartbeat_file.exists():
                return {
                    'healthy': False,
                    'error': '心跳文件不存在',
                    'last_update': None
                }
            
            # 读取心跳文件
            with open(heartbeat_file, 'r') as f:
                heartbeat_data = json.load(f)
            
            # 检查心跳文件是否过期
            current_time = time.time()
            last_heartbeat = heartbeat_data.get('timestamp', 0)
            timeout_threshold = self.config.get('timeout_threshold', 60)
            
            is_timeout = current_time - last_heartbeat > timeout_threshold
            
            return {
                'healthy': heartbeat_data.get('healthy', False) and not is_timeout,
                'last_update': heartbeat_data.get('datetime'),
                'check_results': heartbeat_data.get('check_results', {}),
                'app_status': heartbeat_data.get('app_status', 'unknown'),
                'timeout': is_timeout,
                'age_seconds': current_time - last_heartbeat
            }
            
        except Exception as e:
            self.logger.error(f"获取心跳状态失败: {e}")
            return {
                'healthy': False,
                'error': f'读取心跳文件失败: {str(e)}',
                'last_update': None
            }
    
    def initialize_heartbeat_file(self) -> bool:
        """
        初始化心跳文件
        
        返回:
            bool: 初始化是否成功
        """
        try:
            # 确保心跳文件目录存在
            heartbeat_file = Path(self.config['heartbeat_file'])
            heartbeat_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 创建初始心跳文件
            initial_data = {
                'timestamp': time.time(),
                'datetime': datetime.now().isoformat(),
                'healthy': True,
                'check_results': {},
                'app_status': 'initializing',
                'version': '1.0.0'
            }
            
            with open(heartbeat_file, 'w') as f:
                json.dump(initial_data, f, indent=2)
            
            self.logger.info(f"心跳文件初始化成功: {heartbeat_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化心跳文件失败: {e}")
            return False
    
    def _check_point_status(self, point: str) -> str:
        """
        检查指定点位的状态
        
        参数:
            point: 检查点位名称
            
        返回:
            str: 状态字符串
        """
        try:
            if point == 'database':
                return self._check_database_connection()
            elif point == 'network':
                return self._check_network_connection()
            elif point == 'file_system':
                return self._check_file_system()
            else:
                self.logger.warning(f"未知的检查点位: {point}")
                return 'unknown'
        except Exception as e:
            self.logger.error(f"检查点位 {point} 状态时出错: {e}")
            return 'error'
    
    def _check_database_connection(self) -> str:
        """
        检查数据库连接状态
        
        返回:
            str: 连接状态
        """
        try:
            # 这里可以添加实际的数据库连接检查逻辑
            # 例如:尝试连接数据库并执行简单查询
            return 'ok'
        except Exception as e:
            self.logger.error(f"数据库连接检查失败: {e}")
            return 'error'
    
    def _check_network_connection(self) -> str:
        """
        检查网络连接状态
        
        返回:
            str: 连接状态
        """
        try:
            # 简单的网络连接检查
            import socket
            socket.create_connection(("8.8.8.8", 53), timeout=3)
            return 'ok'
        except Exception as e:
            self.logger.error(f"网络连接检查失败: {e}")
            return 'error'
    
    def _check_file_system(self) -> str:
        """
        检查文件系统状态
        
        返回:
            str: 文件系统状态
        """
        try:
            # 检查磁盘空间
            import shutil
            total, used, free = shutil.disk_usage("/")
            free_percent = (free / total) * 100
            
            if free_percent < 10:  # 剩余空间少于10%
                return 'warning'
            elif free_percent < 5:  # 剩余空间少于5%
                return 'error'
            else:
                return 'ok'
        except Exception as e:
            self.logger.error(f"文件系统检查失败: {e}")
            return 'error'
    
    def cleanup_heartbeat_file(self):
        """
        清理心跳文件(应用关闭时调用)
        """
        try:
            heartbeat_file = Path(self.config['heartbeat_file'])
            if heartbeat_file.exists():
                # 写入应用关闭状态
                shutdown_data = {
                    'timestamp': time.time(),
                    'datetime': datetime.now().isoformat(),
                    'healthy': False,
                    'check_results': {},
                    'app_status': 'shutdown',
                    'version': '1.0.0'
                }
                
                with open(heartbeat_file, 'w') as f:
                    json.dump(shutdown_data, f, indent=2)
                
                self.logger.info("心跳文件已标记为应用关闭状态")
                
        except Exception as e:
            self.logger.error(f"清理心跳文件失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取当前内部状态
        
        返回:
            Dict: 内部状态字典
        """
        with self._lock:
            return self._status.copy()
    
    def is_healthy(self) -> bool:
        """
        检查系统是否健康
        
        返回:
            bool: 系统是否健康
        """
        with self._lock:
            return self._status['healthy']


def create_heartbeat_monitor(config: Dict[str, Any]) -> HeartbeatMonitor:
    """
    创建心跳监控器实例
    
    参数:
        config: 心跳检测配置
        
    返回:
        HeartbeatMonitor: 心跳监控器实例
    """
    return HeartbeatMonitor(config)