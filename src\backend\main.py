import getpass
import typer
import uvicorn

from typing import Optional, <PERSON><PERSON>

from config.auth_config import login_check


app = typer.Typer(
    name="工业智能平台",
    help="Industry Intelligence Platform",
    add_completion=False,
)


def get_credentials() -> Tuple[Optional[str], Optional[str]]:
    """获取用户凭证信息。
    
    循环提示用户输入用户名和密码,直到验证成功或用户选择退出。
    
    Args:
        None
    
    Returns:
        Tuple[Optional[str], Optional[str]]: 包含用户名和密码的元组。
            如果用户选择退出,则返回 (None, None)。
    """
    while True:
        username = input("请输入用户名 (输入 'q' 退出): ")
        if username.lower() == 'q':
            return None, None
        
        password = getpass.getpass("请输入密码: ")
        
        if login_check(username, password):
            typer.secho("登录成功!!!", fg=typer.colors.GREEN)
            return username, password
        else:
            typer.secho("用户名或密码错误, 请重新输入。", fg=typer.colors.RED)


@app.command()
def run(
    host: str = typer.Option("0.0.0.0", help="服务器主机地址"),
    port: int = typer.Option(8081, help="服务器端口号")
) -> None:
    """启动工业智能大脑服务器。
    
    Args:
        host: 服务器主机地址
        port: 服务器端口号
    """
    try:
        # 直接导入app.py中的app实例
        from app import app as fastapi_app
        
        # 不使用登录
        typer.secho("正在启动服务器", fg=typer.colors.GREEN)
        uvicorn.run(fastapi_app, host=host, port=port)
    except Exception as e:
        typer.secho(f"启动服务器时发生错误: {str(e)}", fg=typer.colors.RED)
        raise typer.Exit(1)


if __name__ == '__main__':
    app()
