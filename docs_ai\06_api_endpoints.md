
# 6. API 接口清单

本部分列出了 `src/backend` 提供的所有 API 端点。

## 6.1. 训练相关接口 (`/train`)

| 方法 | 路径 | 函数名 | 描述 | 请求体 |
| :--- | :--- | :--- | :--- | :--- |
| `POST` | `/train` | `train` | 启动一个**单个**模型训练的定时任务。 | `TrainModelRequestBody` |
| `POST` | `/train_list` | `train_list` | **立即执行**一批模型的训练(非定时)。 | `TrainModelListRequestBody` |
| `POST` | `/stop_train` | `stop_train` | 停止一个指定的训练任务。 | `StopTrainRequestBody` |
| `POST` | `/stop_train_all` | `stop_train_all` | 停止所有正在运行的训练任务。 | - |
| `GET` | `/query_train_tasks` | `query_train_tasks` | 查询所有正在运行的训练任务。 | - |
| `GET` | `/query_latest_train_effect` | `query_latest_train_effect` | 获取指定项目最新的训练效果图 (PNG)。 | - (Query Param: `project_name`) |

## 6.2. 实时预测相关接口 (`/rt`)

| 方法 | 路径 | 函数名 | 描述 | 请求体 |
| :--- | :--- | :--- | :--- | :--- |
| `POST` | `/realtime_predict` | `realtime_predict` | 启动一个通用的实时预测任务。 | `RealtimePredictRequestBody` |
| `POST` | `/stop_realtime_predict` | `stop_realtime_predict` | 停止一个指定的实时预测任务。 | `StopRealtimePredictRequestBody` |
| `POST` | `/stop_realtime_predict_all` | `stop_realtime_predict_all` | 停止所有类型的预测任务。 | - |
| `POST` | `/sequence_realtime_predict` | `sequence_realtime_predict` | 启动一个**序列**实时预测任务。 | `RealtimePredictRequestBody` |
| `POST` | `/sequence_soft_sensor` | `sequence_soft_sensor` | 启动一个**序列软测量**任务。 | `RealtimePredictRequestBody` |
| `POST` | `/stop_sequence_predict` | `stop_sequence_predict` | 停止一个指定的序列预测任务。 | `StopRealtimePredictRequestBody` |
| `POST` | `/stop_sequence_soft` | `stop_sequence_soft` | 停止一个指定的序列软测量任务。 | `StopRealtimePredictRequestBody` |
| `GET` | `/get_realtime_predict_jobs` | `get_realtime_predict_jobs` | 查询所有正在运行的预测任务。 | - |

## 6.3. 优化决策相关接口 (`/opt`)

| 方法 | 路径 | 函数名 | 描述 | 请求体 |
| :--- | :--- | :--- | :--- | :--- |
| `POST` | `/decision_making` | `decision_making` | 启动一个优化决策任务。 | `DecisionMakingRequestBody` |
| `POST` | `/stop_decision_making` | `stop_decision_making` | 停止一个指定的决策任务。 | `StopDecisionMakingRequestBody` |
| `POST` | `/stop_decision_making_all` | `stop_decision_making_all` | 停止所有决策任务。 | - |
| `GET` | `/get_decision_making` | `get_decision_making` | 查询所有正在运行的决策任务。 | - |

## 6.4. 系统通用接口

| 方法 | 路径 | 函数名 | 描述 |
| :--- | :--- | :--- | :--- |
| `GET` | `/` | `index` | 返回欢迎信息。 |
| `GET` | `/favicon.ico` | `favicon` | 返回网站图标。 |
| `GET` | `/query_all_running_tasks` | `query_all_running_tasks` | 一次性查询所有调度器(训练、预测、决策)中的任务。 |
