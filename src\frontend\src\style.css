/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
  background-color: #f5f7fa;
  color: #303133;
}

#app {
  height: 100vh;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

/* 页面标题 */
.page-title {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #409EFF;
}

/* 表单样式 */
.form-container {
  max-width: 600px;
}

.form-item {
  margin-bottom: 20px;
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

/* 状态标签 */
.status-tag {
  margin-left: 10px;
}

/* 数据表格 */
.data-table {
  margin-top: 20px;
}

/* 图表容器 */
.chart-container {
  height: 400px;
  margin-top: 20px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 10px;
  }
  
  .nav-menu {
    margin-top: 10px;
  }
  
  .main-content {
    padding: 10px;
  }
  
  .form-container {
    max-width: 100%;
  }
}

/* 加载动画 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 错误提示 */
.error-message {
  color: #f56c6c;
  text-align: center;
  padding: 20px;
}

/* 成功提示 */
.success-message {
  color: #67c23a;
  text-align: center;
  padding: 20px;
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stats-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stats-value {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stats-label {
  color: #909399;
  font-size: 14px;
}