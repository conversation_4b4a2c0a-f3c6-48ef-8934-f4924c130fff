"""URL解码中间件

本模块实现了用于处理Java客户端URL编码请求的中间件。
当Java客户端对JSON请求体中的中文参数进行URL编码时,
此中间件会自动解码这些参数,确保FastAPI能正确处理中文内容。

使用场景:
- Java客户端使用URLEncoder.encode()对JSON字段值进行编码
- 需要在FastAPI后端自动解码这些编码的参数
- 保持与现有API接口的兼容性

示例:
    Java客户端发送: {"project_name": "%E4%B8%AD%E6%96%87%E9%A1%B9%E7%9B%AE"}
    中间件解码后: {"project_name": "中文项目"}
"""

import json
import urllib.parse
from typing import Callable, Any
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from industrytslib import get_logger

# 初始化日志记录器
middleware_logger = get_logger(
    logger_name='URL Decoder Middleware',
    logger_type='middleware',
    level="DEBUG",
    console_level="INFO"
)


class URLDecoderMiddleware(BaseHTTPMiddleware):
    """
    URL解码中间件
    
    功能:
    1. 拦截所有HTTP请求
    2. 检查请求体是否为JSON格式
    3. 递归解码JSON中所有字符串值的URL编码
    4. 将解码后的请求体传递给后续处理
    
    特点:
    - 只处理Content-Type为application/json的请求
    - 递归处理嵌套的JSON结构
    - 自动检测并解码URL编码的字符串
    - 保持非字符串类型数据不变
    - 错误处理和日志记录
    
    Args:
        app: FastAPI应用实例
        
    示例:
        app.add_middleware(URLDecoderMiddleware)
    """
    
    def __init__(self, app, **kwargs):
        """
        初始化URL解码中间件
        
        Args:
            app: FastAPI应用实例
            **kwargs: 其他中间件参数
        """
        super().__init__(app, **kwargs)
        middleware_logger.info("URL解码中间件已初始化")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        中间件主要处理逻辑
        
        Args:
            request: HTTP请求对象
            call_next: 下一个中间件或路由处理函数
            
        Returns:
            Response: HTTP响应对象
        """
        # 只处理POST、PUT、PATCH请求的JSON数据
        if request.method in ["POST", "PUT", "PATCH"]:
            content_type = request.headers.get("content-type", "")
            
            # 检查是否为JSON请求
            if "application/json" in content_type:
                try:
                    # 读取原始请求体
                    body = await request.body()
                    
                    if body:
                        # 解析JSON数据
                        try:
                            json_data = json.loads(body.decode('utf-8'))
                            middleware_logger.debug(f"原始JSON数据: {json_data}")
                            
                            # 递归解码URL编码的字符串
                            decoded_data = self._decode_json_recursively(json_data)
                            
                            # 检查是否有数据被解码
                            if decoded_data != json_data:
                                middleware_logger.info(f"检测到URL编码数据并已解码: {decoded_data}")
                                
                                # 重新构造请求体
                                new_body = json.dumps(decoded_data, ensure_ascii=False).encode('utf-8')
                                
                                # 创建新的请求对象
                                async def receive():
                                    return {
                                        "type": "http.request",
                                        "body": new_body,
                                        "more_body": False
                                    }
                                
                                # 更新请求的receive函数
                                request._receive = receive
                            else:
                                middleware_logger.debug("未检测到URL编码数据")
                                
                        except json.JSONDecodeError as e:
                            middleware_logger.warning(f"JSON解析失败: {e}")
                        except UnicodeDecodeError as e:
                            middleware_logger.warning(f"UTF-8解码失败: {e}")
                            
                except Exception as e:
                    middleware_logger.error(f"中间件处理异常: {e}")
                    return JSONResponse(
                        status_code=400,
                        content={"code": 400, "message": f"请求体处理失败: {str(e)}"}
                    )
        
        # 继续处理请求
        response = await call_next(request)
        return response
    
    def _decode_json_recursively(self, data: Any) -> Any:
        """
        递归解码JSON数据中的URL编码字符串
        
        Args:
            data: 要处理的数据,可以是字典、列表、字符串或其他类型
            
        Returns:
            Any: 解码后的数据
            
        示例:
            输入: {"name": "%E4%B8%AD%E6%96%87", "list": ["%E6%B5%8B%E8%AF%95"]}
            输出: {"name": "中文", "list": ["测试"]}
        """
        if isinstance(data, dict):
            # 处理字典类型
            return {key: self._decode_json_recursively(value) for key, value in data.items()}
        
        elif isinstance(data, list):
            # 处理列表类型
            return [self._decode_json_recursively(item) for item in data]
        
        elif isinstance(data, str):
            # 处理字符串类型
            return self._decode_url_string(data)
        
        else:
            # 其他类型保持不变
            return data
    
    def _decode_url_string(self, text: str) -> str:
        """
        解码URL编码的字符串
        
        Args:
            text: 可能包含URL编码的字符串
            
        Returns:
            str: 解码后的字符串
            
        示例:
            输入: "%E4%B8%AD%E6%96%87%E9%A1%B9%E7%9B%AE"
            输出: "中文项目"
        """
        try:
            # 检查字符串是否包含URL编码模式
            if '%' in text:
                # 尝试URL解码
                decoded = urllib.parse.unquote(text, encoding='utf-8')
                
                # 如果解码后的字符串与原字符串不同,说明确实进行了解码
                if decoded != text:
                    middleware_logger.debug(f"URL解码: '{text}' -> '{decoded}'")
                    return decoded
            
            # 如果没有URL编码或解码失败,返回原字符串
            return text
            
        except (UnicodeDecodeError, ValueError) as e:
            # 解码失败时返回原字符串
            middleware_logger.warning(f"URL解码失败 '{text}': {e}")
            return text
    
    def is_url_encoded(self, text: str) -> bool:
        """
        检查字符串是否包含URL编码
        
        Args:
            text: 要检查的字符串
            
        Returns:
            bool: 如果包含URL编码返回True,否则返回False
            
        示例:
            输入: "%E4%B8%AD%E6%96%87"
            输出: True
            
            输入: "普通文本"
            输出: False
        """
        try:
            # 简单检查：包含%且解码后不同
            if '%' in text:
                decoded = urllib.parse.unquote(text, encoding='utf-8')
                return decoded != text
            return False
        except Exception:
            return False