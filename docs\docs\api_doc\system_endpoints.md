# 系统端点 API

## 概述

本部分包含不属于特定业务模块(训练、预测、决策)的系统级API端点。这些接口主要用于平台级的状态查询。

## 接口列表

### 1. 查询所有正在运行的任务

-   **接口地址**: `GET /query_all_running_tasks`
-   **功能描述**: 查询平台所有调度器(训练、预测、决策)中当前正在运行的全部后台任务。这是一个用于全局监控的核心接口。
-   **请求参数**: 无

#### 响应结构

响应体是一个JSON对象,包含三个键,分别对应三个调度器:
-   `train_scheduler`: 包含所有在训练调度器中运行的任务对象列表。
-   `predict_scheduler`: 包含所有在预测调度器中运行的任务对象列表。
-   `decision_scheduler`: 包含所有在决策调度器中运行的任务对象列表。

每个任务对象包含任务的详细信息,例如 `id`, `name`, `trigger` 和 `next_run_time`。

**响应示例**:
```json
{
  "train_scheduler": [
    {
      "id": "demo_project_for_training",
      "name": "demo_project_for_training",
      "func": "industrytslib.trainers.classic_trainer.ClassicTrainer.main",
      "args": [],
      "kwargs": {},
      "trigger": "date[2024-07-30 17:00:00 CST]",
      "next_run_time": "2024-07-30T17:00:00+08:00"
    }
  ],
  "predict_scheduler": [
    {
      "id": "sequence_predict_temp_forecast",
      "name": "temp_forecast",
      "func": "industrytslib.predictors.sequence_predictor.SequencePredictor.main",
      "args": [],
      "kwargs": {},
      "trigger": "interval[0:00:30]",
      "next_run_time": "2024-07-30T17:00:30+08:00"
    }
  ],
  "decision_scheduler": []
}
```

#### 调用示例

**cURL**
```bash
curl -X GET "http://localhost:8081/query_all_running_tasks"
```

**Python**
```python
import requests
import json

BASE_URL = "http://localhost:8081"

def query_all_tasks():
    """查询所有调度器中的所有任务"""
    print("--- 正在查询所有运行中的任务 ---")
    try:
        response = requests.get(f"{BASE_URL}/query_all_running_tasks")
        response.raise_for_status()
        
        all_tasks = response.json()
        
        print("\n--- 训练任务 ---")
        print(json.dumps(all_tasks.get('train_scheduler', []), indent=2, ensure_ascii=False))
        
        print("\n--- 预测任务 ---")
        print(json.dumps(all_tasks.get('predict_scheduler', []), indent=2, ensure_ascii=False))
        
        print("\n--- 决策任务 ---")
        print(json.dumps(all_tasks.get('decision_scheduler', []), indent=2, ensure_ascii=False))
        
        return all_tasks
        
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return None

if __name__ == "__main__":
    # 在运行此脚本之前,请确保已通过其他API启动了一些任务
    query_all_tasks()
``` 