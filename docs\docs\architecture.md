# 系统架构文档

## 概述

IndustryAI是一个基于微服务架构的工业智能平台,采用前后端分离设计,支持高并发、高可用的工业时间序列分析和智能决策。

## 整体架构

```mermaid
graph TD
    A["<b>前端展示层</b><br/>Vue 3 + Element Plus + ECharts + Pinia<br/>- 模型训练管理界面<br/>- 实时预测监控界面<br/>- 决策优化控制界面<br/>- 系统监控仪表板"]
    B["<b>API网关层</b><br/>FastAPI + FastAPI Guard<br/>- 请求路由和负载均衡<br/>- 安全认证和访问控制<br/>- 请求限流和防护<br/>- API文档和接口管理"]
    
    subgraph C [业务服务层]
        direction LR
        C1["<b>训练服务</b><br/>- 模型训练<br/>- 参数调优<br/>- 模型评估"]
        C2["<b>预测服务</b><br/>- 实时预测<br/>- 序列预测<br/>- 软测量"]
        C3["<b>决策服务</b><br/>- 智能决策<br/>- 工艺优化<br/>- 多目标优化"]
    end

    D["<b>任务调度层</b><br/>APScheduler + Background Tasks<br/>- 训练任务调度器 (train_scheduler)<br/>- 预测任务调度器 (predict_scheduler)<br/>- 决策任务调度器 (decision_scheduler)<br/>- 后台任务管理器 (background_scheduler)"]
    
    subgraph E [核心算法层 - industrytslib]
        direction LR
        E1["<b>时间序列模型</b><br/>- TimesNet<br/>- Informer<br/>- PatchTST<br/>- Mamba<br/>- LSTM-KAN"]
        E2["<b>优化算法</b><br/>- 强化学习<br/>- 多目标优化<br/>- 遗传算法<br/>- 粒子群优化<br/>- 贝叶斯优化"]
        E3["<b>数据处理</b><br/>- Polars<br/>- 数据清洗<br/>- 特征工程<br/>- 数据增强<br/>- 异常检测"]
    end
    
    subgraph F [数据存储层]
        direction LR
        F1["<b>PostgreSQL</b><br/>- 业务数据<br/>- 模型配置<br/>- 用户信息"]
        F2["<b>Redis</b><br/>- 缓存数据<br/>- 会话管理<br/>- 任务队列"]
        F3["<b>InfluxDB</b><br/>- 时序数据<br/>- 监控指标<br/>- 性能数据"]
    end
    
    G["<b>监控运维层</b><br/>Rust心跳检查器 + 系统监控 + 任务状态监控服务<br/>- 服务健康检查<br/>- 性能指标监控<br/>- 日志聚合分析<br/>- 告警通知机制"]

    A -- "HTTP/WebSocket" --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
```

## 核心组件详解

### 1. 前端展示层

**技术栈**: Vue 3 + Element Plus + ECharts + Pinia

**主要功能**:
- **模型训练管理**: 提供直观的训练任务创建、监控和管理界面
- **实时预测监控**: 实时显示预测结果和系统状态
- **决策优化控制**: 智能决策参数配置和结果展示
- **系统监控仪表板**: 系统性能、资源使用情况的可视化监控

**关键特性**:
- 响应式设计,支持多设备访问
- 实时数据更新,WebSocket长连接
- 丰富的图表组件,支持时间序列数据可视化
- 模块化组件设计,易于扩展和维护

### 2. API网关层

**技术栈**: FastAPI + FastAPI Guard + Uvicorn

**主要功能**:
- **请求路由**: 智能路由分发,支持版本控制
- **安全认证**: IP白名单、请求频率限制、User-Agent检测
- **API文档**: 自动生成OpenAPI文档,支持在线测试
- **中间件**: 请求日志、错误处理、CORS支持

**安全特性**:
- IP访问控制和地理位置过滤
- 请求频率限制和DDoS防护
- 恶意请求检测和自动封禁
- 详细的访问日志和审计跟踪

### 3. 业务服务层

#### 训练服务 (Training Service)
- **单模型训练**: 支持指定算法类型的单个模型训练
- **批量训练**: 支持多个项目的批量训练任务
- **训练管理**: 训练任务的启动、停止、监控
- **模型评估**: 自动化模型性能评估和报告生成

#### 预测服务 (Prediction Service)
- **实时预测**: 高频率的实时数据预测
- **序列预测**: 长序列时间序列预测
- **软测量**: 基于过程参数的质量指标预测
- **多输出预测**: 同时预测多个目标变量

#### 决策服务 (Decision Service)
- **智能决策**: 基于强化学习的自动决策
- **工艺优化**: 多目标优化的工艺参数调整
- **决策评估**: 决策效果的实时评估和反馈
- **策略学习**: 在线学习和策略更新

### 4. 任务调度层

**技术栈**: APScheduler + AsyncIO

**调度器类型**:
- **train_scheduler**: 管理所有训练任务的调度
- **predict_scheduler**: 管理实时预测任务的调度
- **decision_scheduler**: 管理决策优化任务的调度
- **background_scheduler**: 管理系统维护和监控任务

**特性**:
- 支持Cron表达式和间隔调度
- 任务持久化和故障恢复
- 并发控制和资源管理
- 任务监控和日志记录

### 5. 核心算法层

**industrytslib核心库**:

#### 时间序列模型
- **TimesNet**: 基于时间序列分解的深度学习模型
- **Informer**: 长序列时间序列预测的Transformer模型
- **PatchTST**: 基于Patch的时间序列Transformer
- **Mamba**: 状态空间模型,支持长序列建模
- **LSTM-KAN**: 结合LSTM和Kolmogorov-Arnold网络

#### 优化算法
- **强化学习**: PPO、SAC、TD3等算法
- **多目标优化**: NSGA-II、MOEA/D等进化算法
- **贝叶斯优化**: 高效的超参数优化
- **粒子群优化**: 适用于连续优化问题

#### 数据处理
- **Polars**: 高性能数据处理引擎
- **数据清洗**: 异常值检测和处理
- **特征工程**: 自动化特征提取和选择
- **数据增强**: 时间序列数据增强技术

### 6. 数据存储层

#### PostgreSQL
- **业务数据**: 项目配置、模型参数、用户信息
- **关系数据**: 复杂的业务关系和约束
- **事务支持**: 保证数据一致性和完整性

#### Redis
- **缓存数据**: 热点数据缓存,提高访问速度
- **会话管理**: 用户会话和状态管理
- **任务队列**: 异步任务的队列管理
- **实时数据**: 临时存储实时计算结果

#### InfluxDB
- **时序数据**: 高效存储时间序列数据
- **监控指标**: 系统性能和业务指标
- **数据压缩**: 自动数据压缩和归档
- **查询优化**: 针对时序数据的查询优化

### 7. 监控运维层

**Rust心跳检查器**:
- 高性能的系统健康检查
- 实时监控服务状态
- 自动故障检测和恢复
- 详细的性能指标收集

**监控功能**:
- **服务监控**: API响应时间、错误率、吞吐量
- **资源监控**: CPU、内存、磁盘、网络使用情况
- **业务监控**: 训练任务状态、预测准确率、决策效果
- **日志监控**: 错误日志聚合和分析

## 数据流架构

### 训练数据流
```mermaid
graph LR
    A[数据源<br/>PostgreSQL] --> B[数据预处理<br/>Polars]
    B --> C[特征工程<br/>industrytslib]
    C --> D[模型训练<br/>PyTorch]
    D --> E[模型评估<br/>评估指标]
    E --> F[模型部署<br/>Redis缓存]
```

### 预测数据流
```mermaid
graph LR
    A[实时数据<br/>InfluxDB] --> B[数据清洗<br/>Polars]
    B --> C[特征提取<br/>特征工程]
    C --> D[模型推理<br/>模型预测]
    D --> E[结果后处理<br/>业务逻辑]
    E --> F[结果存储<br/>PostgreSQL]
```

### 决策数据流
```mermaid
graph LR
    A[当前状态<br/>实时数据] --> B[状态评估<br/>强化学习]
    B --> C[策略选择<br/>决策算法]
    C --> D[动作执行<br/>控制指令]
    D --> E[效果评估<br/>反馈数据]
    E --> F[策略更新<br/>模型更新]
```

## 部署架构

### 单机部署
```mermaid
graph TD
    subgraph S [单机服务器]
        subgraph F [前端]
            Nginx
        end
        subgraph B [后端]
            FastAPI
        end
        subgraph D [数据存储]
            PostgreSQL
            Redis
        end
        F --> B
        B --> D
    end
```

### 分布式部署
```mermaid
graph TD
    LB[负载均衡器<br/>Nginx, HAProxy]
    
    subgraph W [Web服务器集群]
        direction LR
        W1[FastAPI 1]
        W2[FastAPI 2]
        W3[...]
    end

    subgraph C [计算节点集群]
        direction LR
        C1[训练服务]
        C2[预测服务]
        C3[...]
    end

    subgraph D [数据存储集群]
        direction LR
        D1["PostgreSQL<br/>(主从)"]
        D2["Redis<br/>(集群)"]
        D3["InfluxDB<br/>(集群)"]
    end

    LB --> W
    W --> C
    W --> D
    C --> D
```

## 性能特性

### 高并发支持
- **异步处理**: 基于AsyncIO的异步架构
- **连接池**: 数据库连接池管理
- **缓存策略**: 多级缓存提高响应速度
- **负载均衡**: 支持水平扩展

### 高可用性
- **故障转移**: 自动故障检测和切换
- **数据备份**: 定期数据备份和恢复
- **服务监控**: 实时服务健康检查
- **优雅降级**: 部分服务故障时的降级策略

### 可扩展性
- **微服务架构**: 服务独立部署和扩展
- **插件机制**: 支持算法和功能插件
- **配置驱动**: 通过配置文件灵活调整
- **API版本控制**: 支持多版本API共存

## 安全架构

### 网络安全
- **IP白名单**: 限制访问来源
- **HTTPS加密**: 数据传输加密
- **防火墙**: 网络层访问控制
- **VPN接入**: 安全的远程访问

### 应用安全
- **身份认证**: 用户身份验证
- **权限控制**: 基于角色的访问控制
- **输入验证**: 严格的输入数据验证
- **SQL注入防护**: 参数化查询防护

### 数据安全
- **数据加密**: 敏感数据加密存储
- **访问审计**: 详细的访问日志记录
- **数据脱敏**: 测试环境数据脱敏
- **备份加密**: 备份数据加密保护

## 总结

IndustryAI采用现代化的微服务架构,具有高性能、高可用、高安全的特点。通过合理的分层设计和组件化开发,系统具有良好的可扩展性和可维护性,能够满足工业智能应用的各种需求。