"""
工业AI服务器主应用模块
本模块负责:
- FastAPI应用的创建和配置
- 后台任务调度器的初始化和管理
- 应用生命周期管理
- 路由注册和API文档配置
"""
import os

from apscheduler.schedulers.background import BackgroundScheduler
from contextlib import asynccontextmanager
from datetime import datetime
from fastapi import FastAPI
from fastapi.responses import FileResponse
from dotenv import load_dotenv

# 安全组件导入 
# ! 暂时不使用
# from guard.middleware import SecurityMiddleware
# from guard.models import SecurityConfig
# from guard.handlers.ipinfo_handler import IPInfoManager

# industrytslib的相关库导入
from industrytslib import (
    get_logger, 
    read_config_toml
)
# 导入系统运行状态监控
# 向数据库中中写入心跳信号
from industrytslib.core.background_tasks.operation_status import SystemOperationStatus
# 导入数据共享相关库
from industrytslib.utils.share import (
    ModifyDataframe, 
    ReadDataframe
)
# TODO 使用duckdb替代share
# from industrytslib.utils.df_share.share_duckdb import (
#     ModifyDataframe, 
#     ReadDataframe
# )

# 导入各功能模块的路由
from routers.train import router as train_router
from routers.realtime_predict import router as realtime_predict_router
from routers.decision_making import router as decision_making_router
from utils.basefunc import worship
from utils.task_scheduler import (
    background_scheduler, 
    train_scheduler, 
    predict_scheduler, 
    decision_scheduler
)

# 后台任务相关导入
# from background_tasks import BackgroundTask

# 用 faulthandler 捕捉 Python 崩溃栈(段存储:segmentation fault)
# 没问题则注释掉, 有则打开
import faulthandler
faulthandler.enable()

# * 将当前工作目录设置为当前文件所在目录
# os.chdir(os.path.dirname(os.path.abspath(__file__)))

# * 加载.env文件
load_dotenv()

# * 初始化日志记录器
fastapi_logger = get_logger(
    logger_name='Industry AI Server', 
    logger_type='fastapi', 
    level="DEBUG",
    console_level="WARNING",
)

# * 读取数据库配置文件
dbconfig = read_config_toml('config/database_config.toml')


# * 从.env文件中读取后台任务开关配置
_BACKGROUND_TASKS_ENABLED = os.getenv('Background_task', 'False').lower() == 'true'
fastapi_logger.info(f"后台任务状态设置为: {'启用' if _BACKGROUND_TASKS_ENABLED else '禁用'}")
_HEARTBEAT_ENABLED = os.getenv('Heartbeat_task', 'False').lower() == 'true'
fastapi_logger.info(f"心跳检测状态设置为: {'启用' if _HEARTBEAT_ENABLED else '禁用'}")

# * 配置作业调度器默认参数
job_defaults = {
    'coalesce': True,  # 堆积的任务只运行一次
    'max_instances': 1,  # 同一个任务同时只能有一个实例运行
    'misfire_grace_time': 80 # 任务错过执行时间的容忍度(秒)
}

# * 初始化并启动后台任务调度器
scheduler = BackgroundScheduler(job_defaults=job_defaults)
scheduler.start()


# * 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    FastAPI应用生命周期管理函数
    负责:
    - 应用启动时的初始化工作
    - 应用关闭时的清理工作
    
    参数:
        app: FastAPI应用实例
    """
    global heartbeat_monitor
    
    fastapi_logger.info('工业AI服务器启动!!!')
    worship()  # 执行启动时的自定义操作
    
    background_task = None
    
    
    # * 是否启动后台任务
    if _BACKGROUND_TASKS_ENABLED:
        fastapi_logger.info('正在启动后台任务...')
        # ! 共享dataframe的修改和读取任务
        modify_dataframe_client = ModifyDataframe(dbconfig)
        read_dataframe_client = ReadDataframe()
        
        # * 添加数据修改任务到调度器
        scheduler.add_job(
            modify_dataframe_client.modify_dataframe,
            trigger='interval',
            seconds=10,
            id="modify_dataframe",
            replace_existing=True,
            next_run_time=datetime.now(),  # 设置立即执行
            misfire_grace_time=None  # 允许错过的任务立即执行
        )
        
        # * 添加数据读取任务到调度器
        scheduler.add_job(
            read_dataframe_client.read_dataframe_latest,
            trigger='interval',
            seconds=60,
            id="read_dataframe",
            replace_existing=True,
            next_run_time=datetime.now(),  # 设置立即执行
            misfire_grace_time=None  # 允许错过的任务立即执行
        )
        
        # ! 其余后台任务,使用线程池执行器执行
        # ! 现在所有的后台任务都交给别人完成,暂时保留接口
        # background_task = BackgroundTask(dbconfig)
        # background_task.start()

        
        fastapi_logger.info('=====================后台任务启动完成!!!=====================')
    else:
        fastapi_logger.info('后台任务已禁用')
        
    if _HEARTBEAT_ENABLED:
        fastapi_logger.info('正在启动心跳检测...')
        heartbeat_task = SystemOperationStatus(dbconfig["operation_status"])
        scheduler.add_job(
            heartbeat_task.run,
            trigger='interval',
            seconds=10,
            id="heartbeat_monitor",
            replace_existing=True,
            next_run_time=datetime.now(),  # 设置立即执行
            misfire_grace_time=None  # 允许错过的任务立即执行
        )
        fastapi_logger.info('心跳检测启动完成!!!')
    else:
        fastapi_logger.info('心跳检测已禁用')

    try:
        yield
    finally:
        # * 应用关闭时的清理工作
        fastapi_logger.warning('工业AI服务器正在关闭...')
        
        # * 关闭后台任务
        if _BACKGROUND_TASKS_ENABLED and background_task:
            background_task.clean()
            background_scheduler.shutdown(wait=False)
        
        # * 关闭所有任务调度器
        # 使用wait=False确保可以立即关闭
        scheduler.shutdown(wait=False)
        train_scheduler.shutdown(wait=False)
        predict_scheduler.shutdown(wait=False)
        decision_scheduler.shutdown(wait=False)
        fastapi_logger.warning('工业AI服务器已关闭!!!')

# * 创建FastAPI应用实例
app = FastAPI(
    title='Industry AI Server',
    description='工业AI服务器',
    version='2025.07.preview',
    lifespan=lifespan,  # 指定生命周期管理函数
)

# # * 安全组件配置, 根据需求配置
# config = SecurityConfig(
#     geo_ip_handler=IPInfoManager("your_ipinfo_token_here"),  # NOTE: Required when using country blocking
#     enable_redis=True,  # Enabled by default, disable to use in-memory storage
#     redis_url="redis://localhost:6379/0",
#     redis_prefix="prod:security:",
#     whitelist=["***********", "2001:db8::1"],
#     blacklist=["********", "2001:db8::2"],
#     blocked_countries=["AR", "IT"],
#     blocked_user_agents=["curl", "wget"],
#     auto_ban_threshold=5,
#     auto_ban_duration=86400,
#     custom_log_file="security.log",
# )

# app.add_middleware(SecurityMiddleware, config=config)

# * 网站图标路径配置
favicon_path = 'favicon.ico'

@app.get('/')
async def index():
    """应用根路由,返回欢迎信息"""
    return {'message': 'Welcome to Industry AI Server of DCKJ!!!'}

@app.get('/favicon.ico', include_in_schema=False)
async def favicon():
    """返回网站图标"""
    return FileResponse(favicon_path)

# * 注册各功能模块的路由
app.include_router(train_router, prefix='/train', tags=['训练相关接口'])
app.include_router(realtime_predict_router, prefix='/rt', tags=['实时预测相关接口'])
app.include_router(decision_making_router, prefix='/opt', tags=['优化决策相关接口'])

# * 查询train、rt、opt三个模块中scheduler中所有在运行的任务
@app.get('/query_all_running_tasks')
async def query_all_running_tasks():
    """查询train、rt、opt三个模块中scheduler中所有在运行的任务"""
    # 查询train、rt、opt三个模块中scheduler中所有在运行的任务
    train_jobs = train_scheduler.get_jobs()
    predict_jobs = predict_scheduler.get_jobs()
    decision_jobs = decision_scheduler.get_jobs()
    
    # 将Job对象序列化为字典格式
    def serialize_job(job):
        """将APScheduler Job对象序列化为字典"""
        return {
            'id': job.id,
            'name': job.name,
            'next_run_time': job.next_run_time,
            'last_run_time': getattr(job, 'last_run_time', None)
        }
    
    return {
        'train_scheduler': [serialize_job(job) for job in train_jobs],
        'predict_scheduler': [serialize_job(job) for job in predict_jobs],
        'decision_scheduler': [serialize_job(job) for job in decision_jobs]
    }
