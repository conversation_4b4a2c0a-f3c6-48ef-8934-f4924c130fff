"""
用于处理后台任务的类。
"""
# import datetime

# 导入线程相关的包
from typing import Literal
from apscheduler.schedulers.background import BackgroundScheduler

from industrytslib import (
    build_background_tasks,
    get_logger
)


class BackgroundTask:
    def __init__(self, dbconfig: dict):
        super().__init__()
        self.logger = get_logger(__name__, 'background_tasks')
        self.dbconfig = dbconfig
        (
            self.task_decision_history_client,
            self.task_prediction_transaction_client,
            self.task_prediction_settle_client
        ) = build_background_tasks(self.dbconfig)
        
        self.scheduler = BackgroundScheduler()

        self.interval_decision_history = 60
        self.interval_prediction_transaction = 60
        self.interval_prediction_settle = 10

        # Flags to control task execution
        self.decision_history_enabled = True
        self.prediction_transaction_enabled = False # Disabled by default
        self.prediction_settle_enabled = False # Disabled by default

    def thread_decision_history(self) -> None:
        if not self.decision_history_enabled:
            self.logger.info("决策历史数据处理任务已禁用,跳过执行。")
            return
        try:
            self.logger.debug("执行决策历史数据处理任务。")
            self.task_decision_history_client.main()
        except Exception as e:
            self.logger.error(f"决策历史数据处理时发生错误: {e}")

    def thread_prediction_transaction(self) -> None:
        if not self.prediction_transaction_enabled:
            self.logger.info("预测数据处理任务已禁用,跳过执行。")
            return
        try:
            self.logger.debug("执行预测数据处理任务。")
            self.task_prediction_transaction_client.main()
        except Exception as e:
            self.logger.error(f"预测数据处理时发生错误: {e}")

    def thread_prediction_settle(self) -> None:
        if not self.prediction_settle_enabled:
            self.logger.info("预测转换数据处理任务已禁用,跳过执行。")
            return
        try:
            self.logger.debug("执行预测转换数据处理任务。")
            self.task_prediction_settle_client.main()
        except Exception as e:
            self.logger.error(f"预测转换数据处理时发生错误: {e}")

    def start(self) -> None:
        """
        启动所有后台任务使用APScheduler。
        """
        self.scheduler.add_job(
            self.thread_decision_history, 
            'interval', 
            seconds=self.interval_decision_history, 
            id='decision_history_task',
            misfire_grace_time=60
        )
        
        # self.scheduler.add_job(
        #     self.thread_prediction_transaction,
        #     'interval',
        #     seconds=self.interval_prediction_transaction,
        #     id='prediction_transaction_task',
        #     misfire_grace_time=60
        # )
        # self.scheduler.add_job(
        #     self.thread_prediction_settle,
        #     'interval',
        #     seconds=self.interval_prediction_settle,
        #     id='prediction_settle_task',
        #     misfire_grace_time=60
        # )
        
        # # Pause tasks that are not enabled by default
        # if not self.prediction_transaction_enabled:
        #     job = self.scheduler.get_job('prediction_transaction_task')
        #     if job: # Ensure job exists before trying to pause
        #         self.scheduler.pause_job('prediction_transaction_task')
        #         self.logger.info("任务 prediction_transaction_task 已在启动时因默认禁用而被暂停。")
        
        # if not self.prediction_settle_enabled:
        #     job = self.scheduler.get_job('prediction_settle_task')
        #     if job: # Ensure job exists before trying to pause
        #         self.scheduler.pause_job('prediction_settle_task')
        #         self.logger.info("任务 prediction_settle_task 已在启动时因默认禁用而被暂停。")


        self.scheduler.start()
        self.logger.info("================后台任务已经开始 (使用 APScheduler)==================")

    def clean(self) -> None:
        """Safely stop all background tasks and cleanup resources."""
        if self.scheduler.running:
            self.scheduler.shutdown()
        self.logger.warning("================后台任务结束 (APScheduler)================")

    def set_task_enabled(
        self, 
        task_id: Literal['decision_history', 'prediction_transaction', 'prediction_settle'], 
        enable: bool
    ):
        """Enable or disable a specific task."""
        job_id = f"{task_id}_task"
        if task_id == 'decision_history':
            self.decision_history_enabled = enable
        elif task_id == 'prediction_transaction':
            self.prediction_transaction_enabled = enable
        elif task_id == 'prediction_settle':
            self.prediction_settle_enabled = enable
        else:
            self.logger.error(f"未知的任务 ID: {task_id}")
            return

        if self.scheduler.running:
            job = self.scheduler.get_job(job_id)
            if job:
                if enable:
                    if job.next_run_time is None: # Check if paused
                        self.scheduler.resume_job(job_id)
                        self.logger.info(f"任务 {job_id} 已恢复。")
                    else:
                        self.logger.info(f"任务 {job_id} 已启用且正在运行。") # Or it was never paused
                else:
                    if job.next_run_time is not None: # Check if running
                        self.scheduler.pause_job(job_id)
                        self.logger.info(f"任务 {job_id} 已暂停。")
                    else:
                        self.logger.info(f"任务 {job_id} 已禁用且已暂停。") # Or it was already paused
            else:
                self.logger.warning(f"在调度器中未找到任务 {job_id}。可能尚未启动或已被移除。")
        else:
            self.logger.info(f"任务 {task_id} 的启用状态已设置为 {enable}。调度器尚未运行。")

