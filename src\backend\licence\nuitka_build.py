import os
import sys
import subprocess

def run_nuitka_build():
    output_dir = "dist"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    nuitka_cmd = [
        "python", "-m", "nuitka",
        # 基础配置
        "--standalone",  # 替换 --onefile,生成独立目录
        "--follow-imports",
        "--show-progress",
        
        # 性能优化
        "--jobs=8",
        "--lto=yes",
        "--low-memory",
        
        # PyTorch 相关
        "--include-package=torch",
        "--include-package=torch.cuda",
        "--include-package=torch.cuda.amp",
        "--include-package=torch.utils.cpp_extension",
        "--include-package=torch.distributed",
        "--include-module=torch._C",
        # "--include-module=torch.ops",
        
        # CUDA 相关
        "--include-package=nvidia.cublas",
        "--include-package=nvidia.cudnn",
        
        # 数据处理相关
        "--include-package=numpy",
        "--include-package=pandas",
        "--include-package=polars",
        # "--include-package=pywavelets",
        "--include-package=sympy",
        
        # Web/API 相关
        "--include-package=starlette",
        "--include-package=fastapi",
        "--include-package=uvicorn",
        
        # 可视化相关
        "--include-package=plotly",
        "--include-package=pyecharts",
        
        # 工具包
        "--include-package=simplejson",
        "--include-package=pydantic",
        "--include-package=rich",
        "--include-package=regex",
        "--include-package=tensorboard",
        
        # 类型和兼容性支持
        "--include-package=typing_extensions",
        "--include-package=packaging",
        "--include-package=filelock",
        
        # 优化插件
        "--enable-plugin=numpy",
        
        # 输出配置
        f"--output-dir={output_dir}",
        "--remove-output",
        
        # 优化标志
        "--python-flag=no_site",
        "--python-flag=no_warnings",
        
        # 调试信息
        "--show-memory",
        "--show-modules",
        
        # 主程序文件
        "main.py"
    ]

    try:
        print("开始编译...")
        subprocess.run(nuitka_cmd, check=True)
        print("编译完成！输出目录:", output_dir)
        
        # 检查输出目录大小
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(os.path.join(output_dir, "main.dist")):
            for f in filenames:
                fp = os.path.join(dirpath, f)
                total_size += os.path.getsize(fp)
        print(f"输出目录总大小: {total_size / (1024*1024):.2f} MB")
            
    except subprocess.CalledProcessError as e:
        print(f"编译失败: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"发生错误: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    run_nuitka_build()
