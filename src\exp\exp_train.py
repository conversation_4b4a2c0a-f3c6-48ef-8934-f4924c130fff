import os
import sys
from industrytslib import get_trainer
from industrytslib.utils.readconfig import read_config_toml


# Add both current directory and parent directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.extend([current_dir, parent_dir])

if __name__ == "__main__":
    suffix_list = [""]
    # suffix_list = ["", "_pred3", "_pred5", "_pred30"]
    project_name_ = "阳泉回转窑联合训练fcao样本_阳泉回转窑联合训练fcao样本_MSASS"

    for suffix in suffix_list:
        project_name = project_name_ + suffix
        config = read_config_toml("config/database_config.toml")
        trainer = get_trainer(
            project_name=project_name,
            # dbconfig=config,
            model_type="time_series_classic"
        )
        # trainer.run(preprocess_flag=True)
        trainer.main()
        # trainer.test(test=1)
        del trainer
