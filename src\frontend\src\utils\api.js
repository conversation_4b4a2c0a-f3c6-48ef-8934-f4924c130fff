import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: '/api', // 通过Vite代理转发到后端
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 在请求发送前做一些处理
    console.log('发送请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 对响应数据做处理
    console.log('收到响应:', response.status, response.config.url)
    return response
  },
  (error) => {
    console.error('响应错误:', error)
    
    // 统一错误处理
    let message = '请求失败'
    
    if (error.response) {
      // 服务器返回错误状态码
      const { status, data } = error.response
      switch (status) {
        case 400:
          message = data.detail || '请求参数错误'
          break
        case 401:
          message = '未授权访问'
          break
        case 403:
          message = '禁止访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data.detail || `请求失败 (${status})`
      }
    } else if (error.request) {
      // 网络错误
      message = '网络连接失败,请检查网络设置'
    } else {
      // 其他错误
      message = error.message || '未知错误'
    }
    
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// API方法封装
export const trainAPI = {
  // 启动单个模型训练
  async startTrain(data) {
    return api.post('/train/train', data)
  },
  
  // 启动批量模型训练
  async startBatchTrain(data) {
    return api.post('/train/train_list', data)
  },
  
  // 停止训练
  async stopTrain(data) {
    return api.post('/train/stop_train', data)
  },
  
  // 停止所有训练
  async stopAllTrain() {
    return api.post('/train/stop_all_train')
  }
}

export const predictAPI = {
  // 启动实时预测
  async startPredict(data) {
    return api.post('/rt/realtime_predict', data)
  },
  
  // 启动序列实时预测
  async startSeqPredict(data) {
    return api.post('/rt/seq_realtime_predict', data)
  },
  
  // 启动序列软测量
  async startSeqSoft(data) {
    return api.post('/rt/seq_soft', data)
  },
  
  // 停止实时预测
  async stopPredict(data) {
    return api.post('/rt/stop_realtime_predict', data)
  },
  
  // 停止所有预测
  async stopAllPredict() {
    return api.post('/rt/stop_all_realtime_predict')
  }
}

export const decisionAPI = {
  // 启动优化决策
  async startDecision(data) {
    return api.post('/opt/decision_making', data)
  },
  
  // 停止优化决策
  async stopDecision(data) {
    return api.post('/opt/stop_decision_making', data)
  },
  
  // 停止所有决策
  async stopAllDecision() {
    return api.post('/opt/stop_all_decision_making')
  }
}

export const systemAPI = {
  // 健康检查
  async checkHealth() {
    return api.get('/health')
  },
  
  // 获取心跳状态
  async getHeartbeatStatus() {
    return api.get('/heartbeat/status')
  }
}

export default api