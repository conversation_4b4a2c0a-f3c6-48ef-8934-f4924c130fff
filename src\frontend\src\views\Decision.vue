<template>
  <div class="decision">
    <div class="page-title">决策优化</div>
    
    <!-- 决策表单 -->
    <div class="card">
      <h3>启动决策任务</h3>
      <el-form 
        ref="decisionFormRef" 
        :model="decisionForm" 
        :rules="decisionRules" 
        label-width="120px" 
        class="form-container"
      >
        <el-form-item label="项目名称" prop="project_name">
          <el-input 
            v-model="decisionForm.project_name" 
            placeholder="请输入项目名称"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="决策间隔" prop="interval">
          <el-input-number 
            v-model="decisionForm.interval" 
            :min="1" 
            :max="3600" 
            placeholder="秒"
            style="width: 100%"
          />
          <div class="form-help">决策任务执行间隔时间(秒)</div>
        </el-form-item>
        
        <div class="button-group">
          <el-button type="primary" @click="startDecision" :loading="loading.decision">
            <el-icon><VideoPlay /></el-icon>
            启动决策
          </el-button>
          <el-button @click="resetForm">
            <el-icon><RefreshRight /></el-icon>
            重置表单
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 决策参数配置 -->
    <div class="card">
      <h3>决策参数配置</h3>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>优化目标</span>
            </template>
            <el-form label-width="100px">
              <el-form-item label="目标类型">
                <el-select v-model="optimizationConfig.objective" style="width: 100%">
                  <el-option label="最大化产量" value="maximize_yield" />
                  <el-option label="最小化成本" value="minimize_cost" />
                  <el-option label="最大化效率" value="maximize_efficiency" />
                  <el-option label="多目标优化" value="multi_objective" />
                </el-select>
              </el-form-item>
              <el-form-item label="权重系数">
                <el-input-number v-model="optimizationConfig.weight" :min="0" :max="1" :step="0.1" style="width: 100%" />
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>约束条件</span>
            </template>
            <el-form label-width="100px">
              <el-form-item label="温度范围">
                <el-input-group>
                  <el-input-number v-model="constraintConfig.tempMin" placeholder="最小值" style="width: 50%" />
                  <el-input-number v-model="constraintConfig.tempMax" placeholder="最大值" style="width: 50%" />
                </el-input-group>
              </el-form-item>
              <el-form-item label="压力范围">
                <el-input-group>
                  <el-input-number v-model="constraintConfig.pressureMin" placeholder="最小值" style="width: 50%" />
                  <el-input-number v-model="constraintConfig.pressureMax" placeholder="最大值" style="width: 50%" />
                </el-input-group>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 决策控制 -->
    <div class="card">
      <h3>决策控制</h3>
      <el-form label-width="120px" class="form-container">
        <el-form-item label="停止指定决策">
          <div style="display: flex; gap: 10px; align-items: center;">
            <el-input 
              v-model="stopProjectName" 
              placeholder="请输入要停止的项目名称"
              style="flex: 1;"
            />
            <el-button type="warning" @click="stopDecision" :loading="loading.stopDecision">
              <el-icon><VideoPause /></el-icon>
              停止决策
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="停止所有决策">
          <el-button type="danger" @click="stopAllDecision" :loading="loading.stopAllDecision">
            <el-icon><VideoStop /></el-icon>
            停止所有决策
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 决策结果展示 -->
    <div class="card">
      <h3>决策结果监控</h3>
      <div class="toolbar">
        <el-button @click="loadDecisionResults" :loading="loading.results">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-select v-model="selectedProject" placeholder="选择项目" style="width: 200px; margin-left: 10px;">
          <el-option label="全部项目" value="" />
          <el-option 
            v-for="project in projectList" 
            :key="project" 
            :label="project" 
            :value="project" 
          />
        </el-select>
      </div>
      
      <!-- 优化结果图表 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="12">
          <div class="chart-container">
            <v-chart :option="objectiveChartOption" autoresize />
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-container">
            <v-chart :option="parameterChartOption" autoresize />
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 优化建议 -->
    <div class="card">
      <h3>优化建议</h3>
      <el-table :data="optimizationSuggestions" style="width: 100%">
        <el-table-column prop="parameter" label="参数" width="150" />
        <el-table-column prop="currentValue" label="当前值" width="120" />
        <el-table-column prop="suggestedValue" label="建议值" width="120" />
        <el-table-column prop="improvement" label="预期改善" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.improvement > 0 ? 'success' : 'warning'">
              {{ scope.row.improvement > 0 ? '+' : '' }}{{ scope.row.improvement }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="confidence" label="置信度" width="100">
          <template #default="scope">
            <el-progress :percentage="scope.row.confidence" :stroke-width="8" />
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button size="small" type="primary" @click="applySuggestion(scope.row)">
              应用建议
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 决策记录 -->
    <div class="card">
      <h3>决策记录</h3>
      <div class="toolbar">
        <el-button @click="loadDecisionRecords" :loading="loading.records">
          <el-icon><Refresh /></el-icon>
          刷新记录
        </el-button>
        <el-button @click="clearDecisionRecords" type="danger" plain>
          <el-icon><Delete /></el-icon>
          清空记录
        </el-button>
      </div>
      
      <el-table :data="decisionRecords" style="width: 100%" v-loading="loading.records">
        <el-table-column prop="timestamp" label="时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="项目名称" width="150" />
        <el-table-column prop="interval" label="间隔(秒)" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
              {{ scope.row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="描述" />
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { decisionAPI } from '../utils/api'
import db from '../utils/database'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const decisionFormRef = ref()

const loading = reactive({
  decision: false,
  stopDecision: false,
  stopAllDecision: false,
  records: false,
  results: false
})

const decisionForm = reactive({
  project_name: '',
  interval: 60
})

const optimizationConfig = reactive({
  objective: 'maximize_yield',
  weight: 0.8
})

const constraintConfig = reactive({
  tempMin: 80,
  tempMax: 120,
  pressureMin: 1.0,
  pressureMax: 3.0
})

const stopProjectName = ref('')
const decisionRecords = ref([])
const selectedProject = ref('')
const projectList = ref([])
const objectiveData = ref([])
const parameterData = ref([])
const optimizationSuggestions = ref([])

// 表单验证规则
const decisionRules = {
  project_name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' }
  ],
  interval: [
    { required: true, message: '请输入决策间隔', trigger: 'blur' },
    { type: 'number', min: 1, max: 3600, message: '间隔时间应在1-3600秒之间', trigger: 'blur' }
  ]
}

// 目标函数图表配置
const objectiveChartOption = computed(() => {
  return {
    title: {
      text: '优化目标趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'time'
    },
    yAxis: {
      type: 'value',
      name: '目标值'
    },
    series: [
      {
        name: '目标函数值',
        type: 'line',
        data: objectiveData.value,
        smooth: true,
        lineStyle: {
          color: '#67C23A'
        }
      }
    ]
  }
})

// 参数优化图表配置
const parameterChartOption = computed(() => {
  return {
    title: {
      text: '参数优化结果',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      top: 30
    },
    xAxis: {
      type: 'time'
    },
    yAxis: {
      type: 'value',
      name: '参数值'
    },
    series: [
      {
        name: '温度',
        type: 'line',
        data: parameterData.value.map(item => [item.timestamp, item.temperature]),
        smooth: true,
        lineStyle: { color: '#E6A23C' }
      },
      {
        name: '压力',
        type: 'line',
        data: parameterData.value.map(item => [item.timestamp, item.pressure]),
        smooth: true,
        lineStyle: { color: '#409EFF' }
      }
    ]
  }
})

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 启动决策
const startDecision = async () => {
  try {
    await decisionFormRef.value.validate()
    loading.decision = true
    
    const response = await decisionAPI.startDecision(decisionForm)
    
    // 记录到本地数据库
    await db.addDecisionRecord({
      projectName: decisionForm.project_name,
      interval: decisionForm.interval,
      status: 'success',
      message: '决策任务启动成功',
      config: {
        optimization: optimizationConfig,
        constraints: constraintConfig
      }
    })
    
    ElMessage.success('决策任务启动成功')
    await loadDecisionRecords()
    
  } catch (error) {
    console.error('启动决策失败:', error)
    
    // 记录失败信息
    await db.addDecisionRecord({
      projectName: decisionForm.project_name,
      interval: decisionForm.interval,
      status: 'failed',
      message: `决策任务启动失败: ${error.message}`
    })
    
  } finally {
    loading.decision = false
  }
}

// 停止指定决策
const stopDecision = async () => {
  if (!stopProjectName.value.trim()) {
    ElMessage.warning('请输入要停止的项目名称')
    return
  }
  
  try {
    loading.stopDecision = true
    
    const response = await decisionAPI.stopDecision({
      project_name: stopProjectName.value.trim()
    })
    
    await db.addDecisionRecord({
      projectName: stopProjectName.value.trim(),
      status: 'success',
      message: '决策任务停止成功'
    })
    
    ElMessage.success('决策任务停止成功')
    stopProjectName.value = ''
    await loadDecisionRecords()
    
  } catch (error) {
    console.error('停止决策失败:', error)
  } finally {
    loading.stopDecision = false
  }
}

// 停止所有决策
const stopAllDecision = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要停止所有决策任务吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.stopAllDecision = true
    
    const response = await decisionAPI.stopAllDecision()
    
    await db.addDecisionRecord({
      projectName: 'ALL',
      status: 'success',
      message: '所有决策任务停止成功'
    })
    
    ElMessage.success('所有决策任务停止成功')
    await loadDecisionRecords()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止所有决策失败:', error)
    }
  } finally {
    loading.stopAllDecision = false
  }
}

// 应用优化建议
const applySuggestion = async (suggestion) => {
  try {
    await ElMessageBox.confirm(
      `确定要应用参数 ${suggestion.parameter} 的优化建议吗？\n当前值: ${suggestion.currentValue}\n建议值: ${suggestion.suggestedValue}`,
      '确认应用建议',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    // 这里应该调用实际的参数设置API
    ElMessage.success(`已应用参数 ${suggestion.parameter} 的优化建议`)
    
    // 记录应用建议的操作
    await db.addDecisionRecord({
      projectName: selectedProject.value || 'default',
      status: 'success',
      message: `应用优化建议: ${suggestion.parameter} = ${suggestion.suggestedValue}`
    })
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('应用建议失败:', error)
    }
  }
}

// 重置表单
const resetForm = () => {
  decisionFormRef.value.resetFields()
}

// 加载决策记录
const loadDecisionRecords = async () => {
  try {
    loading.records = true
    decisionRecords.value = await db.getDecisionRecords(50)
    
    // 更新项目列表
    const projects = [...new Set(decisionRecords.value.map(record => record.projectName))]
    projectList.value = projects.filter(project => project && project !== 'ALL')
    
  } catch (error) {
    console.error('加载决策记录失败:', error)
  } finally {
    loading.records = false
  }
}

// 加载决策结果(模拟数据)
const loadDecisionResults = async () => {
  try {
    loading.results = true
    
    // 模拟目标函数数据
    const now = Date.now()
    const objectiveMockData = []
    const parameterMockData = []
    
    for (let i = 0; i < 30; i++) {
      const timestamp = now - (30 - i) * 120000 // 每2分钟一个数据点
      
      objectiveMockData.push([
        timestamp,
        80 + Math.random() * 20 + Math.sin(i * 0.2) * 10
      ])
      
      parameterMockData.push({
        timestamp,
        temperature: 90 + Math.random() * 20 + Math.sin(i * 0.15) * 5,
        pressure: 1.5 + Math.random() * 1.0 + Math.cos(i * 0.1) * 0.3
      })
    }
    
    objectiveData.value = objectiveMockData
    parameterData.value = parameterMockData
    
    // 模拟优化建议
    optimizationSuggestions.value = [
      {
        parameter: '反应温度',
        currentValue: '95°C',
        suggestedValue: '102°C',
        improvement: 5.2,
        confidence: 85,
        description: '提高温度可增加反应速率'
      },
      {
        parameter: '反应压力',
        currentValue: '2.1 MPa',
        suggestedValue: '1.8 MPa',
        improvement: 3.1,
        confidence: 78,
        description: '降低压力可减少能耗'
      },
      {
        parameter: '催化剂浓度',
        currentValue: '0.5%',
        suggestedValue: '0.7%',
        improvement: 8.5,
        confidence: 92,
        description: '增加催化剂浓度可提高转化率'
      }
    ]
    
  } catch (error) {
    console.error('加载决策结果失败:', error)
  } finally {
    loading.results = false
  }
}

// 清空决策记录
const clearDecisionRecords = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有决策记录吗？此操作不可恢复。',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await db.clearStore('decisionRecords')
    await loadDecisionRecords()
    ElMessage.success('决策记录已清空')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空决策记录失败:', error)
    }
  }
}

onMounted(async () => {
  await loadDecisionRecords()
  await loadDecisionResults()
})
</script>

<style scoped>
.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.chart-container {
  height: 300px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
}

.el-input-group {
  display: flex;
}
</style>