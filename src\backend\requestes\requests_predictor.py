import httpx
import typer
from typing import Dict, List
import asyncio
from dataclasses import dataclass
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from api.requestbody import (
    RealtimePredictRequestBody,
    StopRealtimePredictRequestBody
)


@dataclass
class ProjectConfig:
    """项目配置数据类"""
    project_name: str  # 项目名称
    interval: int      # 预测间隔时间（秒）
    predictor_type: str  # 预测器类型
    
    @classmethod
    def from_string(cls, config_str: str) -> 'ProjectConfig':
        """从字符串解析项目配置
        
        Args:
            config_str: 格式为 "项目名|间隔时间|预测器类型" 的配置字符串
            
        Returns:
            ProjectConfig: 解析后的项目配置对象
        """
        parts = config_str.split('|')
        if len(parts) != 3:
            raise ValueError(f"配置格式错误，期望格式：项目名|间隔时间|预测器类型，实际：{config_str}")
        
        project_name, interval_str, predictor_type = parts
        try:
            interval = int(interval_str)
        except ValueError:
            raise ValueError(f"间隔时间必须是整数，实际：{interval_str}")
            
        return cls(
            project_name=project_name.strip(),
            interval=interval,
            predictor_type=predictor_type.strip()
        )


async def realtime_predict_async(
    base_url: str, 
    project_name: str, 
    interval: int,
    predictor_type: str
) -> Dict:
    """
    Send an async POST request to initiate realtime prediction.

    Args:
        base_url (str): The base URL of the API server.
        project_name (str): The name of the project to predict.
        interval (int): The interval for realtime prediction.
        predictor_type (str): The type of predictor to use.

    Returns:
        dict: The response from the server containing 'code' and 'message'.

    Raises:
        httpx.RequestError: If there's an error with the request.
    """
    url = f"{base_url}/rt/realtime_predict"
    payload = RealtimePredictRequestBody(
        project_name=project_name,
        interval=interval,
        predictor_type=predictor_type
    )

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url, json=payload.model_dump())
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            print(f"An error occurred while making the request: {e}")
            raise


def realtime_predict(
    base_url: str, 
    project_name: str, 
    interval: int,
    predictor_type: str
) -> Dict:
    """
    Send a synchronous POST request to initiate realtime prediction.

    Args:
        base_url (str): The base URL of the API server.
        project_name (str): The name of the project to predict.
        interval (int): The interval for realtime prediction.
        predictor_type (str): The type of predictor to use.

    Returns:
        dict: The response from the server containing 'code' and 'message'.

    Raises:
        httpx.RequestError: If there's an error with the request.
    """
    url = f"{base_url}/rt/realtime_predict"
    payload = RealtimePredictRequestBody(
        project_name=project_name,
        interval=interval,
        predictor_type=predictor_type
    )

    with httpx.Client() as client:
        try:
            response = client.post(url, json=payload.model_dump())
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            print(f"An error occurred while making the request: {e}")
            raise


async def stop_predict_async(base_url: str, project_name: str) -> Dict:
    """
    Send an async POST request to stop realtime prediction for a specific project.

    Args:
        base_url (str): The base URL of the API server.
        project_name (str): The name of the project to stop prediction.

    Returns:
        dict: The response from the server containing 'code' and 'message'.

    Raises:
        httpx.RequestError: If there's an error with the request.
    """
    url = f"{base_url}/stop_predict"
    payload = StopRealtimePredictRequestBody(project_name=project_name)

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url, json=payload.model_dump())
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            print(f"An error occurred while making the request: {e}")
            raise


def stop_predict(base_url: str, project_name: str) -> Dict:
    """
    Send a synchronous POST request to stop realtime prediction for a specific project.

    Args:
        base_url (str): The base URL of the API server.
        project_name (str): The name of the project to stop prediction.

    Returns:
        dict: The response from the server containing 'code' and 'message'.

    Raises:
        httpx.RequestError: If there's an error with the request.
    """
    url = f"{base_url}/stop_predict"
    payload = StopRealtimePredictRequestBody(project_name=project_name)

    with httpx.Client() as client:
        try:
            response = client.post(url, json=payload.model_dump())
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            print(f"An error occurred while making the request: {e}")
            raise


async def batch_predict_async(
    base_url: str,
    project_configs: List[ProjectConfig]
) -> List[Dict]:
    """
    批量异步执行实时预测请求
    
    Args:
        base_url: API服务器的基础URL
        project_configs: 项目配置列表
        
    Returns:
        List[Dict]: 所有预测请求的响应结果列表
        
    Raises:
        httpx.RequestError: 如果请求过程中发生错误
    """
    # 使用单个HTTP客户端复用连接
    async with httpx.AsyncClient() as client:
        # 创建所有预测任务
        tasks = []
        for config in project_configs:
            task = _single_predict_with_client(
                client, base_url, config
            )
            tasks.append(task)
        
        # 并发执行所有预测任务
        print(f"开始批量执行 {len(project_configs)} 个预测任务...")
        raw_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果和异常，确保返回正确的类型
        processed_results: List[Dict] = []
        for config, result in zip(project_configs, raw_results):
            if isinstance(result, Exception):
                print(f"项目 {config.project_name} 预测失败: {result}")
                processed_results.append({"error": str(result), "project_name": config.project_name})
            else:
                print(f"项目 {config.project_name} 预测成功: {result}")
                # result应该是Dict类型，但为了类型安全，我们进行检查
                if isinstance(result, dict):
                    processed_results.append(result)
                else:
                    processed_results.append({"error": "Invalid response type", "project_name": config.project_name})
    
    return processed_results


async def _single_predict_with_client(
    client: httpx.AsyncClient,
    base_url: str,
    config: ProjectConfig
) -> Dict:
    """
    使用指定的HTTP客户端执行单个预测请求
    
    Args:
        client: HTTP客户端实例
        base_url: API服务器的基础URL
        config: 项目配置
        
    Returns:
        Dict: 预测请求的响应结果
        
    Raises:
        httpx.RequestError: 如果请求过程中发生错误
    """
    url = f"{base_url}/rt/realtime_predict"
    payload = RealtimePredictRequestBody(
        project_name=config.project_name,
        interval=config.interval,
        predictor_type=config.predictor_type
    )
    
    try:
        response = await client.post(url, json=payload.model_dump())
        response.raise_for_status()
        return response.json()
    except httpx.RequestError as e:
        print(f"项目 {config.project_name} 请求失败: {e}")
        raise


async def run_predict_async(
    base_url: str,
    project_name: str,
    interval: int,
    predictor_type: str
) -> None:
    """
    Asynchronous function to run realtime prediction with command line interface.
    """
    try:
        result = await realtime_predict_async(base_url, project_name, interval, predictor_type)
        print(f"Prediction Response: {result}")
    except httpx.RequestError as e:
        print(f"Failed to start prediction: {e}")
        raise typer.Exit(code=1)


async def run_batch_predict_async(
    base_url: str,
    config_strings: List[str]
) -> None:
    """
    批量执行实时预测的异步函数
    
    Args:
        base_url: API服务器的基础URL
        config_strings: 配置字符串列表，格式为 "项目名|间隔时间|预测器类型"
    """
    try:
        # 解析配置字符串
        project_configs = []
        for config_str in config_strings:
            try:
                config = ProjectConfig.from_string(config_str)
                project_configs.append(config)
            except ValueError as e:
                print(f"配置解析错误: {e}")
                raise typer.Exit(code=1)
        
        # 执行批量预测
        results = await batch_predict_async(base_url, project_configs)
        
        # 统计结果
        success_count = sum(1 for result in results if "error" not in result)
        total_count = len(results)
        
        print(f"\n批量预测完成: {success_count}/{total_count} 个项目成功")
        
        # 如果有失败的项目，返回错误码
        if success_count < total_count:
            raise typer.Exit(code=1)
            
    except httpx.RequestError as e:
        print(f"批量预测失败: {e}")
        raise typer.Exit(code=1)


def run_predict_cli(
    project_name: str = typer.Option(..., "--project-name", "-p", help="Name of the project to predict"),
    base_url: str = typer.Option("http://localhost:8999", "--base-url", "-u", help="Base URL of the API server"),
    interval: int = typer.Option(30, "--interval", "-i", help="Prediction interval"),
    predictor_type: str = typer.Option("classic", "--predictor-type", "-t", help="Type of predictor to use")
) -> None:
    """
    Command line interface for realtime prediction.
    """
    asyncio.run(
        run_predict_async(
                base_url=base_url, 
                project_name=project_name, 
                interval=interval, 
                predictor_type=predictor_type
            )
        )


def run_batch_predict_cli(
    configs: List[str] = typer.Option(..., "--config", "-c", help="项目配置，格式：项目名|间隔时间|预测器类型"),
    base_url: str = typer.Option("http://localhost:8999", "--base-url", "-u", help="API服务器的基础URL")
) -> None:
    """
    批量实时预测的命令行接口
    
    示例用法：
    python requests_predictor.py batch \
        --config "项目1|60|classic_mo" \
        --config "项目2|30|classic" \
        --base-url "http://localhost:8999"
    """
    asyncio.run(
        run_batch_predict_async(
            base_url=base_url,
            config_strings=configs
        )
    )


# 创建Typer应用
app = typer.Typer(help="实时预测请求工具")

# 注册命令
app.command("single", help="单个项目预测")(run_predict_cli)
app.command("batch", help="批量项目预测")(run_batch_predict_cli)


def main():
    # 为了保持向后兼容，如果没有子命令则使用单个预测模式
    import sys
    if len(sys.argv) == 1 or (len(sys.argv) > 1 and sys.argv[1] not in ['single', 'batch', '--help', '-h']):
        # 没有子命令，使用原来的单个预测CLI
        typer.run(run_predict_cli)
    else:
        # 有子命令，使用新的应用
        app()


if __name__ == "__main__":
    main()
