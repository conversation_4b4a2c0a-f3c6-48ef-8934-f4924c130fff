# 心跳检测系统

## 概述

心跳检测系统是一个用于监控Industry AI应用程序健康状态的模块。它通过一个独立的Rust程序来检测系统的关键组件,并在异常时提供报警功能。

## 架构设计

```
┌─────────────────┐    ┌─────────────────┐
│   Python App   │    │  Rust Checker   │
│                 │    │                 │
│  - FastAPI      │    │  - 独立进程     │
│  - 生成心跳文件 │    │  - 检测心跳文件 │
│  - 系统检测     │    │  - 故障处理     │
│  - 提供API接口  │    │  - 告警机制     │
│                 │    │                 │
└─────────────────┘    └─────────────────┘
         │                       │
         │                       │
         └───────────────────────┘
                    │
            ┌───────▼───────┐
            │ heartbeat.json │
            │               │
            │ - timestamp   │
            │ - healthy     │
            │ - app_status  │
            │ - check_results│
            └───────────────┘
```

### 工作流程

1. **Python应用运行**:FastAPI应用启动后开始定期更新心跳文件
2. **生成心跳文件**:Python应用执行系统检测并将结果写入心跳文件
3. **Rust程序监控**:独立运行的Rust程序定期检测心跳文件状态
4. **故障检测**:Rust程序检测超时、异常状态并执行相应处理
5. **API接口**:Python应用通过HTTP接口提供心跳状态查询
6. **独立管理**:两个程序独立运行,互不依赖启动顺序

## 功能特性

### 1. 分离式检测机制
- **Python应用**:负责生成心跳文件,执行系统检测,提供API接口
- **Rust程序**:独立进程,专门监控心跳文件状态,处理故障和告警
- **解耦设计**:两个组件职责明确,独立运行,互不影响启动顺序

### 2. 多种检测点
- **数据库连接**: 检测数据库是否可访问
- **网络连接**: 检测网络连通性
- **文件系统**: 检测文件系统状态
- **自定义检测点**: 可扩展的检测逻辑

### 3. 实时监控
- 定期检测系统状态
- 异常时立即报警
- 提供详细的状态信息

## 配置说明

### 环境变量配置

在`.env`文件中添加以下配置:

```bash
# 心跳检测配置
HEARTBEAT_ENABLED=true                    # 是否启用心跳检测
HEARTBEAT_RUST_PATH=./heartbeat_checker/target/release/heartbeat_checker  # Rust程序路径
HEARTBEAT_FILE=./heartbeat.json          # 心跳文件路径
HEARTBEAT_CHECK_POINT=database           # 检测点类型
HEARTBEAT_CHECK_INTERVAL=30              # 检测间隔(秒)
HEARTBEAT_TIMEOUT=60                     # 超时阈值(秒)
```

### 检测点类型

- `database`: 数据库连接检测
- `network`: 网络连接检测
- `file_system`: 文件系统检测

## 安装和部署

### 1. 编译Rust程序

```bash
# 方法1: 使用提供的构建脚本
./scripts/build_heartbeat.sh

# 方法2: 手动编译
cd heartbeat_checker
cargo build --release
```

### 2. 配置环境变量

复制示例配置文件并修改:

```bash
cp .env.example .env
# 编辑.env文件,设置心跳检测相关配置
```

### 3. 启动心跳检测程序(独立运行)

**重要**: Rust心跳检测程序需要独立启动,不会随Python应用自动启动。

```bash
# 方法1: 前台运行(用于调试)
./scripts/start_heartbeat.sh

# 方法2: 后台运行(推荐用于生产环境)
./scripts/start_heartbeat_daemon.sh

# 查看状态
./scripts/status_heartbeat.sh

# 停止程序
./scripts/stop_heartbeat.sh
```

### 4. 启动Python应用

```bash
python app.py
```

**注意**: Python应用只会监控心跳文件,不会启动或管理Rust程序。

## API接口

### 1. 健康检查接口

**GET** `/health`

返回系统的基本健康状态。

**响应示例**:
```json
{
  "status": "ok",
  "message": "系统运行正常",
  "last_check": "2024-01-01T12:00:00",
  "timestamp": "2024-01-01T12:00:00"
}
```

### 2. 详细心跳状态接口

**GET** `/heartbeat/status`

返回详细的心跳检测状态信息。

**响应示例**:
```json
{
  "enabled": true,
  "healthy": true,
  "last_check": "2024-01-01T12:00:00",
  "error_message": null,
  "config": {
    "check_point": "database",
    "check_interval": 30,
    "timeout_threshold": 60
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

## 监控和告警

### 1. 状态监控

系统会定期检查心跳文件的更新时间:
- 如果超过配置的超时阈值,系统会标记为不健康
- 健康检查接口会返回503状态码

### 2. 日志记录

心跳检测的状态会记录在应用日志中:
```
[INFO] 心跳检测已启动 - 检测点: database, 间隔: 30秒
[WARNING] 心跳检测超时,系统可能存在异常
[ERROR] Rust心跳程序启动失败,使用Python备用方案
```

## 故障排除

### 1. Rust程序无法启动

**问题**: Rust程序编译失败或无法执行

**解决方案**:
1. 检查Rust是否正确安装:`cargo --version`
2. 重新编译程序:`./scripts/build_heartbeat.sh`
3. 检查可执行文件权限
4. 系统会自动回退到Python备用方案

### 2. 心跳文件无法创建

**问题**: 心跳文件路径不存在或无写入权限

**解决方案**:
1. 检查文件路径是否正确
2. 确保应用有写入权限
3. 创建必要的目录结构

### 3. 检测点配置错误

**问题**: 不支持的检测点类型

**解决方案**:
1. 检查`HEARTBEAT_CHECK_POINT`配置
2. 确保使用支持的检测点类型
3. 查看Rust程序日志了解详细错误

## 扩展开发

### 1. 添加新的检测点

在Rust程序中添加新的检测逻辑:

```rust
fn check_point_status(point: &str) -> String {
    match point {
        "database" => check_database_connection(),
        "network" => check_network_connection(),
        "file_system" => check_file_system(),
        "custom_service" => check_custom_service(), // 新增检测点
        _ => {
            eprintln!("Unknown check point: {}", point);
            "unknown".to_string()
        }
    }
}

fn check_custom_service() -> String {
    // 实现自定义服务检测逻辑
    "ok".to_string()
}
```

### 2. 自定义检测逻辑

可以根据具体需求修改各个检测函数的实现,例如:
- 连接特定的数据库
- 检查特定的网络服务
- 验证特定的文件或目录

## 性能考虑

- Rust程序资源占用极低,适合长期运行
- 检测间隔可根据需要调整,建议不低于10秒
- 心跳文件大小很小,对磁盘IO影响微乎其微
- Python备用方案会增加一定的资源开销

## 安全考虑

- 心跳文件包含系统状态信息,建议设置适当的文件权限
- Rust程序以应用相同的用户权限运行
- 检测逻辑应避免暴露敏感信息