from industrytslib import (
    get_realtime_predictor, 
    get_decision_agent, 
    get_trainer
)


def build_tasks(task_type: str, **kwargs):
    """ 
    根据任务类型和任务名称构建任务
    """
    match task_type:
        case "trainer":
            return get_trainer(**kwargs)
        case "predictor":
            return get_realtime_predictor(**kwargs)
        case "decision_agent":
            return get_decision_agent(**kwargs)
        case _:
            raise ValueError(f"Invalid task type: {task_type}")
        