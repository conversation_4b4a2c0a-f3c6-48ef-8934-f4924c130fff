# justfile for industrytslib installation
# 智能识别并安装libs文件夹中的industrytslib wheel文件

# Default recipe
default: install

# Install industrytslib wheel file with dependencies
install:
    #!/usr/bin/env bash
    set -euo pipefail
    
    # 设置脚本目录
    LIBS_DIR="$(pwd)"
    
    echo "正在搜索libs文件夹中的industrytslib wheel文件..."
    
    # 查找所有industrytslib的wheel文件
    WHL_FILES=$(find "$LIBS_DIR" -name "industrytslib-*.whl" -type f)
    
    if [ -z "$WHL_FILES" ]; then
        echo "错误: 在 $LIBS_DIR 中未找到industrytslib的wheel文件"
        echo "请确保libs文件夹中包含industrytslib-*.whl文件"
        exit 1
    fi
    
    # 如果找到多个文件,选择版本最高的
    if [ $(echo "$WHL_FILES" | wc -l) -gt 1 ]; then
        echo "找到多个industrytslib wheel文件,正在选择最高版本..."
        echo "$WHL_FILES" | while read file; do
            echo "  - $(basename "$file")"
        done
        
        # 使用sort -V进行版本号排序,选择最高版本
        WHL_FILE=$(echo "$WHL_FILES" | sort -V | tail -1)
    else
        WHL_FILE="$WHL_FILES"
    fi
    
    # 获取文件名(不含路径)
    WHL_FILENAME=$(basename "$WHL_FILE")
    echo "选择安装wheel文件: $WHL_FILENAME"
    
    PIP_CMD="pip"
    
    # 安装wheel文件及其依赖
    echo "正在安装 $WHL_FILENAME 及其依赖组合..."
    echo "依赖组合: async, data_storage, data_vis, ml"
    
    # 执行安装命令
    $PIP_CMD install "${WHL_FILENAME}[async,data_storage,data_vis,ml]" --force --no-deps
    
    if [ $? -eq 0 ]; then
        echo "✅ 安装成功！"
        echo "已安装的包信息:"
        $PIP_CMD show industrytslib
    else
        echo "❌ 安装失败,请检查错误信息"
        exit 1
    fi

# Show available wheel files
list-wheels:
    @echo "可用的wheel文件:"
    @find . -name "industrytslib-*.whl" -type f || echo "未找到wheel文件"

# Clean installation (uninstall industrytslib)
clean:
    @echo "卸载industrytslib..."
    pip uninstall industrytslib -y || echo "industrytslib未安装或卸载失败"

# Show help
help:
    @echo "可用命令:"
    @echo "  just install      - 安装industrytslib及其依赖"
    @echo "  just list-wheels  - 列出可用的wheel文件"
    @echo "  just clean        - 卸载industrytslib"
    @echo "  just help         - 显示此帮助信息"