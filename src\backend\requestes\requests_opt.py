import httpx
import typer
from typing import Dict, List
import asyncio
from dataclasses import dataclass
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from api.requestbody import (
    DecisionMakingRequestBody,
    StopDecisionMakingRequestBody
)


@dataclass
class OptConfig:
    """决策优化配置数据类"""
    project_name: str  # 项目名称
    interval: int      # 决策优化间隔时间（秒）
    decision_type: str # 决策类型
    
    @classmethod
    def from_string(cls, config_str: str) -> 'OptConfig':
        """从字符串解析决策优化配置
        
        Args:
            config_str: 格式为 "项目名|间隔时间|决策类型" 的配置字符串
            
        Returns:
            OptConfig: 解析后的决策优化配置对象
        """
        parts = config_str.split('|')
        if len(parts) != 3:
            raise ValueError(f"配置格式错误，期望格式：项目名|间隔时间|决策类型，实际：{config_str}")
        
        project_name, interval_str, decision_type = parts
        try:
            interval = int(interval_str)
        except ValueError:
            raise ValueError(f"间隔时间必须是整数，实际：{interval_str}")
            
        return cls(
            project_name=project_name.strip(),
            interval=interval,
            decision_type=decision_type.strip()
        )


async def decision_making_async(
    base_url: str, 
    project_name: str, 
    interval: int,
    decision_type: str = "basic"
) -> Dict:
    """
    Send an async POST request to initiate decision making.

    Args:
        base_url (str): The base URL of the API server.
        project_name (str): The name of the project for decision making.
        interval (int): The interval for decision making.
        decision_type (str): The type of decision making to use.

    Returns:
        dict: The response from the server containing 'code' and 'message'.

    Raises:
        httpx.RequestError: If there's an error with the request.
    """
    url = f"{base_url}/opt/decision_making"
    payload = DecisionMakingRequestBody(
        project_name=project_name,
        interval=interval,
        decision_type=decision_type
    )

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url, json=payload.model_dump())
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            print(f"An error occurred while making the request: {e}")
            raise


def decision_making(
    base_url: str, 
    project_name: str, 
    interval: int,
    decision_type: str = "basic"
) -> Dict:
    """
    Send a synchronous POST request to initiate decision making.

    Args:
        base_url (str): The base URL of the API server.
        project_name (str): The name of the project for decision making.
        interval (int): The interval for decision making.
        decision_type (str): The type of decision making to use.

    Returns:
        dict: The response from the server containing 'code' and 'message'.

    Raises:
        httpx.RequestError: If there's an error with the request.
    """
    url = f"{base_url}/decision_making"
    payload = DecisionMakingRequestBody(
        project_name=project_name,
        interval=interval,
        decision_type=decision_type
    )

    with httpx.Client() as client:
        try:
            response = client.post(url, json=payload.model_dump())
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            print(f"An error occurred while making the request: {e}")
            raise


async def stop_decision_making_async(base_url: str, project_name: str) -> Dict:
    """
    Send an async POST request to stop decision making for a specific project.

    Args:
        base_url (str): The base URL of the API server.
        project_name (str): The name of the project to stop decision making.

    Returns:
        dict: The response from the server containing 'code' and 'message'.

    Raises:
        httpx.RequestError: If there's an error with the request.
    """
    url = f"{base_url}/stop_decision_making"
    payload = StopDecisionMakingRequestBody(project_name=project_name)

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url, json=payload.model_dump())
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            print(f"An error occurred while making the request: {e}")
            raise


def stop_decision_making(base_url: str, project_name: str) -> Dict:
    """
    Send a synchronous POST request to stop decision making for a specific project.

    Args:
        base_url (str): The base URL of the API server.
        project_name (str): The name of the project to stop decision making.

    Returns:
        dict: The response from the server containing 'code' and 'message'.

    Raises:
        httpx.RequestError: If there's an error with the request.
    """
    url = f"{base_url}/stop_decision_making"
    payload = StopDecisionMakingRequestBody(project_name=project_name)

    with httpx.Client() as client:
        try:
            response = client.post(url, json=payload.model_dump())
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            print(f"An error occurred while making the request: {e}")
            raise


async def run_decision_making_async(
    base_url: str,
    project_name: str,
    interval: int,
    decision_type: str = "basic"
) -> None:
    """
    Asynchronous function to run decision making with command line interface.
    """
    try:
        result = await decision_making_async(base_url, project_name, interval, decision_type)
        print(f"Decision Making Response: {result}")
    except httpx.RequestError as e:
        print(f"Failed to start decision making: {e}")
        raise typer.Exit(code=1)


async def batch_decision_making_async(
    base_url: str,
    opt_configs: List[OptConfig]
) -> List[Dict]:
    """
    批量异步执行决策优化请求
    
    Args:
        base_url: API服务器的基础URL
        opt_configs: 决策优化配置列表
        
    Returns:
        List[Dict]: 所有决策优化请求的响应结果列表
        
    Raises:
        httpx.RequestError: 如果请求过程中发生错误
    """
    # 使用单个HTTP客户端复用连接
    async with httpx.AsyncClient() as client:
        # 创建所有决策优化任务
        tasks = []
        for config in opt_configs:
            task = _single_decision_making_with_client(
                client, base_url, config
            )
            tasks.append(task)
        
        # 并发执行所有决策优化任务
        print(f"开始批量执行 {len(opt_configs)} 个决策优化任务...")
        raw_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果和异常，确保返回正确的类型
        processed_results: List[Dict] = []
        for config, result in zip(opt_configs, raw_results):
            if isinstance(result, Exception):
                print(f"项目 {config.project_name} 决策优化失败: {result}")
                processed_results.append({"error": str(result), "project_name": config.project_name})
            else:
                print(f"项目 {config.project_name} 决策优化成功: {result}")
                # result应该是Dict类型，但为了类型安全，我们进行检查
                if isinstance(result, dict):
                    processed_results.append(result)
                else:
                    processed_results.append({"error": "Invalid response type", "project_name": config.project_name})
    
    return processed_results


async def _single_decision_making_with_client(
    client: httpx.AsyncClient,
    base_url: str,
    config: OptConfig
) -> Dict:
    """
    使用指定的HTTP客户端执行单个决策优化请求
    
    Args:
        client: HTTP客户端实例
        base_url: API服务器的基础URL
        config: 决策优化配置
        
    Returns:
        Dict: 决策优化请求的响应结果
        
    Raises:
        httpx.RequestError: 如果请求过程中发生错误
    """
    url = f"{base_url}/opt/decision_making"
    payload = DecisionMakingRequestBody(
        project_name=config.project_name,
        interval=config.interval,
        decision_type=config.decision_type
    )
    
    try:
        response = await client.post(url, json=payload.model_dump())
        response.raise_for_status()
        return response.json()
    except httpx.RequestError as e:
        print(f"项目 {config.project_name} 请求失败: {e}")
        raise


async def run_batch_decision_making_async(
    base_url: str,
    config_strings: List[str]
) -> None:
    """
    批量执行决策优化的异步函数
    
    Args:
        base_url: API服务器的基础URL
        config_strings: 配置字符串列表，格式为 "项目名|间隔时间|决策类型"
    """
    try:
        # 解析配置字符串
        opt_configs = []
        for config_str in config_strings:
            try:
                config = OptConfig.from_string(config_str)
                opt_configs.append(config)
            except ValueError as e:
                print(f"配置解析错误: {e}")
                raise typer.Exit(code=1)
        
        # 执行批量决策优化
        results = await batch_decision_making_async(base_url, opt_configs)
        
        # 统计结果
        success_count = sum(1 for result in results if "error" not in result)
        total_count = len(results)
        
        print(f"\n批量决策优化完成: {success_count}/{total_count} 个项目成功")
        
        # 如果有失败的项目，返回错误码
        if success_count < total_count:
            raise typer.Exit(code=1)
            
    except httpx.RequestError as e:
        print(f"批量决策优化失败: {e}")
        raise typer.Exit(code=1)


def run_decision_making_cli(
    project_name: str = typer.Option(..., "--project-name", "-p", help="Name of the project for decision making"),
    base_url: str = typer.Option("http://localhost:8999", "--base-url", "-u", help="Base URL of the API server"),
    interval: int = typer.Option(30, "--interval", "-i", help="Decision making interval"),
    decision_type: str = typer.Option("basic", "--decision-type", "-t", help="Type of decision making to use")
) -> None:
    """
    Command line interface for decision making.
    """
    asyncio.run(run_decision_making_async(base_url, project_name, interval, decision_type))


def run_batch_decision_making_cli(
    configs: List[str] = typer.Option(..., "--config", "-c", help="决策优化配置，格式：项目名|间隔时间|决策类型"),
    base_url: str = typer.Option("http://localhost:8999", "--base-url", "-u", help="API服务器的基础URL")
) -> None:
    """
    批量决策优化的命令行接口
    
    示例用法：
    python requests_opt.py batch \
        --config "项目1|600|basic" \
        --config "项目2|300|classic" \
        --base-url "http://localhost:8999"
    """
    asyncio.run(
        run_batch_decision_making_async(
            base_url=base_url,
            config_strings=configs
        )
    )


# 创建Typer应用
app = typer.Typer(help="决策优化工具")

# 注册单个决策优化命令
app.command("single", help="执行单个决策优化")(run_decision_making_cli)

# 注册批量决策优化命令
app.command("batch", help="批量执行决策优化")(run_batch_decision_making_cli)

if __name__ == "__main__":
    app()
