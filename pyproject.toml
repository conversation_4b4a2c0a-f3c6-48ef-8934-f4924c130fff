[project]
name = "industryai"
version = "2025.07.25"
description = "Industry AI Server"
authors = [
    { name = "<PERSON><PERSON>", email = "bah<PERSON><PERSON><PERSON>@gmail.com" }
]
readme = "README.md"
keywords = [
    "Process Industry", 
    "Industry Intelligence",
    "Long-term Time Series Forecasting",
    "Process Monitoring",
    "Industry Operational Parameter Optimization",
]
requires-python = ">=3.11"
dependencies = [
    "apscheduler==3.11.0",
    "httpx>=0.28.1",
    # 第一次使用uv sync 不加 mamba 相关的库
    # "industrytslib[async, data_storage, data_vis, ml]",
    "industrytslib[async, data_storage, data_vis, ml, mamba]",
    "pydantic-settings>=2.7.0",
    "typer>=0.16.0",
    "uvicorn>=0.34.0",
    # pip install torch==2.5.1 torchvision==0.20.1 torchaudio==2.5.1
    "torch>=2.7.1",
    "triton>=3.1.0; sys_platform == 'linux'",
    "setuptools>=80.8.0",
    "fastapi-guard>=3.0.0",
]

[tool.uv]
# index-url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"  # 清华源
index-url = "https://mirrors.aliyun.com/pypi/simple"  # 阿里源
no-build-isolation-package = ["causal-conv1d", "mamba-ssm", "flash-attn"]
resolution = "highest"
compile-bytecode = true
link-mode = "copy"

[tool.uv.sources]
default = [{ url = "https://mirrors.aliyun.com/pypi/simple"}]
industrytslib = { path = "libs/industrytslib-3.2.4-py3-none-any.whl" }
torch = [
    { index = "pytorch-gpu-win", marker = "sys_platform == 'win32'" },
    { index = "pytorch-gpu-linux", marker = "sys_platform == 'linux'" },
    { index = "pytorch-cpu", marker = "sys_platform == 'darwin'" },
]
torchvision = [
    { index = "pytorch-gpu-win", marker = "sys_platform == 'win32'" },
    { index = "pytorch-gpu-linux", marker = "sys_platform == 'linux'" },
    { index = "pytorch-cpu", marker = "sys_platform == 'darwin'" },
]
torchaudio = [
    { index = "pytorch-gpu-win", marker = "sys_platform == 'win32'" },
    { index = "pytorch-gpu-linux", marker = "sys_platform == 'linux'" },
    { index = "pytorch-cpu", marker = "sys_platform == 'darwin'" },
]


[[tool.uv.index]]
name = "pytorch-gpu-win"
url = "https://download.pytorch.org/whl/cu128"
explicit = true

[[tool.uv.index]]
name = "pytorch-gpu-linux"
# url = "https://mirrors.aliyun.com/pypi/simple"
url = "https://download.pytorch.org/whl/cu128"  # 50系显卡启用
explicit = true

[[tool.uv.index]]
name = "pytorch-cpu"
url = "https://download.pytorch.org/whl/cpu"
explicit = true

[dependency-groups]
dev = [
    "celery>=5.4.0",
    "dockerpyze>=2.1.1",
    "dynaconf>=3.2.10",
    "flower>=2.0.1",
    "funboost>=47.9",
    "furo>=2024.8.6",
    "mkdocs>=1.6.1",
    "mkdocs-material>=9.5.49",
    "mkdocstrings>=0.27.0",
    "mkdocstrings-python>=1.12.2",
    "nuitka>=2.7.7", # python 3.11仅支持到2.7.7
    "pyarmor>=9.1.7",
    "pyarmor-webui>=2.6",
    "pytest>=8.3.5",
    "sphinx>=8.1.3",
    "sqlalchemy>=2.0.38",
    "toml>=0.10.2",
    "mkdocs-git-revision-date-localized-plugin>=1.2.5",
    "mkdocs-minify-plugin>=0.8.0",
    "mkdocs-awesome-pages-plugin>=2.9.0",
    "mkdocs-macros-plugin>=1.0.5",
    # "mkdocs-pdf-export-plugin>=0.5.10", # 暂时不使用,因为需要安装wkhtmltopdf,在windows上安装比较麻烦
]

[tool.dpy]
# ! 由于权限问题暂时不可用
entrypoint = "uv run main.py --host 0.0.0.0 --port 8999"
