import uuid
import platform
import hashlib
from datetime import datetime, timedelta
import typer
from pathlib import Path
import shutil

app = typer.Typer()

def get_hardware_id():
    """获取硬件标识"""
    # 获取系统信息
    system_info = platform.node() + platform.platform() + platform.processor()
    # 获取MAC地址
    mac = uuid.getnode()
    # 组合信息并生成哈希
    machine_id = f"{system_info}{mac}"
    return hashlib.sha256(machine_id.encode()).hexdigest()

def init_project():
    """初始化 PyArmor 项目"""
    try:
        from pyarmor.pyarmor import main as pyarmor_main
        # 确保 .pyarmor 目录存在
        if not Path('.pyarmor').exists():
            # 初始化项目
            pyarmor_main(['init'])
            # 配置项目
            pyarmor_main(['config', '--runtime-mode', '1'])
    except Exception as e:
        typer.secho(f"初始化项目失败: {str(e)}", fg=typer.colors.RED)
        raise e

@app.command()
def generate(
    days: int = typer.Option(365, help="授权天数"),
    output_dir: str = typer.Option("licenses", help="授权文件输出目录"),
    hardware_id: str = typer.Option(None, help="硬件标识,不指定则不进行硬件绑定"),
    trial: bool = typer.Option(False, help="是否生成试用版授权")
):
    """生成授权文件"""
    try:
        # 初始化项目
        init_project()
        
        # 确保输出目录存在
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # 计算过期时间
        expire_date = datetime.now() + timedelta(days=days)
        
        from pyarmor.pyarmor import main as pyarmor_main
        
        # 生成许可证
        cmd = ['licenses']
        
        if hardware_id:
            cmd.extend(['--bind-machine', hardware_id])
            
        if trial:
            cmd.extend(['--disable-restrict-mode', '0'])
            
        cmd.extend([
            '--expired', expire_date.strftime('%Y-%m-%d'),
            '-O', output_dir,
            'main.py'
        ])
        
        pyarmor_main(cmd)
        
        typer.secho(f"授权文件已生成在目录: {output_dir}", fg=typer.colors.GREEN)
        typer.secho(f"过期时间: {expire_date.strftime('%Y-%m-%d')}", fg=typer.colors.GREEN)
        if hardware_id:
            typer.secho(f"硬件绑定ID: {hardware_id}", fg=typer.colors.GREEN)
        if trial:
            typer.secho("已生成试用版授权", fg=typer.colors.YELLOW)
        
    except Exception as e:
        typer.secho(f"生成授权文件失败: {str(e)}", fg=typer.colors.RED)

@app.command()
def pack(
    trial_days: int = typer.Option(30, help="试用版天数,0表示不包含试用版"),
    output_dir: str = typer.Option("dist", help="输出目录")
):
    """打包加密程序"""
    try:
        # 初始化项目
        init_project()
        
        from pyarmor.pyarmor import main as pyarmor_main
        
        # 清理输出目录
        if Path(output_dir).exists():
            shutil.rmtree(output_dir)
        
        # 基础打包命令
        cmd = ['build']
        
        if trial_days > 0:
            expire_date = datetime.now() + timedelta(days=trial_days)
            cmd.extend([
                '--expired', expire_date.strftime('%Y-%m-%d'),
                '--disable-restrict-mode', '0'
            ])
            
        cmd.extend([
            '--output', output_dir,
            'main.py'
        ])
        
        # 执行打包
        pyarmor_main(cmd)
        
        typer.secho(f"程序打包完成！输出目录: {output_dir}", fg=typer.colors.GREEN)
        if trial_days > 0:
            typer.secho(f"包含{trial_days}天试用期", fg=typer.colors.GREEN)
        
    except Exception as e:
        typer.secho(f"打包程序失败: {str(e)}", fg=typer.colors.RED)

@app.command()
def get_id():
    """获取当前机器的硬件ID"""
    try:
        hardware_id = get_hardware_id()
        typer.secho(f"当前机器的硬件ID: {hardware_id}", fg=typer.colors.GREEN)
        return hardware_id
    except Exception as e:
        typer.secho(f"获取硬件ID失败: {str(e)}", fg=typer.colors.RED)
        return None

if __name__ == "__main__":
    app() 