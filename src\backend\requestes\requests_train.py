import httpx
import typer
from typing import Dict, Any, List
import asyncio
import sys
import os
from dataclasses import dataclass

# 动态添加路径以支持直接运行脚本
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(current_dir)
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

from api.requestbody import (
    TimeUnit,
    TrainModelRequestBody,
    TrainModelListRequestBody,
    StopTrainRequestBody
)


@dataclass
class TrainConfig:
    """
    训练配置数据类
    
    Attributes:
        project_name: 项目名称
        interval: 训练间隔时间（秒）
        algorithm_type: 算法类型
    """
    project_name: str
    interval: int
    algorithm_type: str
    
    @classmethod
    def from_string(cls, config_str: str) -> 'TrainConfig':
        """
        从配置字符串解析训练配置
        
        Args:
            config_str: 配置字符串，格式为 "项目名|间隔时间|算法类型"
            
        Returns:
            TrainConfig: 解析后的训练配置对象
            
        Raises:
            ValueError: 如果配置字符串格式不正确
        """
        parts = config_str.split('|')
        if len(parts) != 3:
            raise ValueError(f"配置格式错误，期望格式：项目名|间隔时间|算法类型，实际：{config_str}")
        
        project_name, interval_str, algorithm_type = parts
        
        try:
            interval = int(interval_str)
        except ValueError:
            raise ValueError(f"间隔时间必须是整数，实际：{interval_str}")
        
        return cls(
            project_name=project_name.strip(),
            interval=interval,
            algorithm_type=algorithm_type.strip()
        )


async def train_model_async(
    base_url: str, 
    project_name: str, 
    interval_hours: int | None = None,
    interval: int | None = None, 
    algorithm_type: str = "classic"
) -> Dict:
    """
    Send an async POST request to the /train endpoint to initiate model training.

    Args:
        base_url (str): The base URL of the API server.
        project_name (str): The name of the project to train.
        interval_hours (int, optional): The interval in hours for scheduling the training job.
        interval (int, optional): The interval in seconds for scheduling the training job (for backward compatibility).
        algorithm_type (str): The type of algorithm to use for training.

    Returns:
        dict: The response from the server containing 'code' and 'message'.

    Raises:
        httpx.RequestError: If there's an error with the request.
    """
    url = f"{base_url}/train/train"
    
    # 构建请求payload,优先使用interval_hours
    payload_data: Dict[str, Any] = {
        "project_name": project_name,
        "algorithm_type": algorithm_type
    }
    
    if interval_hours is not None:
        payload_data["interval_hours"] = interval_hours
    elif interval is not None:
        payload_data["interval"] = interval
        payload_data["time_unit"] = TimeUnit.SECONDS
    else:
        raise ValueError("必须提供 interval_hours 或 interval 参数")
    
    payload = TrainModelRequestBody(**payload_data)

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url, json=payload.model_dump())
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            print(f"An error occurred while making the request: {e}")
            raise


def train_model(
    base_url: str, 
    project_name: str, 
    interval_hours: int | None = None,
    interval: int | None = None, 
    algorithm_type: str = "classic"
) -> Dict:
    """
    Send a synchronous POST request to the /train endpoint to initiate model training.

    Args:
        base_url (str): The base URL of the API server.
        project_name (str): The name of the project to train.
        interval_hours (int, optional): The interval in hours for scheduling the training job.
        interval (int, optional): The interval in seconds for scheduling the training job (for backward compatibility).
        algorithm_type (str): The type of algorithm to use for training.

    Returns:
        dict: The response from the server containing 'code' and 'message'.

    Raises:
        httpx.RequestError: If there's an error with the request.
    """
    url = f"{base_url}/train/train"
    
    # 构建请求payload,优先使用interval_hours
    payload_data: Dict[str, Any] = {
        "project_name": project_name,
        "algorithm_type": algorithm_type
    }
    
    if interval_hours is not None:
        payload_data["interval_hours"] = interval_hours
    elif interval is not None:
        payload_data["interval"] = interval
        payload_data["time_unit"] = TimeUnit.SECONDS
    else:
        raise ValueError("必须提供 interval_hours 或 interval 参数")
    
    payload = TrainModelRequestBody(**payload_data)

    with httpx.Client() as client:
        try:
            response = client.post(url, json=payload.model_dump())
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            print(f"An error occurred while making the request: {e}")
            raise


async def train_model_list_async(
    base_url: str, 
    project_name_list: list[str], 
    interval_hours_list: list[int] | None = None,
    interval_list: list[int] | None = None,
    algorithm_type_list: list[str] | None = None
) -> Dict:
    """
    Send an async POST request to the /train_list endpoint to initiate model training for multiple projects.

    Args:
        base_url (str): The base URL of the API server.
        project_name_list (list[str]): List of project names to train.
        interval_hours_list (list[int], optional): List of intervals in hours for scheduling the training jobs.
        interval_list (list[int], optional): List of intervals in seconds for scheduling the training jobs (for backward compatibility).
        algorithm_type_list (list[str], optional): List of algorithm types to use for training.

    Returns:
        dict: The response from the server containing 'code' and 'message'.

    Raises:
        httpx.RequestError: If there's an error with the request.
    """
    url = f"{base_url}/train/train_list"
    
    # 设置默认算法类型
    if algorithm_type_list is None:
        algorithm_type_list = ["classic"] * len(project_name_list)
    
    # 构建请求payload
    payload_data: Dict[str, Any] = {
        "project_name_list": project_name_list,
        "algorithm_type_list": algorithm_type_list
    }
    
    if interval_hours_list is not None:
        payload_data["interval_hours_list"] = interval_hours_list
    elif interval_list is not None:
        payload_data["interval_list"] = interval_list
        payload_data["time_unit"] = TimeUnit.SECONDS
    else:
        raise ValueError("必须提供 interval_hours_list 或 interval_list 参数")
    
    payload = TrainModelListRequestBody(**payload_data)

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url, json=payload.model_dump())
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            print(f"An error occurred while making the request: {e}")
            raise

def train_model_list(
    base_url: str, 
    project_name_list: list[str], 
    interval_hours_list: list[int] | None = None,
    interval_list: list[int] | None = None,
    algorithm_type_list: list[str] | None = None
) -> Dict:
    """
    Send a synchronous POST request to the /train_list endpoint to initiate model training for multiple projects.

    Args:
        base_url (str): The base URL of the API server.
        project_name_list (list[str]): List of project names to train.
        interval_hours_list (list[int], optional): List of intervals in hours for scheduling the training jobs.
        interval_list (list[int], optional): List of intervals in seconds for scheduling the training jobs (for backward compatibility).
        algorithm_type_list (list[str], optional): List of algorithm types to use for training.

    Returns:
        dict: The response from the server containing 'code' and 'message'.

    Raises:
        httpx.RequestError: If there's an error with the request.
    """
    url = f"{base_url}/train/train_list"
    
    # 设置默认算法类型
    if algorithm_type_list is None:
        algorithm_type_list = ["classic"] * len(project_name_list)
    
    # 构建请求payload
    payload_data: Dict[str, Any] = {
        "project_name_list": project_name_list,
        "algorithm_type_list": algorithm_type_list
    }
    
    if interval_hours_list is not None:
        payload_data["interval_hours_list"] = interval_hours_list
    elif interval_list is not None:
        payload_data["interval_list"] = interval_list
        payload_data["time_unit"] = TimeUnit.SECONDS
    else:
        raise ValueError("必须提供 interval_hours_list 或 interval_list 参数")
    
    payload = TrainModelListRequestBody(**payload_data)

    with httpx.Client() as client:
        try:
            response = client.post(url, json=payload.model_dump())
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            print(f"An error occurred while making the request: {e}")
            raise


async def stop_train_async(base_url: str, project_name: str) -> Dict:
    """
    Send an async POST request to stop training for a specific project.

    Args:
        base_url (str): The base URL of the API server.
        project_name (str): The name of the project to stop training.

    Returns:
        dict: The response from the server containing 'code' and 'message'.

    Raises:
        httpx.RequestError: If there's an error with the request.
    """
    url = f"{base_url}/train/stop_train"
    payload = StopTrainRequestBody(project_name=project_name)

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url, json=payload.model_dump())
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            print(f"An error occurred while making the request: {e}")
            raise


def stop_train(base_url: str, project_name: str) -> Dict:
    """
    Send a synchronous POST request to stop training for a specific project.

    Args:
        base_url (str): The base URL of the API server.
        project_name (str): The name of the project to stop training.

    Returns:
        dict: The response from the server containing 'code' and 'message'.

    Raises:
        httpx.RequestError: If there's an error with the request.
    """
    url = f"{base_url}/train/stop_train"
    payload = StopTrainRequestBody(project_name=project_name)

    with httpx.Client() as client:
        try:
            response = client.post(url, json=payload.model_dump())
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            print(f"An error occurred while making the request: {e}")
            raise

async def run_train_async(
    base_url: str,
    project_name: str,
    interval_hours: int | None = None,
    interval: int | None = None,
    algorithm_type: str = "classic"
) -> None:
    """
    Asynchronous function to run model training with command line interface.
    """
    try:
        result = await train_model_async(base_url, project_name, interval_hours, interval, algorithm_type)
        print(f"Training Response: {result}")
    except httpx.RequestError as e:
        print(f"Failed to train model: {e}")
        raise typer.Exit(code=1)


async def run_batch_train_async(
    base_url: str,
    config_strings: List[str]
) -> None:
    """
    批量执行训练的异步函数
    
    Args:
        base_url: API服务器的基础URL
        config_strings: 配置字符串列表，格式为 "项目名|间隔时间|算法类型"
    """
    try:
        # 解析配置字符串
        train_configs = []
        for config_str in config_strings:
            try:
                config = TrainConfig.from_string(config_str)
                train_configs.append(config)
            except ValueError as e:
                print(f"配置解析错误: {e}")
                raise typer.Exit(code=1)
        
        # 构建批量训练的参数列表
        project_name_list = [config.project_name for config in train_configs]
        interval_list = [config.interval for config in train_configs]
        algorithm_type_list = [config.algorithm_type for config in train_configs]
        
        # 执行批量训练
        print(f"开始批量执行 {len(train_configs)} 个训练任务...")
        result = await train_model_list_async(
            base_url=base_url,
            project_name_list=project_name_list,
            interval_list=interval_list,
            algorithm_type_list=algorithm_type_list
        )
        
        print(f"批量训练响应: {result}")
        
        # 检查响应状态
        if result.get('code') != 200:
            print(f"批量训练失败: {result.get('message', '未知错误')}")
            raise typer.Exit(code=1)
        else:
            print(f"批量训练成功: {len(train_configs)} 个项目已启动训练")
            
    except httpx.RequestError as e:
        print(f"批量训练失败: {e}")
        raise typer.Exit(code=1)

def run_train_cli(
    project_name: str = typer.Option(..., "--project-name", "-p", help="Name of the project to train"),
    base_url: str = typer.Option("http://localhost:8999", "--base-url", "-u", help="Base URL of the API server"),
    interval_hours: int | None = typer.Option(None, "--interval-hours", "-ih", help="Training interval in hours"),
    interval: int | None = typer.Option(None, "--interval", "-i", help="Training interval in seconds (for backward compatibility)"),
    algorithm_type: str = typer.Option("classic", "--algorithm-type", "-a", help="Type of algorithm to use")
) -> None:
    """
    Command line interface for training models.
    支持小时单位和秒单位的训练间隔设置。
    
    示例:
        # 使用小时单位(推荐)
        python requests_train.py single --project-name "test_project" --interval-hours 2 --algorithm-type "classic"
        
        # 使用秒单位(向后兼容)
        python requests_train.py single --project-name "test_project" --interval 7200 --algorithm-type "classic"
    """
    # 检查参数
    if interval_hours is None and interval is None:
        print("错误: 必须提供 --interval-hours 或 --interval 参数")
        raise typer.Exit(code=1)
    
    if interval_hours is not None and interval is not None:
        print("警告: 同时提供了小时和秒参数,将优先使用小时参数")
    
    asyncio.run(
        run_train_async(
                base_url=base_url, 
                project_name=project_name, 
                interval_hours=interval_hours,
                interval=interval,
                algorithm_type=algorithm_type
            )
        )


def run_batch_train_cli(
    configs: List[str] = typer.Option(..., "--config", "-c", help="训练配置，格式：项目名|间隔时间|算法类型"),
    base_url: str = typer.Option("http://localhost:8999", "--base-url", "-u", help="API服务器的基础URL")
) -> None:
    """
    批量训练的命令行接口
    
    示例用法:
    python requests_train.py batch \
        --config "项目1|300|classic" \
        --config "项目2|600|classic_alter" \
        --base-url "http://localhost:8999"
    """
    asyncio.run(
        run_batch_train_async(
            base_url=base_url,
            config_strings=configs
        )
    )


# 创建Typer应用
app = typer.Typer(help="模型训练工具")

# 注册单个训练命令
app.command("single", help="执行单个模型训练")(run_train_cli)

# 注册批量训练命令
app.command("batch", help="批量执行模型训练")(run_batch_train_cli)

if __name__ == "__main__":
    app()
