
# 7. 常见代码模式和约定

本部分总结了项目中常见的代码模式和开发约定。

## 7.1. 任务调度模式

- **专用调度器**: 系统没有使用单一的全局调度器,而是为不同类型的任务(训练、预测、决策、后台)创建了独立的 `APScheduler` 实例。
    - **优点**: 隔离了不同任务的执行,避免了长耗时任务(如训练)阻塞其他关键任务(如预测)。每个调度器有独立的线程池,可以根据任务特性调整并发数。
    - **实现**: `utils/task_scheduler.py`

## 7.2. 工厂模式 (Factory Pattern)

- **动态任务创建**: `tasks/__init__.py` 中的 `build_tasks` 函数和 `industrytslib` 中的 `get_trainer`, `get_realtime_predictor`, `get_decision_agent` 函数扮演了工厂的角色。
    - **优点**: 调用者(主要是 `routers`)无需关心任务对象的具体实现类。只需提供任务类型(如 `"classic"`, `"sequence"`),工厂函数就会返回对应的实例。这使得添加新的模型或算法类型变得容易,只需在工厂函数中增加一个分支,而无需修改上层调用代码。
    - **示例**:
      ```python
      # in routers/train.py
      trainer = get_trainer(
          project_name=request.project_name, 
          dbconfig=dbconfig, 
          model_type=request.algorithm_type # 'classic' or 'sequence'
      )
      ```

## 7.3. 生命周期管理

- **`@asynccontextmanager`**: `app.py` 使用 `lifespan` 函数来管理应用的启动和关闭事件。
    - **启动时**: 启动后台任务调度器、初始化心跳监控等。
    - **关闭时**: 安全地关闭所有调度器和后台线程,释放资源。
    - **优点**: 这是 FastAPI 推荐的现代方式,取代了旧的 `@app.on_event("startup")` 和 `@app.on_event("shutdown")` 写法,代码更集中、更优雅。

## 7.4. 配置管理

- **TOML + `dotenv`**:
    - **`config/*.toml`**: 用于存储结构化的、非敏感的配置,如数据库连接参数。
    - **`.env`**: 用于存储环境变量,特别是用于控制开关(如 `Background_task=True`)或敏感数据。
    - **加载**: `industrytslib.read_config_toml` 和 `dotenv.load_dotenv` 分别用于加载这两类配置。

## 7.5. 日志记录

- **统一日志工厂**: 使用 `industrytslib.get_logger` 来获取日志记录器实例。
    - **约定**: 通常会为每个模块或功能类型指定一个 `logger_type`(如 `'trainer'`, `'fastapi'`),这可能对应到不同的日志文件或格式,便于分类查看和故障排查。

## 7.6. API 设计

- **名词作为资源路径**: API 路径设计遵循 RESTful 风格,使用名词表示资源(如 `/train`, `/rt`)。
- **动词在请求体或路径末端**: 操作通过 HTTP 方法(`POST`, `GET`)或路径的最后一部分(如 `stop_train`)来表示。
- **统一的响应格式**: 大多数 API 响应都遵循 `{'code': 200, 'message': '...'}` 的格式,便于客户端统一处理。
