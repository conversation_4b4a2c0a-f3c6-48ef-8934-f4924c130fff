// SQLite数据库工具类
// 注意:在浏览器环境中,我们使用IndexedDB来模拟SQLite功能

class LocalDatabase {
  constructor() {
    this.dbName = 'IndustryAI_DB'
    this.version = 1
    this.db = null
  }

  // 初始化数据库
  async init() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version)
      
      request.onerror = () => {
        console.error('数据库打开失败:', request.error)
        reject(request.error)
      }
      
      request.onsuccess = () => {
        this.db = request.result
        console.log('数据库初始化成功')
        resolve(this.db)
      }
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result
        
        // 创建训练记录表
        if (!db.objectStoreNames.contains('trainRecords')) {
          const trainStore = db.createObjectStore('trainRecords', {
            keyPath: 'id',
            autoIncrement: true
          })
          trainStore.createIndex('projectName', 'projectName', { unique: false })
          trainStore.createIndex('timestamp', 'timestamp', { unique: false })
        }
        
        // 创建预测记录表
        if (!db.objectStoreNames.contains('predictRecords')) {
          const predictStore = db.createObjectStore('predictRecords', {
            keyPath: 'id',
            autoIncrement: true
          })
          predictStore.createIndex('projectName', 'projectName', { unique: false })
          predictStore.createIndex('timestamp', 'timestamp', { unique: false })
        }
        
        // 创建决策记录表
        if (!db.objectStoreNames.contains('decisionRecords')) {
          const decisionStore = db.createObjectStore('decisionRecords', {
            keyPath: 'id',
            autoIncrement: true
          })
          decisionStore.createIndex('projectName', 'projectName', { unique: false })
          decisionStore.createIndex('timestamp', 'timestamp', { unique: false })
        }
        
        // 创建系统日志表
        if (!db.objectStoreNames.contains('systemLogs')) {
          const logStore = db.createObjectStore('systemLogs', {
            keyPath: 'id',
            autoIncrement: true
          })
          logStore.createIndex('level', 'level', { unique: false })
          logStore.createIndex('timestamp', 'timestamp', { unique: false })
        }
        
        // 创建算法配置表
        if (!db.objectStoreNames.contains('algorithmConfigs')) {
          const configStore = db.createObjectStore('algorithmConfigs', {
            keyPath: 'id',
            autoIncrement: true
          })
          configStore.createIndex('name', 'name', { unique: true })
          configStore.createIndex('algorithm_type', 'algorithm_type', { unique: false })
          configStore.createIndex('timestamp', 'timestamp', { unique: false })
        }
        
        // 创建样本数据表
        if (!db.objectStoreNames.contains('sampleData')) {
          const sampleStore = db.createObjectStore('sampleData', {
            keyPath: 'id',
            autoIncrement: true
          })
          sampleStore.createIndex('name', 'name', { unique: true })
          sampleStore.createIndex('project_name', 'project_name', { unique: false })
          sampleStore.createIndex('timestamp', 'timestamp', { unique: false })
        }
        
        // 创建训练结果表
        if (!db.objectStoreNames.contains('trainResults')) {
          const resultStore = db.createObjectStore('trainResults', {
            keyPath: 'id',
            autoIncrement: true
          })
          resultStore.createIndex('project_name', 'project_name', { unique: false })
          resultStore.createIndex('config_id', 'config_id', { unique: false })
          resultStore.createIndex('timestamp', 'timestamp', { unique: false })
        }
        
        console.log('数据库结构创建完成')
      }
    })
  }

  // 添加训练记录
  async addTrainRecord(record) {
    const data = {
      ...record,
      timestamp: new Date().toISOString(),
      type: 'train'
    }
    return this._addRecord('trainRecords', data)
  }

  // 添加预测记录
  async addPredictRecord(record) {
    const data = {
      ...record,
      timestamp: new Date().toISOString(),
      type: 'predict'
    }
    return this._addRecord('predictRecords', data)
  }

  // 添加决策记录
  async addDecisionRecord(record) {
    const data = {
      ...record,
      timestamp: new Date().toISOString(),
      type: 'decision'
    }
    return this._addRecord('decisionRecords', data)
  }

  // 添加系统日志
  async addSystemLog(level, message, details = null) {
    const log = {
      level,
      message,
      details,
      timestamp: new Date().toISOString()
    }
    return this._addRecord('systemLogs', log)
  }

  // 获取训练记录
  async getTrainRecords(limit = 100) {
    return this._getRecords('trainRecords', limit)
  }

  // 获取预测记录
  async getPredictRecords(limit = 100) {
    return this._getRecords('predictRecords', limit)
  }

  // 获取决策记录
  async getDecisionRecords(limit = 100) {
    return this._getRecords('decisionRecords', limit)
  }

  // 获取系统日志
  async getSystemLogs(level = null, limit = 100) {
    if (level) {
      return this._getRecordsByIndex('systemLogs', 'level', level, limit)
    }
    return this._getRecords('systemLogs', limit)
  }

  // 根据项目名称获取记录
  async getRecordsByProject(storeName, projectName, limit = 100) {
    return this._getRecordsByIndex(storeName, 'projectName', projectName, limit)
  }

  // 添加算法配置
  async addAlgorithmConfig(config) {
    const data = {
      ...config,
      timestamp: new Date().toISOString()
    }
    return this._addRecord('algorithmConfigs', data)
  }

  // 获取算法配置列表
  async getAlgorithmConfigs(limit = 100) {
    return this._getRecords('algorithmConfigs', limit)
  }

  // 根据算法类型获取配置
  async getConfigsByAlgorithmType(algorithmType, limit = 100) {
    return this._getRecordsByIndex('algorithmConfigs', 'algorithm_type', algorithmType, limit)
  }

  // 根据ID获取算法配置
  async getAlgorithmConfigById(id) {
    return this._getRecordById('algorithmConfigs', id)
  }

  // 更新算法配置
  async updateAlgorithmConfig(id, config) {
    return this._updateRecord('algorithmConfigs', id, config)
  }

  // 删除算法配置
  async deleteAlgorithmConfig(id) {
    return this._deleteRecord('algorithmConfigs', id)
  }

  // 添加样本数据
  async addSampleData(sample) {
    const data = {
      ...sample,
      timestamp: new Date().toISOString()
    }
    return this._addRecord('sampleData', data)
  }

  // 获取样本数据列表
  async getSampleData(limit = 100) {
    return this._getRecords('sampleData', limit)
  }

  // 根据项目名称获取样本数据
  async getSampleDataByProject(projectName, limit = 100) {
    return this._getRecordsByIndex('sampleData', 'project_name', projectName, limit)
  }

  // 删除样本数据
  async deleteSampleData(sampleId) {
    return this._deleteRecord('sampleData', sampleId)
  }

  // 添加训练结果
  async addTrainResult(result) {
    const data = {
      ...result,
      timestamp: new Date().toISOString()
    }
    return this._addRecord('trainResults', data)
  }

  // 获取训练结果
  async getTrainResults(limit = 100) {
    return this._getRecords('trainResults', limit)
  }

  // 根据项目名称获取训练结果
  async getTrainResultsByProject(projectName, limit = 100) {
    return this._getRecordsByIndex('trainResults', 'project_name', projectName, limit)
  }

  // 根据配置ID获取训练结果
  async getTrainResultsByConfigId(configId, limit = 100) {
    return this._getRecordsByIndex('trainResults', 'config_id', configId, limit)
  }

  // 清空指定表的数据
  async clearStore(storeName) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite')
      const store = transaction.objectStore(storeName)
      const request = store.clear()
      
      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  // 私有方法:添加记录
  _addRecord(storeName, data) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite')
      const store = transaction.objectStore(storeName)
      const request = store.add(data)
      
      request.onsuccess = () => {
        console.log(`记录已添加到 ${storeName}:`, data)
        resolve(request.result)
      }
      request.onerror = () => {
        console.error(`添加记录到 ${storeName} 失败:`, request.error)
        reject(request.error)
      }
    })
  }

  // 私有方法:获取记录
  _getRecords(storeName, limit) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readonly')
      const store = transaction.objectStore(storeName)
      const index = store.index('timestamp')
      const request = index.openCursor(null, 'prev') // 按时间倒序
      
      const results = []
      let count = 0
      
      request.onsuccess = (event) => {
        const cursor = event.target.result
        if (cursor && count < limit) {
          results.push(cursor.value)
          count++
          cursor.continue()
        } else {
          resolve(results)
        }
      }
      
      request.onerror = () => reject(request.error)
    })
  }

  // 私有方法:根据索引获取记录
  _getRecordsByIndex(storeName, indexName, value, limit) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readonly')
      const store = transaction.objectStore(storeName)
      const index = store.index(indexName)
      const request = index.openCursor(IDBKeyRange.only(value), 'prev')
      
      const results = []
      let count = 0
      
      request.onsuccess = (event) => {
        const cursor = event.target.result
        if (cursor && count < limit) {
          results.push(cursor.value)
          count++
          cursor.continue()
        } else {
          resolve(results)
        }
      }
      
      request.onerror = () => reject(request.error)
    })
  }

  // 私有方法:根据ID获取记录
  _getRecordById(storeName, id) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readonly')
      const store = transaction.objectStore(storeName)
      const request = store.get(id)
      
      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  // 私有方法:更新记录
  _updateRecord(storeName, id, data) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite')
      const store = transaction.objectStore(storeName)
      
      // 先获取现有记录
      const getRequest = store.get(id)
      getRequest.onsuccess = () => {
        const existingRecord = getRequest.result
        if (existingRecord) {
          const updatedRecord = {
            ...existingRecord,
            ...data,
            id: id, // 保持原ID
            updated_at: new Date().toISOString()
          }
          
          const putRequest = store.put(updatedRecord)
          putRequest.onsuccess = () => resolve(putRequest.result)
          putRequest.onerror = () => reject(putRequest.error)
        } else {
          reject(new Error('记录不存在'))
        }
      }
      getRequest.onerror = () => reject(getRequest.error)
    })
  }

  // 私有方法:删除记录
  _deleteRecord(storeName, id) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite')
      const store = transaction.objectStore(storeName)
      const request = store.delete(id)
      
      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }
}

// 创建全局数据库实例
const db = new LocalDatabase()

// 导出数据库实例和初始化函数
export { db }
export default db