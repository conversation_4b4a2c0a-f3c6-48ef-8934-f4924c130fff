import os
import sys
import time
from industrytslib.core import get_realtime_predictor
from industrytslib.utils.readconfig import read_config_toml


# Add both current directory and parent directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.extend([current_dir, parent_dir])


pred_list = [
    "回转窑强度样本_回转窑强度样本_CNN",
    "水泥A磨强度样本_水泥A磨强度样本_CNN",
    "水泥磨二线强度样本_水泥磨二线强度样本_CNN",
]

def main_sync():
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    config = read_config_toml("config/dbconfig.toml")

    predictor_list = []
    for project_name in pred_list:
        predictor = get_realtime_predictor(project_name, config, "classic_mo")
        print(f"task {project_name} create successfully!")
        predictor_list.append(predictor)

    while True:
        for predictor in predictor_list:
            try:
                predictor.main()
            except Exception as e:
                print(e)
        time.sleep(60)


if __name__ == "__main__":
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    main_sync()
